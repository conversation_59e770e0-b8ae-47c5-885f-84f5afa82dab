#!/usr/bin bash

# Script requires a later version of bash than the default on macOS
# if [ -x /opt/homebrew/bin/bash ]; then
#   exec /opt/homebrew/bin/bash "$0" "$@"
# # elif [ -x /bin/bash ]; then
# #   exec /bin/bash "$0" "$@"
# # else
# #   echo "Error: No suitable Bash interpreter found."
# #   exit 1
# fi

# Check if the environment parameter is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <environment> [-f|--force]"
  echo "Environment must be 'staging' or 'prod'."
  exit 1
fi

ENVIRONMENT=$1

# Set FORCE_UPDATE based on environment variable or command-line argument
if [ "${FORCE_UPDATE}" = "true" ] || [ "$2" = "-f" ] || [ "$2" = "--force" ]; then
  FORCE_UPDATE=true
else
  FORCE_UPDATE=false
fi

if [ "$ENVIRONMENT" == "t" ] || [ "$ENVIRONMENT" == "test" ]; then
  ENVIRONMENT="staging"
fi

if [ "$ENVIRONMENT" == "l" ] || [ "$ENVIRONMENT" == "live" ] || [ "$ENVIRONMENT" == "p" ] || [ "$ENVIRONMENT" == "production" ]; then
  ENVIRONMENT="prod"
fi

# Validate the environment parameter
if [ "$ENVIRONMENT" != "staging" ] && [ "$ENVIRONMENT" != "prod" ]; then
  echo "Invalid environment: $ENVIRONMENT"
  echo "Environment must be 'staging' or 'prod'."
  exit 1
fi

# Define Lambda functions and their corresponding directories
# Later version of bash required: declare -A lambda_functions=(... - doesn't work, declare: -A: invalid option
declare -A lambda_functions=(
  ["mp4_converter"]="conversionHandlers/mp4"
  ["ogv_converter"]="conversionHandlers/ogv"
  ["webm_converter"]="conversionHandlers/webm"
  ["email_sender"]="emailSender"
  ["video_status_updater"]="statusUpdater"
  ["ai_service"]="aiService"
  ["data_service"]="dataService"
  ["video_embeddings_service"]="videoEmbeddingsService"
)

# Get the list of changed files since the last commit
changed_files=$(git diff --name-only HEAD^ HEAD)

# Check if common folder has changed
if echo "$changed_files" | grep -q "src/common/"; then
    common_changed=true
else
    common_changed=false
fi

# Function to check if a Lambda function needs to be updated
needs_update() {
    local dir_name=$1
    echo "$changed_files" | grep -q "src/${dir_name}/" || echo "$changed_files" | grep -q "dist/${dir_name}/"
}

# Function to update a Lambda function
update_function() {
    local function_name=$1
    local dir_name=$2
    local zip_file="./dist/${dir_name}/function.zip"
    local full_function_name="${function_name}--$ENVIRONMENT"

    echo "Updating Lambda function: $full_function_name with ZIP file: $zip_file"

    if output=$(aws lambda update-function-code --function-name "$full_function_name" --zip-file "fileb://$zip_file" 2>&1); then
        echo "Successfully updated $full_function_name"
    else
        echo "$output"
        echo "Failed to update $full_function_name"
        exit 1
    fi
}

# Print the value of FORCE_UPDATE for debugging
echo "FORCE_UPDATE: $FORCE_UPDATE"

# Loop through the Lambda functions and update as needed
for function_name in "${!lambda_functions[@]}"; do
    dir_name="${lambda_functions[$function_name]}"

    echo "Checking $function_name"

    if $FORCE_UPDATE || $common_changed || needs_update "$dir_name"; then
        update_function "$function_name" "$dir_name"
    else
        echo "Skipping $function_name (no changes detected)"
    fi
done
