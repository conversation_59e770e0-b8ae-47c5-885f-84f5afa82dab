{"name": "repd-lambdas", "version": "1.0.0", "description": "", "main": "src/index.js", "scripts": {"build": "cross-env NODE_ENV=dev ./build.sh", "build-prod": "cross-env NODE_ENV=prod ./build.sh", "deploy": "/opt/homebrew/bin/bash ./deploy.sh", "deploy:other": "/bin/bash ./deploy.sh", "test": "jest"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@anthropic-ai/sdk": "^0.24.0", "@sendgrid/client": "^8.1.3", "@sendgrid/mail": "^8.1.3", "@supabase/supabase-js": "^2.44.3", "aws-sdk": "^2.1608.0", "axios": "^1.7.2", "datadog-lambda-js": "^9.115.0", "dd-trace": "^5.25.0", "dotenv": "^16.4.5", "execa": "^9.3.0", "fluent-ffmpeg": "^2.1.3", "get-video-duration": "^4.1.0", "openai": "^4.52.7", "pg": "^8.11.5", "pg-hstore": "^2.3.4", "sequelize": "^6.6.2"}, "devDependencies": {"@babel/preset-env": "^7.24.6", "babel-jest": "^29.7.0", "cross-env": "^7.0.3", "esbuild": "^0.20.2", "jest": "^29.7.0"}}