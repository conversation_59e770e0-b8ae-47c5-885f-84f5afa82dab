#!/bin/bash

set -e

services=(
    "conversionHandlers/mp4"
    "conversionHandlers/ogv"
    "conversionHandlers/webm"
    "statusUpdater"
    "emailSender"
    "aiService"
    "dataService"
    "videoEmbeddingsService"
    "local-test"
)

build_service() {
    local service_name=$1
    local source_file="src/${service_name}/handler.js"
    local output_dir="dist/${service_name}"
    local output_file="${output_dir}/index.js"

    # Ensure source file exists
    if [ ! -f "$source_file" ]; then
        echo "Error: Source file $source_file not found"
        return 1
    fi

    echo "Building ${service_name}..."

    # Remove existing output directory
    rm -rf "$output_dir"

    # Create output directory
    mkdir -p "$output_dir"

    # Build with esbuild
    esbuild "$source_file" \
        --bundle \
        --minify \
        --sourcemap \
        --platform=node \
        --target=es2020 \
        --define:process.env.NODE_ENV=\"$NODE_ENV\" \
        --outfile="$output_file"

    # Create zip file
    (cd "$output_dir" && zip function.zip index.js)

    echo "${service_name} built successfully"
}

# Set NODE_ENV if not already set
NODE_ENV=${NODE_ENV:-dev}

# If no arguments are provided, build all services
if [ $# -eq 0 ]; then
    for service in "${services[@]}"; do
        build_service "$service"
    done
else
    # Build only the specified services
    for service in "$@"; do
        # shellcheck disable=SC2199
        # shellcheck disable=SC2076
        if [[ " ${services[@]} " =~ " ${service} " ]]; then
            build_service "$service"
        else
            echo "Warning: Unknown service '${service}'"
        fi
    done
fi
