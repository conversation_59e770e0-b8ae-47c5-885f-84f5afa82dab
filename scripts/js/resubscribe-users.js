import userModel from '../../src/models/userModel.js';
import { SendGrid } from '../../src/global/mail.js';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Setup proper path resolution for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from the correct path
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

async function testReSubscribeUsers() {
	try {
		const adminUsers = await userModel.findAll({
			where: {
				enabled: true,
				accessLevel: ['admin', 'guest admin', 'manager']
			},
			raw: true
		});

		console.log(`Found ${adminUsers.length} admin users to process`);

		for (const user of adminUsers) {
			const fullUser = await userModel.findByPk(user.id);
			
			if (!fullUser?.email) {
				console.log('Skipping user with no email');
				continue;
			}

			try {
				// Using the correct SendGrid API method
				const response = await SendGrid.request({
					method: 'DELETE',
					url: '/v3/suppression/unsubscribes',
					body: {
						emails: [fullUser.email]
					}
				});
				console.log(`✅ Successfully re-subscribed ${fullUser.email}`, response);
			} catch (error) {
				console.error(`❌ Failed to re-subscribe ${fullUser.email}:`, error.message);
			}
		}
	} catch (error) {
		console.error('Error running test:', error);
	}
}

// Run the test
console.log('Starting resubscribe test...');
testReSubscribeUsers()
	.then(() => console.log('Test completed'))
	.catch(error => console.error('Test failed:', error));
