// Get all available Sendgrid stats, including stats from years ago

import dayjs from 'dayjs';
import { SendGrid } from '../../src/global/mail.js';

import ngpVanEmailsModel from '../../src/models/ngpVanEmailsModel.js';

// create table if not exists public.ngp_van_emails
// (
// 	id bigint default nextval('emails_id_seq'::regclass) not null,
// 	created_at timestamp with time zone not null,
// 	updated_at timestamp with time zone not null,
// 	enabled boolean,
// 	email varchar(255),
// 	opens integer default 0,
// 	cta integer default 0,
// 	unsubscribes integer default 0,
// 	message_id varchar(255),
// 	recipient_name varchar(255),
// 	saved_list_id integer,
// 	donations integer default 0,
// 	last_event_time timestamp with time zone,
// 	saved_list_name varchar(255),
// 	event_ids varchar(255) [] default (ARRAY[]::character varying[])::character varying(255)[],
// 	processed integer default 0,
// 	dropped integer default 0,
// 	delivered integer default 0,
// 	bounce integer default 0,
// 	deferred integer default 0,
// 	spamreport integer default 0,
// 	constraint emails_pkey
// 		primary key (id)
// );

// alter table public.ngp_van_emails owner to u_hjkrtesdf;

async function getDetailedStats(messageId) {
	const request = {
		method: 'GET',
		url: `/v3/messages/${messageId}`,
	};
	
	let response, body;
	try {
		[response, body] = await SendGrid.request(request);
	} catch (error) {
		if (error.response && error.response.statusCode === 429) {
			console.log('Rate limit exceeded. Retrying after 5 seconds...');
			await new Promise(resolve => setTimeout(resolve, 5000));
			return getDetailedStats(messageId);
		}
		throw error;
	}

	console.log('getDetailedStats', body);

	return body;
}

async function sendGridStatsCallback (offset = 0) {
	// Use the message_id to get the stats for each email in bulk

	const emailRecords = await ngpVanEmailsModel.findAll(
		{
			where: {
				subject: null
			},
			offset,
			limit: 100,
			order: [['created_at', 'DESC']],
		}
	);

	console.log('emailRecords', emailRecords[0].dataValues);

	const messageIds = await emailRecords.map(email => email.email);

	const request = {
		method: 'GET',
		url: '/v3/messages',
		qs: {
			query: messageIds.map(id => `to_email="${id}"`).join(' OR '),
			limit: 100 // Max limit
		}
	};
	
	let response, body;
	try {
		[response, body] = await SendGrid.request(request);
	} catch (error) {
		if (error.response && error.response.statusCode === 429) {
			console.log('Rate limit exceeded. Retrying after 5 seconds...');
			await new Promise(resolve => setTimeout(resolve, 5000));
			return sendGridStatsCallback(offset);
		}
		throw error;
	}

	console.log('getSendGridStats', body.messages[0]);

	for (const email of emailRecords) {
		const stat = body.messages.find(stat => 
			stat.to_email.toLowerCase() === email.email.toLowerCase() && 
			(
				dayjs(stat.last_event_time).format('YYYY-MM-DD') === dayjs(email.createdAt).format('YYYY-MM-DD') ||
				dayjs(stat.last_event_time).format('YYYY-MM-DD') >   dayjs(email.updatedAt).format('YYYY-MM-DD')
			)
		);

		if (stat) {
			await new Promise(resolve => setTimeout(resolve, 5000)); // Rate limit: 1 request every 5 seconds
			const detailedStats = await getDetailedStats(stat.msg_id);

			const attributes = {
				opens: stat.opens_count,
				cta: stat.clicks_count,
				lastEventTime: stat.last_event_time,
				longMessageId: stat.msg_id,
				fromEmail: stat.from_email,
				subject: stat.subject,
				status: stat.status,
				unsubscribes: detailedStats.events.reduce((acc, event) => acc + (event.event_name === 'unsubscribe' ? 1 : 0), 0),
				processed: detailedStats.events.reduce((acc, event) => acc + (event.event_name === 'processed' ? 1 : 0), 0),
				dropped: detailedStats.events.reduce((acc, event) => acc + (event.event_name === 'drop' ? 1 : 0), 0),
				delivered: detailedStats.events.reduce((acc, event) => acc + (event.event_name === 'delivered' ? 1 : 0), 0),
				bounce: detailedStats.events.reduce((acc, event) => acc + (event.event_name === 'bounce' ? 1 : 0), 0),
				deferred: detailedStats.events.reduce((acc, event) => acc + (event.event_name === 'deferred' ? 1 : 0), 0),
				spamreport: detailedStats.events.reduce((acc, event) => acc + (event.event_name === 'spamreport' ? 1 : 0), 0),
			};

			console.log('attributes', attributes);

			ngpVanEmailsModel.update(
				attributes,
				{
					where: {
						id: email.id,
					},
				}
			);
		}
	}

	// sendGridStatsCallback(offset + 100);
};

// db.query(`
//   DROP TABLE IF EXISTS stats.sendgrid_data;

//   CREATE TABLE IF NOT EXISTS stats.sendgrid_data (
// 		last_event_time TIMESTAMP,
// 		message_id TEXT,
// 		subject TEXT,
// 		from_email TEXT,
// 		to_email TEXT,
// 		status TEXT,
// 		opens_count INT,
// 		client_id BIGINT DEFAULT NULL
//   );
// `);

// Execute the function
sendGridStatsCallback();

