import { Op } from 'sequelize';
import answerModel from '../../src/models/answerModel.js';

import ffmpeg from 'fluent-ffmpeg';

const getDurations = async () => {
	const answers = await answerModel.findAll({
		where: {
			enabled: true,
			videoDuration: null,
			createdAt: {
				[Op.gte]: new Date('2024-12-01')
			}
		},
	});

	for (const answer of answers) {
		const videoPath1 = answer.videoUrl; //s.mp4;

		if (videoPath1) {
			ffmpeg.ffprobe(videoPath1, (err, metadata) => {
				if (err) {
					console.error(`Error getting duration for ${videoPath1}:`, err);
				} else {
					const videoDuration = metadata.format.duration;

					answer.changed('videoDuration', true);
					answer.update({ videoDuration });

					console.log(`Updated duration for ${videoPath1}: ${videoDuration}`);
				}
			});
		}

		const videoPath2 = answer.videoUrls.mp4;

		// if (videoPath2) {
		// 	ffmpeg.ffprobe(videoPath2, (err, metadata) => {
		// 		if (err) {
		// 			console.error(`Error getting duration for ${videoPath2}:`, err);
		// 		} else {
		// 			const videoDuration = metadata.format.duration;

		// 			answer.changed('videoDuration', true);
		// 			answer.update({ videoDuration });

		// 			console.log(`Updated duration for ${videoPath2}: ${videoDuration}`);
		// 		}
		// 	});
		// }
	}
};

getDurations();