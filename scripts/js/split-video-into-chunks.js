const ffmpeg = require('fluent-ffmpeg');
const fs = require('fs');
const path = require('path');

// Function to split the video into chunks
function splitVideo(inputPath, segmentTime, outputDir, callback) {
	ffmpeg(inputPath)
		.outputOptions('-f', 'segment', '-segment_time', segmentTime, '-reset_timestamps', '1')
		.output(path.join(outputDir, 'segment_%03d.mp4'))
		.on('end', callback)
		.run();
}

// Function to convert each chunk
function convertChunk(inputPath, outputPath, callback) {
	ffmpeg(inputPath)
		.outputOptions('-c:v', 'libx264', '-crf', '23', '-c:a', 'aac', '-strict', 'experimental')
		.output(outputPath)
		.on('end', callback)
		.run();
}

// Function to concatenate the chunks
function concatenateChunks(chunkFiles, outputPath, callback) {
	const fileList = chunkFiles.map(file => `file '${file}'`).join('\n');
	fs.writeFileSync('filelist.txt', fileList);

	ffmpeg()
		.input('filelist.txt')
		.inputOptions('-f', 'concat', '-safe', '0')
		.outputOptions('-c', 'copy')
		.output(outputPath)
		.on('end', callback)
		.run();
}

// Example usage
const inputPath = 'input.mp4';
const outputDir = 'chunks';
const segmentTime = '600'; // 10 minutes
const outputPath = 'output.mp4';

if (!fs.existsSync(outputDir)){
	fs.mkdirSync(outputDir);
}

splitVideo(inputPath, segmentTime, outputDir, () => {
	const chunkFiles = fs.readdirSync(outputDir).map(file => path.join(outputDir, file));
	let convertedChunks = [];

	chunkFiles.forEach((chunk, index) => {
		const outputChunk = chunk.replace('.mp4', '_converted.mp4');
		convertChunk(chunk, outputChunk, () => {
			convertedChunks.push(outputChunk);
			if (convertedChunks.length === chunkFiles.length) {
				concatenateChunks(convertedChunks, outputPath, () => {
					console.log('Conversion and concatenation complete!');
				});
			}
		});
	});
});