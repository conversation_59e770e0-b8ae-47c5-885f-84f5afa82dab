import { Op } from 'sequelize';

import { createCategorySummary } from '../../src/helpers/ai.js';

import '../../src/models/index.js';

import questionModel from '../../src/models/questionModel.js';

// Get questions without category summary
const questions = await questionModel.findAll({
  where: { 
    categorySummary: null
  }
});

console.log(`Found ${questions.length} questions without category summary`);

for (const question of questions) {
  const categorySummary = await createCategorySummary(question.text);

  console.log(`${question.text} => ${categorySummary}`);

  question.changed('categorySummary', true);
  await question.update({ categorySummary });
}

console.log('Category summary generation completed');
