import { Op, or } from 'sequelize';

import answerModel from '../../src/models/answerModel.js';

import { startTranscriptionJob } from '../../src/global/transcription.js';

// Get transcription for an answer, save it and translate it, every minute
async function start () {
	const answers = await answerModel.findAll({
		where: {
			transcription: null,
			transcriptionProcessing: {
				[Op.eq]: null
			},
			// clientId: { [Op.in]: [15] }, // <PERSON> for testing
			// videoUrl: { [Op.ne]: null },
			// createdAt: { [Op.lte]: new Date() }
		},
		order: [['createdAt', 'DESC']],
	});

	console.log('Transcription jobs found:', answers.length);

	for (const answer of answers) {
		startTranscriptionJob(answer);
	}

	console.log('Transcription jobs processed.');
}

start();