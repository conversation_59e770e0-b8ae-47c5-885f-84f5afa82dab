import { Op } from 'sequelize';
import { db } from '../../src/global/database.js';
import { createSentiment, categorize } from '../../src/helpers/ai.js';

import '../../src/models/index.js';

import questionModel from '../../src/models/questionModel.js';
import aiQueryModel from '../../src/models/aiQueryModel.js';

// Clean up duplicate AI queries first
console.log('Cleaning up duplicate AI queries...');
await db.query(`
  DELETE FROM ai_queries
  WHERE id IN (
      SELECT id
      FROM (
          SELECT id,
                 ROW_NUMBER() OVER (PARTITION BY query, created_at ORDER BY id) as row_num
          FROM ai_queries
      ) as duplicates
      WHERE duplicates.row_num > 1
  );

  DELETE FROM ai_queries
  WHERE id IN (
    SELECT id
      FROM (
          SELECT id,
                 ROW_NUMBER() OVER (PARTITION BY query, original::text ORDER BY id) as row_num
          FROM ai_queries
          WHERE created_at > '2025-07-28'
      ) as duplicates
      WHERE duplicates.row_num > 1
  );
`);

// Remove AI queries with null query values
console.log('Removing AI queries with null query values...');
await db.query(`DELETE FROM ai_queries WHERE query is null;`);

// Get questions without sentiment
const questions = await questionModel.findAll({
  where: { sentiment: null, enabled: true }
});
const aiSentimentQueries = await aiQueryModel.findAll( {
  where: { sentiment: null },
  enabled: true
} );
const aiCategoryQueries = await aiQueryModel.findAll( {
  where: { category: null },
  enabled: true
} );

for ( const question of questions ) {
  const sentiment = await createSentiment( question.text );

  console.log(`${question.text} ${sentiment}`);

  question.changed( 'sentiment', true );
  await question.update( { sentiment: sentiment.replace(/[^\d]/g, '') } );
}

for (const aiQuery of aiSentimentQueries) {
  const sentiment = await createSentiment(aiQuery.query);

  console.log(`${aiQuery.query} ${sentiment}`);

  aiQuery.sentiment = sentiment;
  aiQuery.changed('sentiment', true);

  await aiQuery.update({ sentiment: sentiment.replace(/[^\d]/g, '') });
}

for (const aiQuery of aiCategoryQueries) {
  const category = await categorize(aiQuery.query);

  console.log(`${aiQuery.query} ${category}`);

  aiQuery.category = category;
  aiQuery.changed('category', true);

  await aiQuery.update({ category });
}

console.log('done');
