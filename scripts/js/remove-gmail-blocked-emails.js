// Script to remove emails from SendGrid blocklist based on configurable filters
import { SendGrid } from '../../src/global/mail.js';

// Define filters - easy to add new ones here
const filters = [
	{
		name: 'Gmail low reputation',
		match: (block) => block.reason && block.reason.includes('G<PERSON> has detected that this message is likely suspicious due to the very low reputation of the sending domain')
	},
	{
		name: 'Maple Valley domain',
		match: (block) => block.email && block.email.toLowerCase().includes('@maplevalley')
	},
	{
		name: 'Government domain (.gov)',
		match: (block) => block.email && /.gov$/i.test(block.email)
	},
	// Add more filters here as needed:
	// {
	//   name: 'Another filter',
	//   match: (block) => block.someProperty && block.someProperty.includes('some text')
	// },
];

async function processBlockedEmails() {
	try {
		console.log('Starting to process blocked emails...');
    
		let processedCount = 0;
		let matchCounts = {};
		filters.forEach(filter => { matchCounts[filter.name] = 0; });
    
		let offset = 0;
		const limit = 100; // Process in smaller batches
		let hasMoreRecords = true;
    
		// Process in batches until we've gone through all records
		while (hasMoreRecords) {
			// Get blocked emails from SendGrid with pagination
			const request = {
				method: 'GET',
				url: '/v3/suppression/blocks',
				qs: {
					limit: limit,
					offset: offset
				}
			};
      
			console.log(`Fetching batch with offset ${offset}...`);
			let [response, body] = await SendGrid.request(request);
      
			if (!Array.isArray(body)) {
				console.error('Unexpected response format:', body);
				return;
			}
      
			// If we got fewer records than requested, we've reached the end
			if (body.length < limit) {
				hasMoreRecords = false;
			}
      
			processedCount += body.length;
			console.log(`Processing ${body.length} blocked emails in this batch`);
      
			// Apply all filters and collect matches
			let allBlocksToRemove = new Set();
      
			// Process each filter
			for (const filter of filters) {
				const matches = body.filter(filter.match);
				matchCounts[filter.name] += matches.length;
				console.log(`Found ${matches.length} matches for "${filter.name}" in this batch`);
        
				// Add to our set of blocks to remove
				matches.forEach(match => allBlocksToRemove.add(match));
			}
      
			// Convert back to array
			const blocksToRemove = Array.from(allBlocksToRemove);
      
			// Process each blocked email
			for (const block of blocksToRemove) {
				try {
					// Delete from blocks list
					const deleteRequest = {
						method: 'DELETE',
						url: '/v3/suppression/blocks',
						body: {
							emails: [block.email]
						}
					};
          
					await SendGrid.request(deleteRequest);
					console.log(`✅ Successfully removed ${block.email} from blocklist`);
				} catch (error) {
					console.error(`❌ Failed to remove ${block.email} from blocklist:`, error.message);
				}
			}
      
			// Increment offset for next batch
			offset += limit;
      
			// Add a small delay to avoid rate limiting
			await new Promise(resolve => setTimeout(resolve, 1000));
		}
    
		console.log('Completed processing blocked emails.');
		console.log(`Processed ${processedCount} total records.`);
    
		// Report matches for each filter
		for (const [filterName, count] of Object.entries(matchCounts)) {
			console.log(`Found ${count} matches for "${filterName}"`);
		}
	} catch (error) {
		console.error('Error running script:', error);
	}
}

// Export the function for use in scheduled tasks
export { processBlockedEmails };

// Execute the function only if this script is run directly
// if (import.meta.url === `file://${process.argv[1]}`) {
processBlockedEmails();
// }
