const crypto = require('crypto');
import envModule from '../../../global/environment.js';
const env = envModule.default;
const key = env.getProperty('db.encryption_key'); // Replace with your actual encryption key

if (key.length !== 64) {
	throw new Error('Invalid encryption key length. Expected 32 bytes (64 hex characters).');
}

const encrypt = (text) => {
	if (!text) return text;
	const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(key, 'hex'), Buffer.alloc(16, 0));
	let encrypted = cipher.update(text, 'utf8', 'hex');
	encrypted += cipher.final('hex');
	return encrypted;
};

const decrypt = (text) => {
	if (!text) return text;
	const decipher = crypto.createDecipheriv('aes-256-cbc', <PERSON>uff<PERSON>.from(key, 'hex'), Buffer.alloc(16, 0));
	let decrypted = decipher.update(text, 'hex', 'utf8');
	decrypted += decipher.final('utf8');
	return decrypted;
};
