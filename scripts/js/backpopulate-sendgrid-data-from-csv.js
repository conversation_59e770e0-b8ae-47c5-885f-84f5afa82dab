import fs from 'fs';
import csv from 'csv-parser';
import dayjs from 'dayjs';
import ngpVanEmailsModel from '../../src/models/ngpVanEmailsModel.js';

import { Op, fn, col } from 'sequelize';

async function readCSV(filePath) {
	return new Promise((resolve, reject) => {
		const results = [];
		fs.createReadStream(filePath)
			.pipe(csv())
			.on('data', (data) => results.push(data))
			.on('end', () => resolve(results))
			.on('error', (error) => reject(error));
	});
}

const emailRecords = await ngpVanEmailsModel.findAll();

async function updateEmailStatsFromCSV(groupedData) {
	const groupedDataEntries = Object.entries(groupedData);
  //   {
	// 	where: {
  //     [Op.and]: [
  //       fn('LOWER', col('email')), { [Op.in]: groupedDataEntries.map(([key, row]) => row.email.toLowerCase()) }
  //     ],
	// 		subject: null
	// 	}
	// });

	// console.log('emailRecords', emailRecords.length, emailRecords[0].dataValues);

	for (const [key, row] of groupedDataEntries) {
    const recv_message_id = key.split('||')[0];
		const emailRecord = await emailRecords.find(email => email.dataValues.email.toLowerCase() === row.email.toLowerCase() && email.dataValues.messageId === recv_message_id);

		// console.log('emailRecord', 
    //   emailRecord,
    //   emailRecords.find(email => email.email.toLowerCase() === row.email.toLowerCase()),
    //   emailRecords.find(email => email.messageId === recv_message_id)
    // );

    const status = row.delivered > 0 ? 'delivered' : row.bounce > 0 ? 'bounced' : row.dropped > 0 ? 'dropped' : 'unknown';

    const attributes = {
      messageId: recv_message_id,
      email: row.email.toLowerCase(),
      opens: row.opens,
      cta: row.cta,
      lastEventTime: row.processed,
      longMessageId: row.message_id,
      fromEmail: row.from,
      subject: row.subject,
      status,
      unsubscribes: row.unsubscribes,
      processed: row.processes,
      dropped: row.dropped,
      delivered: row.delivered,
      bounce: row.bounce,
      deferred: row.deferred,
      spamreport: row.spamreport,
      enabled: true,
    };

		if (emailRecord) {
			// console.log('Updating email record:', emailRecord, attributes);
			console.log('Updating email record:', row.email, recv_message_id);

			await ngpVanEmailsModel.update(attributes, {
				where: {
					id: emailRecord.id,
					email: row.email.toLowerCase()
				},
			});
		}
    else {
			console.log('Creating email record:', row.email, recv_message_id);

      await ngpVanEmailsModel.create(attributes);
    }
	}
}

async function complileStats(filePath) {
	const csvData = await readCSV(filePath);

	let groupedData = {};

	console.log('csvData', csvData.length);

	for (const index in csvData) {
		const row = csvData[index];
    const key = row.recv_message_id + '||' + row.email;

		if (!groupedData[key]) {
			groupedData[key] = {
				opens: 0,
				cta: 0,
				unsubscribes: 0,
				processes: 0,
				dropped: 0,
				delivered: 0,
				bounce: 0,
				deferred: 0,
				spamreport: 0,
				...row
			};
		}

		groupedData[key].opens        += row.event === 'open'        ? 1 : 0;
		groupedData[key].cta          += row.event === 'click'       ? 1 : 0;
		groupedData[key].unsubscribes += row.event === 'unsubscribe' ? 1 : 0;
		groupedData[key].processes    += row.event === 'processed'   ? 1 : 0;
		groupedData[key].dropped      += row.event === 'dropped'     ? 1 : 0;
		groupedData[key].delivered    += row.event === 'delivered'   ? 1 : 0;
		groupedData[key].bounce       += row.event === 'bounce'      ? 1 : 0;
		groupedData[key].deferred     += row.event === 'deferred'    ? 1 : 0;
		groupedData[key].spamreport   += row.event === 'spamreport'  ? 1 : 0;

    // console.log('groupedData', index);

		// if (index <= 0)
		// 	console.log('groupedData', groupedData);

		if (index >= csvData.length - 2 && index <= csvData.length) {
    // if (index >= csvData.lenght - 1) {
        // console.log('groupedData', Object.entries(groupedData).length);

			return updateEmailStatsFromCSV(groupedData);
		}
	}
}

// Execute the function with the path to your CSV file
await complileStats('/Users/<USER>/Downloads/SendGrid Data.csv');
