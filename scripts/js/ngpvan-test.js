import queryString from 'query-string';
import validatePhoneNumber from 'validate-phone-number-node-js';
import ngpVanPeopleModel from '../../models/ngpVanPeopleModel.js';
import clientModel from '../../models/clientModel.js';
import userModel from '../../models/userModel.js';
import constantUtil from '../../utils/constantUtil.js';
import serviceUtil from '../../utils/serviceUtil.js';
import { createCanvassResponse } from '../../services/ngpVanCanvasResponseService.js';
import {
	getNgpVanAuthObject,
	getVotebuilderAuthObject,
	getNgpVanStatsNote,
	getNgpVanActivistCodes,
	http,
	url
} from '../../services/ngpVanService.js';

const logger = {
	debug: (...args) => {
		return;
		if ( [ 'production' ].includes( process.env.NODE_ENV ) ) return;
		console.log(args);
	},
	warn: (...args) => {
		return;
		if ( [ 'production' ].includes( process.env.NODE_ENV ) ) return;
		console.warn(args);
	}
};

const getNgpVanCodes = async ( ngp_auth ) => {
	const options = {
		auth: ngp_auth,
		headers: { 'Content-type' : 'application/json' },
		uri: `${ url }/codes?codeType=Tag`,
	};

	if ( ngp_auth.user !== null && ngp_auth.password !== null )
		return new Promise((resolve, reject) => {
			http.get( options, async ( error, httpResponse, body ) => {
				const items = JSON.parse( body || '{}' ).items || [];
				if ( error ) {
					resolve([]);
				} else if ( httpResponse.statusCode > 302 || !items ) {
					resolve([]);
				} else {
					resolve(items);
				}

			} );
		});
};

const addNgpVanCodes = async ( ngp_auth, name ) => {
	const supportedEntities = [
		{
			'name': 'Contacts',
			'isSearchable': true,
			'isApplicable': true
		},
		{
			'name': 'SurveyQuestions',
			'isSearchable': true,
			'isApplicable': true
		},
		{
			'name': 'ActivistCodes',
			'isSearchable': true,
			'isApplicable': true
		}
	];

	const options = {
		auth: ngp_auth,
		headers: { 'Content-type' : 'application/json' },
		uri: `${ url }/codes`,
		body: JSON.stringify( {
			name,
			supportedEntities,
			codeType: 'Tag'
		} )
	};

	if ( ngp_auth.user !== null && ngp_auth.password !== null )
		await http.post( options, async ( error, httpResponse, body ) => {
			const data = JSON.parse( body || '{}' );

			if ( error ) {
				return false;
			} else if ( httpResponse.statusCode > 302 || !body ) {
				return false;
			} else {
				return true;
			}
		} );
};


const getNgpVanPeopleNotes = async ( ngp_auth, vanId ) => {
	const options = {
		auth: ngp_auth,
		headers: { 'Content-type' : 'application/json' },
		uri: `${ url }/people/${ vanId }/notes`,
	};

	if ( ngp_auth.user !== null && ngp_auth.password !== null )
		return new Promise((resolve, reject) => {
			http.get( options, async ( error, httpResponse, body ) => {
				const data = JSON.parse( body || '{}' );
				if ( error ) {
					resolve([]);
				} else if ( httpResponse.statusCode > 302 ) {
					resolve([]);
				} else {
					resolve(data);
				}
			} );
		});
};


const updateNgpVanPeopleNotes = async ( ngp_auth, vanId, noteId, stats ) => {
	const options = {
		auth: ngp_auth,
		headers: { 'Content-type' : 'application/json' },
		uri: `${ url }/people/${ vanId }/notes/${ noteId }`,
		body: JSON.stringify( {
			text: stats,
			isViewRestricted: false
		} )
	};

	if ( ngp_auth.user !== null && ngp_auth.password !== null )
		return new Promise((resolve, reject) => {
			http.put( options, async ( error, httpResponse, body ) => {
				const data = JSON.parse( body || '{}' );

				console.log('updateNgpVanPeopleNotes', body);

				if ( error ) {
					resolve([]);
				} else if ( httpResponse.statusCode > 302 || !data ) {
					resolve([]);
				} else {
					resolve(data);
				}
			} );
		});
};


const addNgpVanPeopleNotes = async ( ngp_auth, vanId, stats, category, contactHistory ) => {
	const options = {
		auth: ngp_auth,
		headers: { 'Content-type' : 'application/json' },
		uri: `${ url }/people/${ vanId }/notes`,
		body: JSON.stringify( {
			text: stats,
			isViewRestricted: false,
			category:  category,
			contactHistory: contactHistory,
		} )
	};

	if ( ngp_auth.user !== null && ngp_auth.password !== null )
		await http.post( options, async ( error, httpResponse, body ) => {
			const data = JSON.parse( body || '{}' );

			if ( error ) {
				return false;
			} else if ( httpResponse.statusCode > 302 || !body ) {
				return false;
			} else {
				return true;
			}
		} );
};


const addNgpVanPeopleCodes = async ( ngp_auth, vanId, codeId ) => {
	const options = {
		auth: ngp_auth,
		headers: { 'Content-type' : 'application/json' },
		uri: `${ url }/people/${ vanId }/codes`,
		body: JSON.stringify( {
			codeId
		} )
	};

	if ( ngp_auth.user !== null && ngp_auth.password !== null )
		await http.post( options, async ( error, httpResponse, body ) => {
			const data = JSON.parse( body || '{}' );
			if ( error ) {
				return false;
			} else if ( httpResponse.statusCode > 302 || !body) {
				return false;
			} else {
				return true;
			}
		} );
};

const findNgpVanPerson = async (ngp_auth, payload) => {
	const options = {
		auth: ngp_auth,
		headers: { 'Content-type' : 'application/json' },
		uri: `${ url }/people/find`,
		body: JSON.stringify(payload)
	};

	if ( ngp_auth.user !== null && ngp_auth.password !== null )
		return new Promise((resolve, reject) => {
			http.post( options, async ( error, httpResponse, body ) => {
				const person = JSON.parse( body || '{}' );
				if ( error ) {
					resolve(null);
				} else if ( httpResponse.statusCode > 302 || !person ) {
					resolve(null);
				} else {
					resolve(person);
				}
			} );
		});
};

const getNgpVanPerson = async (ngp_auth, payload) => {
	const queryParams = queryString.stringify({firstName: payload.firstName, lastName: payload.lastName, zipOrPostalCode: payload.zipOrPostalCode, email: payload.email});
	const options = {
		auth: ngp_auth,
		headers: { 'Content-type' : 'application/json' },
		uri: `${ url }/people?${queryParams}`,
	};

	if ( ngp_auth.user !== null && ngp_auth.password !== null )
		return new Promise((resolve, reject) => {
			http.get( options, async ( error, httpResponse, body ) => {
				const items = JSON.parse( body || '{}' ).items || [];
				if ( error ) {
					resolve([]);
				} else if ( httpResponse.statusCode > 302 ) {
					resolve([]);
				} else {
					resolve(items);
				}
			} );
		});
};

const createNgpVanPerson = async (ngp_auth, payload) => {
	const options = {
		auth: ngp_auth,
		headers: { 'Content-type' : 'application/json' },
		uri: `${ url }/people/findOrCreate`,
		body: JSON.stringify( payload )
	};

	if ( ngp_auth.user !== null && ngp_auth.password !== null ) {
		return new Promise((resolve, reject) => {
			http.post( options, ( error, httpResponse, body ) => {
				const person = JSON.parse( body || '{}' );

				if ( error ) {
					resolve(null);
				} else if ( httpResponse.statusCode > 302 || !person ) {
					resolve(null);
				} else {
					resolve(person);
				}
			} );
		});
	}
};

const searchUser = async (ngp_auth, person, dbmode) => {
	let userExist = false;
	let foundUsers = [];

	foundUsers = await getNgpVanPerson(ngp_auth, {
		firstName: person.firstName,
		lastName: person.lastName,
		zipOrPostalCode: person.zip
	});
	// logger.debug(`@@@ repd(${dbmode}) @@@ - ${foundUsers.length} users {{ ${person.firstName} (zipcode-${person.zip}) exist in ngpvan people list }}`);

	if ( foundUsers.length > 0 )
		userExist = true;
	if ( foundUsers.length != 1) {
		foundUsers = await getNgpVanPerson(ngp_auth, {
			firstName: person.firstName,
			lastName: person.lastName,
			email: person.email,
		});
	}

	if ( foundUsers.length > 0 )
		userExist = true;
	else
		userExist = false;

	return {
		foundUsers,
		userExist
	};
};

const getAllClients = async () => {
	const clients = await clientModel.findAll({
		where: { id: 155, enabled: true }
	});
	return clients;
};

const getAllNgpVanPeople = async ( clientId ) => {
	const people = await ngpVanPeopleModel.findAll( { where: { enabled: true, clientId: clientId } });
	return people;
};

const transformToPeopleEntity = ( user ) => {
	const fields = [
		{ name: 'firstName', value: user.firstName },
		{ name: 'lastName', value: user.lastName },
		{ name: 'email', value: user.email },
		{ name: 'vanId', value: user.vanId },
		{ name: 'zip', value: user.zip },
		{ name: 'phone', value: user.phone },
		{ name: 'userId', value: user.id },
		{ name: 'clientId', value: user.clientId },
		{ name: 'enabled', value: true }
	];
	return fields;
};

const syncMyCampaignUsersToPeople = async ( ngp_auth, client, people ) => {
	// logger.debug(`@@@ repd(${constantUtil.ngpVanDbmodes[1]}) @@@ - {{ syncing repd users to ngpvan and save to ngpvan people for this client ${client.name}}}`);
	const users = await userModel.findAll( { where: { enabled: true, clientId: client.id } });

	const intersections = users.map(item => item.email).filter(element => people.map(item => item.email).includes(element));

	for ( const user of users ) {
		if (!user.firstName || !user.lastName || !user.email || intersections.includes(user.email)) {
			continue;
		}

		const { foundUsers } = await searchUser(ngp_auth, user, constantUtil.ngpVanDbmodes[1]);

		if ( foundUsers.length == 1 ) {
			// logger.debug(`@@@ repd(${constantUtil.ngpVanDbmodes[1]}) @@@ - only one user {{ ${user.firstName} ${user.lastName} exist in ngpvan people list }}`);
			await addNotesAndCodesToPerson(ngp_auth, client, foundUsers[0].vanId, constantUtil.ngpVanDbmodes[1], user.id);
		}
		
		const newVanUser = await findOrCreateNgpVanUser(ngp_auth, client, user);

		if ( newVanUser) {
			user.vanId = newVanUser.vanId;

			const entityToCreate = {};

			serviceUtil.updateProperties( entityToCreate, transformToPeopleEntity(user) );

			entityToCreate.salutation = newVanUser.salutation;
			entityToCreate.envelopeName = newVanUser.envelopeName;
			entityToCreate.title = newVanUser.title;
			entityToCreate.dateOfBirth = newVanUser.dateOfBirth;
			entityToCreate.party = newVanUser.party;
			entityToCreate.sex = newVanUser.sex;
			entityToCreate.suffix = newVanUser.suffix;
			entityToCreate.nickname = newVanUser.nickname;
			entityToCreate.website = newVanUser.website;
			entityToCreate.employer = newVanUser.employer;
			entityToCreate.occupation = newVanUser.occupation;
			entityToCreate.emails = JSON.stringify(newVanUser.emails);
			entityToCreate.phones = JSON.stringify(newVanUser.phones);

			// logger.debug(`@@@ repd(${constantUtil.ngpVanDbmodes[1]}) @@@ - {{ entityToCreate - ${JSON.stringify(entityToCreate)} }}`);
			await ngpVanPeopleModel.create(entityToCreate);
		}
		// logger.debug(`@@@ repd(${constantUtil.ngpVanDbmodes[1]}) @@@ - syncUsersToNgpVanPeople is done for this client ${client.name}`);
	}
};

const syncMyVotersUsersToPeople = async ( ngp_auth, client, people ) => {
	// logger.debug(`@@@ repd(${constantUtil.ngpVanDbmodes[0]}) @@@ - {{ syncing repd users to ngpvan and save to ngpvan people for this client ${client.name}}}`);
	const users = await userModel.findAll( { where: { enabled: true, clientId: client.id } });

	const intersections = users.map(item => item.email).filter(element => people.map(item => item.email).includes(element));

	for ( const user of users ) {
		if (!user.firstName || !user.lastName || !user.email || intersections.includes(user.email)) {
			continue;
		}

		const { foundUsers, userExist } = await searchUser(ngp_auth, user, constantUtil.ngpVanDbmodes[1]);

		if ( foundUsers.length == 1 ) {
			// logger.debug(`@@@ repd(${constantUtil.ngpVanDbmodes[0]}) @@@ - only one user {{ ${user.firstName} ${user.lastName} exist in ngpvan people list }}`);
			await addNotesAndCodesToPerson(ngp_auth, client, foundUsers[0].vanId, constantUtil.ngpVanDbmodes[1], user.id);
		}
		
		if ( userExist && foundUsers[0] ) {
			user.vanId = foundUsers[0].vanId;

			const entityToCreate = {};

			serviceUtil.updateProperties( entityToCreate, transformToPeopleEntity(user) );

			entityToCreate.salutation = foundUsers[0].salutation;
			entityToCreate.envelopeName = foundUsers[0].envelopeName;
			entityToCreate.title = foundUsers[0].title;
			entityToCreate.dateOfBirth = foundUsers[0].dateOfBirth;
			entityToCreate.party = foundUsers[0].party;
			entityToCreate.sex = foundUsers[0].sex;
			entityToCreate.suffix = foundUsers[0].suffix;
			entityToCreate.nickname = foundUsers[0].nickname;
			entityToCreate.website = foundUsers[0].website;
			entityToCreate.employer = foundUsers[0].employer;
			entityToCreate.occupation = foundUsers[0].occupation;
			entityToCreate.emails = JSON.stringify(foundUsers[0].emails);
			entityToCreate.phones = JSON.stringify(foundUsers[0].phones);

			// logger.debug(`@@@ repd(${constantUtil.ngpVanDbmodes[0]}) @@@ - {{ entityToCreate - ${JSON.stringify(entityToCreate)} }}`);

			await ngpVanPeopleModel.findOrCreate( {
				where: { firstName: entityToCreate.firstName, lastName: entityToCreate.lastName, vanId: entityToCreate.vanId },
				defaults: entityToCreate
			} );
		}
	}
};

const checkValidNgpAuth = async ( ngp_auth ) => {
	const options = {
		auth: ngp_auth,
		headers: { 'Content-type' : 'application/json' },
		uri: `${ url }/apikeyProfiles`
	};

	if ( ngp_auth.user !== null && ngp_auth.password !== null )
		return new Promise((resolve, reject) => {
			http.get( options, ( error, httpResponse, body ) => {
				const data = JSON.parse( body || '{}' );

				if ( error ) {
					resolve(false);
				} else if ( httpResponse.statusCode > 302 || !data ) {
					resolve(false);
				} else {
					resolve(true);
				}
			} );
		});
};

const addNotesAndCodesToPerson = async (auth, client, vanId, dbmode, userId) => {
	if (!userId) return;

	const activistCodeNames = await createCanvassResponse(auth, client, vanId, dbmode, userId);
	const notes = await getNgpVanPeopleNotes(auth, vanId);
	// logger.debug(`@@@ repd(${dbmode}) @@@ - {{ succeeded to get all notes ${JSON.stringify(notes)}}}}`);

	const stats = await getNgpVanStatsNote(client.id, userId, activistCodeNames);

	if ( (notes && notes.items && notes.items.length > 0)) {
		const item = notes.items[0];
		// logger.debug(`@@@ repd(${dbmode}) @@@ - update stats notes ${item.noteId} as ${stats.yaml} to ngp van user ${vanId}`);
		await updateNgpVanPeopleNotes(auth, vanId, item.noteId, stats.yaml);
	} else {
		// logger.debug(`@@@ repd(${dbmode}) @@@ - add new stats notes ${stats.yaml} to ngp van user ${vanId}`);
		await addNgpVanPeopleNotes(auth, vanId, stats.yaml, null, {
			inputTypeId: '11',
			dateCanvassed: new Date().toISOString()
		});
	}
	
	const codes = await getNgpVanCodes(auth);
	logger.debug(`@@@ repd(${dbmode}) @@@ - {{ succeeded to get all codes ${JSON.stringify(codes)}}}}`);
	
	const activistCodes = await getNgpVanActivistCodes(auth);

	for (activistCode of activistCodes) {
		if ( !codes.map(item => item.name).includes(activistCode.name) ) {
			logger.debug(`@@@ repd(${dbmode}) @@@ - add tag ${activistCode.name}`);
			const codeId = await addNgpVanCodes(auth, activistCode.name);
			if ( codeId ) {
				logger.debug(`@@@ repd(${dbmode}) @@@ - add tag ${activistCode.name} to ngp van user ${vanId}`);
				await addNgpVanPeopleCodes(auth, vanId, codeId);
			}
		}
	}

	for ( let code of codes ) {
		if ( ( code.name == constantUtil.defaultCodeFields[1] && stats.json.volunteerClicked) ||
			( code.name == constantUtil.defaultCodeFields[2] && stats.json.donationClicked)
		) {
			// logger.debug(`@@@ repd(${dbmode}) @@@ - add tag ${code.name} to ngp van user ${vanId}`);
			await addNgpVanPeopleCodes(auth, vanId, code.codeId);
		} else {
			if ( code.name.toLowerCase() === constantUtil.defaultCodeFields[0].toLowerCase()) {
				// logger.debug(`@@@ repd(${dbmode}) @@@ - add tag ${code.name} to ngp van user ${vanId}`);
				await addNgpVanPeopleCodes(auth, vanId, code.codeId);
			}
		}
	}
};

const findOrCreateNgpVanUser = async (ngp_auth, client, person) => {
	const { vanId, firstName,  lastName, salutation, title, dateOfBirth, envelopeName, party, sex, suffix, nickname, website, employer, occupation, email, phone, zip } = person;
	const payload = {
		vanId, 
		firstName,  
		lastName, 
		salutation, 
		title, 
		dateOfBirth, 
		envelopeName, 
		party, 
		sex, 
		suffix, 
		nickname, 
		website, 
		employer,
		occupation,
		emails: [ { email: email} ], 
		phones: validatePhoneNumber.validate(phone)? [ { phoneNumber: phone } ] : [],
		addresses: [ { zip: zip} ], 
	};

	const newVanUser = await createNgpVanPerson(ngp_auth, payload);

	// logger.debug(`@@@ repd(${constantUtil.ngpVanDbmodes[1]}) @@@ - {{ newVanUser payload ${JSON.stringify(payload)}}}`);

	if (newVanUser) {
		logger.debug(`@@@ repd(${constantUtil.ngpVanDbmodes[1]}) @@@ - {{ succeeded to create ngpvan user ${firstName} ${lastName} and vanId is ${newVanUser.vanId}}}`);
		await addNotesAndCodesToPerson(ngp_auth, client, newVanUser.vanId,  constantUtil.ngpVanDbmodes[1], person.userId);
	} else {
		logger.debug(`@@@ repd(${constantUtil.ngpVanDbmodes[1]}) @@@ - {{ failed to create user ${firstName} ${lastName} }}`);
	}
	return newVanUser;
};

const syncPeople = async () => {
	const clients = await getAllClients();

	console.debug(`@@@repd@@@ - {{ total ${clients.length} clients found }}`);

	for ( const client of clients ) {
		if ( client.id != 155 ) continue;

		const ngp_auth = await getNgpVanAuthObject( client.id );
		const ngp_auth_votebuilder = await getVotebuilderAuthObject( client.id );
		const people = await getAllNgpVanPeople( client.id );
		const checkResult = await checkValidNgpAuth( ngp_auth );

		if ( !checkResult ) {
			console.debug(`@@@repd@@@ - {{ ${ngp_auth.user}: ${ngp_auth.password} are invalid ngpvan credentials }}`);
			continue;
		}

		await syncMyVotersUsersToPeople(ngp_auth_votebuilder, client, people);

		if ( people.length === 0 ) continue;

		for ( const person of people ) {
			const {foundUsers} = await searchUser(ngp_auth_votebuilder, person, constantUtil.ngpVanDbmodes[0]);

			if ( foundUsers.length === 1 ) {
				console.debug(`   @@@repd(${constantUtil.ngpVanDbmodes[0]})@@@ - only one user {{ ${person.firstName} ${person.lastName} exist in ngpvan people list }}`);
				await addNotesAndCodesToPerson(ngp_auth_votebuilder, client, foundUsers[0].vanId, constantUtil.ngpVanDbmodes[0], person.userId);
			} else {
				console.error(`   @@@repd(${constantUtil.ngpVanDbmodes[0]})@@@ - ${foundUsers.length} users {{ ${person.firstName} ${person.lastName} exist in ngpvan people list }}`);
			}

			// await findOrCreateNgpVanUser(ngp_auth, client, person);
		}

		// await syncMyCampaignUsersToPeople(ngp_auth, client, people);
	}
};

syncPeople();
