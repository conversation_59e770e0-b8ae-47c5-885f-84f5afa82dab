// Script to check if unsubscribed emails also appear in blocks or bounces lists
import { SendGrid } from '../../src/global/mail.js';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import csv from 'csv-parser';
import os from 'os';

// Setup proper path resolution for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from the correct path
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

async function readUnsubscribedEmailsFromCSV() {
	return new Promise((resolve, reject) => {
		const results = [];
		const csvPath = path.join(os.homedir(), 'Downloads', 'SendGrid Suppression Unsubscribes.csv');
    
		console.log(`Reading unsubscribed emails from ${csvPath}...`);
    
		fs.createReadStream(csvPath)
			.pipe(csv())
			.on('data', (data) => {
				if (data.email) {
					results.push(data.email.toLowerCase());
				}
			})
			.on('end', () => {
				console.log(`Read ${results.length} unsubscribed emails from CSV`);
				resolve(results);
			})
			.on('error', (error) => {
				console.error('Error reading CSV:', error);
				reject(error);
			});
	});
}

async function getBlockedEmails() {
	try {
		console.log('Fetching blocked emails from SendGrid...');
		let allBlocks = [];
		let offset = 0;
		const limit = 500;
		let hasMoreRecords = true;
    
		while (hasMoreRecords) {
			const request = {
				method: 'GET',
				url: '/v3/suppression/blocks',
				qs: {
					limit: limit,
					offset: offset
				}
			};
      
			console.log(`Fetching blocks batch with offset ${offset}...`);
			const [response, body] = await SendGrid.request(request);
      
			if (!Array.isArray(body) || body.length === 0) {
				hasMoreRecords = false;
			} else {
				allBlocks = [...allBlocks, ...body];
				offset += limit;
        
				if (body.length < limit) {
					hasMoreRecords = false;
				}
        
				// Add a small delay to avoid rate limiting
				await new Promise(resolve => setTimeout(resolve, 1000));
			}
		}
    
		console.log(`Found ${allBlocks.length} blocked emails`);
		return allBlocks.map(block => block.email.toLowerCase());
	} catch (error) {
		console.error('Error fetching blocked emails:', error);
		return [];
	}
}

async function getBouncedEmails() {
	try {
		console.log('Fetching bounced emails from SendGrid...');
		let allBounces = [];
		let offset = 0;
		const limit = 500;
		let hasMoreRecords = true;
    
		while (hasMoreRecords) {
			const request = {
				method: 'GET',
				url: '/v3/suppression/bounces',
				qs: {
					limit: limit,
					offset: offset
				}
			};
      
			console.log(`Fetching bounces batch with offset ${offset}...`);
			const [response, body] = await SendGrid.request(request);
      
			if (!Array.isArray(body) || body.length === 0) {
				hasMoreRecords = false;
			} else {
				allBounces = [...allBounces, ...body];
				offset += limit;
        
				if (body.length < limit) {
					hasMoreRecords = false;
				}
        
				// Add a small delay to avoid rate limiting
				await new Promise(resolve => setTimeout(resolve, 1000));
			}
		}
    
		console.log(`Found ${allBounces.length} bounced emails`);
		return allBounces.map(bounce => bounce.email.toLowerCase());
	} catch (error) {
		console.error('Error fetching bounced emails:', error);
		return [];
	}
}

async function checkSuppressionLists() {
	try {
		console.log('Starting to check suppression lists...');
    
		// Get all unsubscribed emails from CSV
		const unsubscribedEmails = await readUnsubscribedEmailsFromCSV();
    
		// Get all blocked emails from SendGrid
		const blockedEmails = await getBlockedEmails();
    
		// Get all bounced emails from SendGrid
		const bouncedEmails = await getBouncedEmails();
    
		// Find emails that are both unsubscribed and blocked
		const unsubscribedAndBlocked = unsubscribedEmails.filter(email => 
			blockedEmails.includes(email)
		);
    
		// Find emails that are both unsubscribed and bounced
		const unsubscribedAndBounced = unsubscribedEmails.filter(email => 
			bouncedEmails.includes(email)
		);
    
		// Find emails that are in all three lists
		const inAllLists = unsubscribedEmails.filter(email => 
			blockedEmails.includes(email) && bouncedEmails.includes(email)
		);
    
		console.log('=== Suppression Lists Analysis ===');
		console.log(`Total unsubscribed emails: ${unsubscribedEmails.length}`);
		console.log(`Total blocked emails: ${blockedEmails.length}`);
		console.log(`Total bounced emails: ${bouncedEmails.length}`);
		console.log(`Emails that are both unsubscribed and blocked: ${unsubscribedAndBlocked.length}`);
		console.log(`Emails that are both unsubscribed and bounced: ${unsubscribedAndBounced.length}`);
		console.log(`Emails that are in all three lists: ${inAllLists.length}`);
    
		// Output sample emails from each category
		if (unsubscribedAndBlocked.length > 0) {
			console.log('\nSample unsubscribed and blocked emails (first 5):');
			unsubscribedAndBlocked.slice(0, 5).forEach(email => {
				console.log(`- ${email}`);
			});
		}
    
		if (unsubscribedAndBounced.length > 0) {
			console.log('\nSample unsubscribed and bounced emails (first 5):');
			unsubscribedAndBounced.slice(0, 5).forEach(email => {
				console.log(`- ${email}`);
			});
		}
    
		if (inAllLists.length > 0) {
			console.log('\nSample emails in all three lists (first 5):');
			inAllLists.slice(0, 5).forEach(email => {
				console.log(`- ${email}`);
			});
		}
    
		// Write results to CSV files
		const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
		if (unsubscribedAndBlocked.length > 0) {
			const blockedCsvPath = path.join(os.homedir(), 'Downloads', `unsubscribed-and-blocked-${timestamp}.csv`);
			fs.writeFileSync(blockedCsvPath, 'email\n' + unsubscribedAndBlocked.join('\n'));
			console.log(`\nSaved ${unsubscribedAndBlocked.length} unsubscribed and blocked emails to ${blockedCsvPath}`);
		}
    
		if (unsubscribedAndBounced.length > 0) {
			const bouncedCsvPath = path.join(os.homedir(), 'Downloads', `unsubscribed-and-bounced-${timestamp}.csv`);
			fs.writeFileSync(bouncedCsvPath, 'email\n' + unsubscribedAndBounced.join('\n'));
			console.log(`Saved ${unsubscribedAndBounced.length} unsubscribed and bounced emails to ${bouncedCsvPath}`);
		}
    
		if (inAllLists.length > 0) {
			const allListsCsvPath = path.join(os.homedir(), 'Downloads', `in-all-suppression-lists-${timestamp}.csv`);
			fs.writeFileSync(allListsCsvPath, 'email\n' + inAllLists.join('\n'));
			console.log(`Saved ${inAllLists.length} emails in all suppression lists to ${allListsCsvPath}`);
		}
    
	} catch (error) {
		console.error('Error running script:', error);
	}
}

// Execute the function
console.log('Starting suppression lists check...');
checkSuppressionLists()
	.then(() => console.log('Process completed'))
	.catch(error => console.error('Process failed:', error));