require( 'dotenv' ).config();
// const { db } = require( '../global/database' );

const https = require('https');
// const clientModel = require('../models/clientModel');

const endpoint = 'https://0v1o58pn3b.execute-api.us-east-1.amazonaws.com/staging/data-service/answers';

function get(url, params) {
	const urlObj = new URL(url);
	Object.keys(params).forEach(key => urlObj.searchParams.append(key, JSON.stringify(params[key])));

	return new Promise((resolve, reject) => {
		https.get(urlObj, (res) => {
			let data = '';

			res.on('data', (chunk) => {
				data += chunk;
			});

			res.on('end', () => {
				console.log(`Status Code: ${res.statusCode}`);
				console.log(`Response Data: ${data}`);
				resolve(JSON.parse(data));
			});
		}).on('error', (err) => {
			console.error('HTTP Request Error:', err);
			reject(err);
		});
	});
}

console.log('Starting to fetch all clients...');

async function syncAllClients() {
	try {
		console.log('Attempting to fetch all clients...');
		// const allClients = await clientModel.findAll();
		// console.log(`Fetched ${allClients.length} clients.`);
		const allClients = [
			269,268,267,266,265,264,263,262,261,260,251,250,246,244,243,242,241,240,239,238,237,236,235,234,233,232,231,
			230,229,228,225,224,223,222,221,220,219,218,217,216,215,214,213,212,211,210,209,203,202,201,200,199,198,197,
			196,195,190,189,188,187,186,180,178,176,175,170,169,168,165,164,163,162,161,160,159,158,157,156,155,154,153,
			149,148,147,146,145,144,143,140,139,138,133,132,120,119,118,115,114,113,112,102,101,100,99,98,97,96,95,94,93,
			92,85,84,79,78,64,56,54,52,50,46,40,37,36,35,34,32,31,30,29,28,15,13,12,11,10,9,8,7,6,5,4,3,2,1
		];

		allClients.forEach(client => {
			const payload = {
				resource: 'answers',
				method: 'get',
				payload: {
					excludeIds: [],
					clientId: client,
				}
			};

			console.log(`Making request for client ID: ${client}`);

			get(endpoint, payload)
				.then(response => {
					console.log('Response:', response);
				})
				.catch(error => {
					console.error('Error:', error);
				});
		});
	} catch (error) {
		console.error('Error fetching clients:', error);
	}
}

syncAllClients();
