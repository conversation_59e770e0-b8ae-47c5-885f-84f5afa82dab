// Need to write a script that uses statEvents model to create new stat events from February 7th based on page views from Google Analytics
// This script will use the Google Analytics API to get the page views for each page on the site
// and then create a new stat event for each page view.

import jwt from 'jsonwebtoken';
import axios from 'axios';
import fs from 'fs';
import { Sequelize } from 'sequelize';

import env from '../../src/global/environment.js';

// Create an instance just for escaping (no need for actual connection)
const sequelize = new Sequelize('postgres::memory:');

// import clientModel from '../../src/models/clientModel.js';
// import statEventModel from '../../src/models/statEventModel.js';
import { db } from '../../src/global/database.js';

const cleanPath = path =>
	(path || '').replace(/^\/|\-|[^a-z0-9]|\/.*$/g, '').toLowerCase().replace('cityof', '');

const escapedValue = (s) => {
	// Return 'null' for null, undefined, empty values, or '(not set)'
	if (s === null || s === undefined || s === '' || s === '(not set)') {
		return 'null';
	}

	// Handle numbers directly
	if (typeof s === 'number') {
		return s;
	}

	// Handle booleans
	if (typeof s === 'boolean') {
		return s ? 'true' : 'false';
	}

	// Handle dates
	if (s instanceof Date) {
		return s.toISOString();
	}

	// Convert to string and escape special characters
	const escaped = s.toString()
		.replace(/'/g, '\'\'')    // Escape single quotes
		.replace(/\\/g, '\\\\')   // Escape backslashes
		.replace(/\0/g, '')       // Remove null bytes
		.replace(/\n+/g, ' ')     // Replace newlines with spaces
		.replace(/\r|\r\n/g, ' ') // Replace carriage returns with spaces
		.replace(/\t/g, ' ')      // Replace tabs with spaces
		.replace(/\v/g, ' ')      // Replace vertical tabs with spaces
		.replace(/\f/g, ' ')      // Replace form feeds with spaces
		.replace(/"/g, '""')      // Escape double quotes
		.replace(/;/g, '\\;')     // Escape semicolons
		.replace(/;/g, '\\;')     // Escape semicolons
		.replace(/\s*$/, '')   // Remove trailing whitespace
		.trim();                  // Remove leading/trailing whitespace

	// Always wrap strings in single quotes
	return `${sequelize.escape(escaped)}`;
};

let token;

const startDate = new Date('2021-01-01');

const increaseYear = (d, a = 0) => {
	const newDate = new Date(d); // Clone the date
	newDate.setFullYear(newDate.getFullYear() + a);
	return newDate.toISOString().split('T')[0]; // Formats as 'YYYY-MM-DD'
};

function signTokenPayload () {
	const keyFile = JSON.parse(fs.readFileSync(`${process.cwd()}/keys/google-api-key.json`));

	// Replace with your private key from the JSON key file (ensure it's stored securely)
	const PRIVATE_KEY = keyFile.private_key.replace(/\\n/g, '\n'); // Handle newlines

	// JWT Claim Set
	const now = Math.floor(Date.now() / 1000);
	const exp = now + 3600; // 1 hour expiry

	const payload = {
		iss: env.getProperty('ga.key_email'),
		scope: 'https://www.googleapis.com/auth/analytics.readonly',
		aud: 'https://oauth2.googleapis.com/token',
		exp,
		iat: now,
	};

	console.log('GA4 -> signTokenPayload -> Token Payload Signed');

	// Generate JWT Token
	return jwt.sign(payload, PRIVATE_KEY, { algorithm: 'RS256' });
}

async function getAccessToken() {
	try {
		// Generate JWT Token
		const response = await axios.post('https://oauth2.googleapis.com/token', null, {
			headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
			params: {
				grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
				assertion: signTokenPayload(),
			},
		});

		console.log('GA4 -> getAccessToken -> Token generated');

		return response.data.access_token;
	} catch (error) {
		console.error('Error fetching access token:', error.response?.data || error.message);
		throw error;
	}
}

async function getClientIdForPaths(paths) {
	const cleanPaths = Array.from(new Set(paths.map(path =>
		`${cleanPath(path)}`
	)));

	const query = `SELECT id, REPLACE(REPLACE(LOWER(name), ' ', ''), '-', '') AS name FROM clients WHERE name ILIKE ANY (
		ARRAY [${cleanPaths.map(path => `'%${path}%'` ).join(',')}]
	)`;

	const clients = await db.query(query);

	// console.log(`GA4 -> getClientIdForPath -> Query: ${query}`);
	// console.log(clients[0][0]);

	if (!clients.length || !clients[0].length) {
		console.log(`GA4 -> getClientIdForPath -> No client found for path: ${path}`);
		return null;
	}

	const pathsToClients = new Map(Array.from(new Set(paths)).map(path => [
		cleanPath(path), clients[0].find(
			client => cleanPath(path).match(cleanPath(client.name))
		)?.id
	]));

	// console.log(pathsToClients);

	console.log(`GA4 -> getClientIdForPath -> Clients found for paths: ${paths.length} - ${clients[0].length}`);

	return pathsToClients;
}

async function fetchAnalyticsData(propertyID, table, startDate, endDate, offset = 0) {
	try {
		if (!token) token = await getAccessToken();

		console.log(`GA4 -> fetchAnalyticsData -> Fetching data from ${startDate} to ${endDate}`);

		const response = await axios.post(
			`https://analyticsdata.googleapis.com/v1beta/properties/${propertyID}:runReport`,
			{
				// 4yearsAgo 30daysAgo 7daysAgo yesterday today
				dateRanges: [{ startDate, endDate }],
				metrics: [{ name: 'eventCount' }],
				dimensions: [
					{ name: 'date' },
					{ name: 'hostName' },
					{ name: 'pagePath' },
					{ name: 'eventName' },
					{ name: 'pageReferrer' },
					// { name: 'fullPageUrl' },
					{ name: 'deviceCategory' },
					{ name: 'region' },
					{ name: 'customEvent:event_label' },
					{ name: 'customEvent:event_category' },
				],
				limit: 10000,
				offset,
			},
			{
				headers: {
					Authorization: `Bearer ${token}`,
					'Content-Type': 'application/json',
				},
			}
		);

		if (!response.data.rows?.length) {
			console.log(`GA4 -> fetchAnalyticsData -> No data found for ${startDate} to ${endDate}`);
			console.log(response.data);
			return;
		}

		console.log(`GA4 -> fetchAnalyticsData -> Data fetched (${response.data.rows.length} records)`);
		// console.log('metricHeaders', response.data.metricHeaders);
		// console.log('dimensionHeaders', response.data.dimensionHeaders);
		// console.log('metadata', response.data.metadata);
		console.log('');

		const clientPaths = await getClientIdForPaths(
			// Paths
			response.data.rows.map(row => row.dimensionValues[2].value)
		);

		let batchQuery = `
			INSERT INTO ${table} (created_at, host, path, event_name, referrer, device, region, label, category, client_id)
			VALUES
		`;
		let batchCount = 0;

		for (const row of response.data.rows) {
			const values = row.dimensionValues.map(
				value => {
					// if value is 20250210 convert to date for PSQL
					if (value.value.length === 8 && !isNaN(value.value)) {
						return `to_timestamp(${escapedValue(value.value)}, 'YYYYMMDD')`;
					}

					// if value is (not set) convert to null
					return (value.value === '(not set)' ? 'null' : `${escapedValue(value.value.replace(/'/, '\'\''))}`);
				}
			);

			batchQuery += `(${values.join(',')}, ${escapedValue(clientPaths.get(cleanPath(row.dimensionValues[2].value))) || 'null'}),`;
			batchCount++;

			if (batchCount === 100) {
				batchQuery = batchQuery.slice(0, -1) + ';'; // Remove last comma and add semicolon

				fs.writeFileSync('/tmp/ga-query.sql', batchQuery, 'utf8'); // Write query to file

				process.stdout.write('.');

				try {
					await db.query(batchQuery);
				} catch (error) {
					console.log('');
					console.error('Error executing batch query:', error);
				}

				// Reset batch query and count
				batchQuery = `
					INSERT INTO ${table} (created_at, host, path, event_name, referrer, device, region, label, category, client_id)
					VALUES
				`;
				batchCount = 0;
			}
		}

		// Execute any remaining queries in the batch
		if (batchCount > 0) {
			batchQuery = batchQuery.slice(0, -1) + ';'; // Remove last comma and add semicolon

			fs.writeFileSync('/tmp/ga-query.sql', batchQuery, 'utf8'); // Write query to file

			try {
				await db.query(batchQuery);
			} catch (error) {
				console.log('');
				console.error('Error executing final batch query:', error);
			}
		}

		if (response.data.rows.length === 10000) {
			fetchAnalyticsData(propertyID, table, startDate, endDate, offset + response.data.rows.length);
		}

		console.log('');
		console.log(`GA4 -> fetchAnalyticsData -> Data inserted into database (${response.data.rows.length} records)`);
	} catch (error) {
		console.log('');
		console.error('Error fetching analytics data:', error.response?.data || error.message);
	}
}

const propertyIDs = ['388305914', '372915626'];

const tables = [
	'stats.google_analytics_data',
	'stats.google_analytics_data_lp',
];

for (const i in propertyIDs) {
	const propertyID = propertyIDs[i];
	const table = tables[i];

	console.log(`GA4 -> Backpopulating data for property ${propertyID} into table ${table}`);

	db.query(`
		DROP TABLE IF EXISTS ${table};

		CREATE TABLE IF NOT EXISTS ${table} (
			created_at TIMESTAMP,
			host TEXT,
			referrer TEXT,
			path TEXT,
			event_name TEXT,
			device TEXT,
			region TEXT,
			label TEXT,
			category TEXT,
			client_id BIGINT
		);
	`);

	async function main() {
		await fetchAnalyticsData(propertyID, table, increaseYear(startDate), increaseYear(startDate, 1));
		await fetchAnalyticsData(propertyID, table, increaseYear(startDate, 1), increaseYear(startDate, 2));
		await fetchAnalyticsData(propertyID, table, increaseYear(startDate, 2), increaseYear(startDate, 3));
		await fetchAnalyticsData(propertyID, table, increaseYear(startDate, 3), 'today');
	}
	
	main().catch(console.error);	
}