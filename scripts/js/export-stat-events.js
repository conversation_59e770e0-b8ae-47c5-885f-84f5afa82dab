#!/usr/bin/env node

// Script to export stat events with host "embed.repd.us" for a given client_id as CSV
import { db } from '../../src/global/database.js';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import os from 'os';

// Setup proper path resolution for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const HOST = 'embed.repd.us';

// Load environment variables from the correct path
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Get client ID from command line arguments
const clientId = process.argv[2];
if (!clientId) {
	console.error('Please provide a client ID as an argument');
	console.error('Usage: node scripts/js/export-stat-events.js <client_id>');
	process.exit(1);
}

// Validate client_id is a number
if (isNaN(parseInt(clientId))) {
	console.error('Client ID must be a valid number');
	process.exit(1);
}

async function exportStatEvents() {
	try {
		console.log(`Fetching stat events for client_id ${clientId} with host "embed.repd.us"...`);

		// Query to get all stat events with host "embed.repd.us" for the given client_id
		const query = `
            SELECT 
                id,
                created_at,
                updated_at,
                event_action,
                event_category,
                event_label,
                origin,
                host,
                ipa,
                access_level,
                original,
                ai_search_query,
                referrer,
                mobile,
                browser
            FROM stats.stat_events 
            WHERE client_id = :clientId 
            AND host != '${HOST}'
            ORDER BY created_at DESC
        `;

		const results = await db.query(query, {
			replacements: { clientId: parseInt(clientId) },
			type: db.QueryTypes.SELECT
		});

		if (results.length === 0) {
			console.log(`No stat events found for client_id ${clientId}`);
			return;
		}

		console.log(`Found ${results.length} stat events`);

		// Convert results to CSV format
		const csvHeaders = [
			'id', 'created_at', 'updated_at', 'event_action', 'event_category',
			'event_label', 'origin', 'host', 'ipa', 'access_level', 'original',
			'ai_search_query', 'referrer', 'mobile', 'browser'
		];

		// Helper function to escape CSV values
		const escapeCsvValue = (value) => {
			if (value === null || value === undefined) {
				return '';
			}
            
			// Convert to string
			let stringValue = String(value);
            
			// If value contains comma, newline, or quote, wrap in quotes and escape internal quotes
			if (stringValue.includes(',') || stringValue.includes('\n') || stringValue.includes('"')) {
				stringValue = '"' + stringValue.replace(/"/g, '""') + '"';
			}
            
			return stringValue;
		};

		// Build CSV content
		let csvContent = csvHeaders.join(',') + '\n';
        
		results.forEach(row => {
			const csvRow = csvHeaders.map(header => {
				let value = row[header];
                
				// Handle JSON fields
				if (header === 'original' && value) {
					value = JSON.stringify(value);
				}
                
				// Handle date fields
				if ((header === 'created_at' || header === 'updated_at' || header === 'export_job_date') && value) {
					value = new Date(value).toISOString();
				}
                
				return escapeCsvValue(value);
			});
            
			csvContent += csvRow.join(',') + '\n';
		});

		// Generate filename with timestamp
		const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
		const filename = `stat-events-client-${clientId}-${timestamp}.csv`;
		const filePath = path.join(os.homedir(), 'Downloads', filename);

		// Write CSV file
		fs.writeFileSync(filePath, csvContent, 'utf8');

		console.log(`\n✅ Successfully exported ${results.length} stat events to:`);
		console.log(`📁 ${filePath}`);
        
		// Show some sample data
		console.log('\n📊 Sample of exported data:');
		console.log(`Event Actions: ${[...new Set(results.map(r => r.event_action))].join(', ')}`);
		console.log(`Event Categories: ${[...new Set(results.map(r => r.event_category))].join(', ')}`);
		console.log(`Date Range: ${new Date(Math.min(...results.map(r => new Date(r.created_at)))).toLocaleDateString()} - ${new Date(Math.max(...results.map(r => new Date(r.created_at)))).toLocaleDateString()}`);

	} catch (error) {
		console.error('Error exporting stat events:', error);
		process.exit(1);
	} finally {
		// Close database connection
		await db.close();
	}
}

// Execute the export
exportStatEvents();
