// Usage: node scripts/js/manualEmailSender.js 3

import cron from 'node-cron'; // if needed for scheduling later

import '../../src/models/index.js';

import ngpVanExportRequestsModel from '../../src/models/ngpVanExportRequestsModel.js';
import answerModel from '../../src/models/answerModel.js';
import questionModel from '../../src/models/questionModel.js';
import clientModel from '../../src/models/clientModel.js';
import userModel from '../../src/models/userModel.js';
import ngpVanEmailsModel from '../../src/models/ngpVanEmailsModel.js';
import { sendAnswerEmail } from '../../src/mailers/ngpVanMailer.js';

const manualIndex = process.argv[2] || 0;
const exportIds = process.argv[3] || ''; //  [558, 557, 556]
const testRun = false;
// const testRun = true;

const EMAIL_BATCH_SIZE = 50;
// Specific emails for first batch (index 0)
const SPECIFIC_EMAILS = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

async function sendEmailsForIndex(manualIndex) {
	// Find first unprocessed export request
	const exportRequests = await ngpVanExportRequestsModel.findAll({
		where: { enabled: true, id: exportIds.split(',') },
		order: [['createdAt', 'ASC']]
	});

	if (!exportRequests.length) {
		console.log('No unprocessed export requests found.');
		return;
	}

	for (const exportRequest of exportRequests) {
		// Fetch associated answer and question info
		const answer = await answerModel.findOne({
			where: { id: exportRequest.answerId },
			include: [{
				as: 'question',
				model: questionModel,
				include: [
					{ as: 'client', model: clientModel, required: false },
					{ as: 'user', model: userModel, required: false }
				],
				required: false
			}]
		});

		if (!answer) {
			console.error(`Answer not found for export request ${exportRequest.id}`);
			await exportRequest.update({ isProcessed: true });
			continue;
		}

		// Find admin user for client
		const user = await userModel.findOne({
			where: { clientId: answer.question.clientId, accessLevel: 'admin' },
			include: [{ as: 'client', model: clientModel }]
		});
		if (!user) {
			console.error(`No admin user found for client ${answer.question.clientId}`);
			await exportRequest.update({ isProcessed: true });
			continue;
		}

		const allPeople = exportRequest.metaData?.people || [];
		if (!allPeople || allPeople.length === 0) {
			console.log(`No people found in export request ${exportRequest.id}`);
			await exportRequest.update({ isProcessed: true });
			continue;
		}

		// Use manualIndex from CLI as a batch multiplier
		const manualOffset = Number(manualIndex);
		if (isNaN(manualOffset) || manualOffset < 0) {
			console.error('Invalid manual offset provided. It must be a non-negative number.');
			return;
		}
		const processedIndex = manualOffset * EMAIL_BATCH_SIZE;
		if (processedIndex >= allPeople.length) {
			console.error(`Invalid offset: starting index ${processedIndex} is >= ${allPeople.length}`);
			continue;
		}

		const currentBatchPeople = allPeople.slice(processedIndex, processedIndex + EMAIL_BATCH_SIZE);
		console.log(`Processing export request ${exportRequest.id} from index ${processedIndex} for ${currentBatchPeople.length} emails`);

		const emailSettings = {
			fromEmail: user.client.fromEmail || user.client.email,
			fromEmailName: user.client.name,
			subject: (testRun ? 'TEST: ' : '') + (user.client.subject || `A new Answer from ${user.client.name} is available`),
			topMessage: user.client.topMessage,
			campaignName: user.client.campaignName,
			campaignAddress: user.client.campaignAddress
		};

		let messageId;
		let emails;
		try {
			if (testRun) {
				emails = SPECIFIC_EMAILS;
				messageId = await sendAnswerEmail(emails, user, emailSettings, answer, exportRequest.savedListId, true);
			} else {
				emails = currentBatchPeople.map(person => person.Email || person.email);
				messageId = await sendAnswerEmail(emails, user, emailSettings, answer, exportRequest.savedListId, true);
			}

			// Test mode: Log the parameters instead of sending the email.
			console.log('Test mode: Would send email with the following:');
			console.log('Emails:', emails);
			console.log('Email settings:', emailSettings);
			console.log('Answer data:', answer.question.text);
			console.log('Message ID:', messageId);
		} catch (e) {
			console.error(`Error sending email: ${e.message}`);
			return;
		}

		const messageIds = exportRequest.messageIds || [];
		messageIds.push(messageId);

		if (!testRun) {
			// Save each email record
			for (const person of currentBatchPeople) {
				try {
					const attributes = {
						enabled: true,
						email: person.Email || person.email,
						messageId: messageId,
						recipientName: `${person.FirstName} ${person.LastName}`,
						savedListId: exportRequest.savedListId,
						savedListName: exportRequest.savedListName
					};

					await ngpVanEmailsModel.findOrCreate({
						where: { messageId: messageId, email: person.Email || person.email },
						defaults: attributes
					});

					process.stdout.write('.');

				} catch (error) {
					console.error(`Error storing recipient: ${error.message}`);
				}
			}

			// Update exportRequest with new processed index
			const newProcessedIndex = processedIndex + currentBatchPeople.length;
			const isFullyProcessed = newProcessedIndex >= allPeople.length;
			await exportRequest.update({
				messageIds,
				processedIndex: newProcessedIndex,
				isProcessed: true // isFullyProcessed
			});

			console.log(`Processed ${currentBatchPeople.length} emails for request ${exportRequest.id}. ${isFullyProcessed ? 'Completed.' : `${Math.round((newProcessedIndex / allPeople.length) * 100)}% complete`}`);
		}
	}
}

if (manualIndex === undefined) {
	console.error('Usage: node manualEmailSender.js <startIndex>');
	process.exit(1);
}

sendEmailsForIndex(manualIndex)
	.then(() => process.exit(0))
	.catch(err => {
		console.error(`Error: ${err.message}`);
		process.exit(1);
	});

// node scripts/js/manualEmailSender.js 4 559; node scripts/js/manualEmailSender.js 5 559; node scripts/js/manualEmailSender.js 6 559;
// node scripts/js/manualEmailSender.js 7 559; node scripts/js/manualEmailSender.js 8 559; node scripts/js/manualEmailSender.js 9 559;
// node scripts/js/manualEmailSender.js 10 559; node scripts/js/manualEmailSender.js 11 559; node scripts/js/manualEmailSender.js 12 559;
// node scripts/js/manualEmailSender.js 13 559; node scripts/js/manualEmailSender.js 14 559; node scripts/js/manualEmailSender.js 15 559;
// node scripts/js/manualEmailSender.js 16 559; node scripts/js/manualEmailSender.js 17 559; node scripts/js/manualEmailSender.js 18 559;
// node scripts/js/manualEmailSender.js 19 559; node scripts/js/manualEmailSender.js 20 559; node scripts/js/manualEmailSender.js 21 559;
// node scripts/js/manualEmailSender.js 22 559; node scripts/js/manualEmailSender.js 23 559; node scripts/js/manualEmailSender.js 24 559;
// node scripts/js/manualEmailSender.js 25 559; node scripts/js/manualEmailSender.js 26 559; node scripts/js/manualEmailSender.js 27 559;
// node scripts/js/manualEmailSender.js 28 559; node scripts/js/manualEmailSender.js 29 559; node scripts/js/manualEmailSender.js 30 559;


// node scripts/js/manualEmailSender.js 0 560; sleep 10; node scripts/js/manualEmailSender.js 1 560; sleep 10; 
// node scripts/js/manualEmailSender.js 2 560; sleep 10; node scripts/js/manualEmailSender.js 3 560; sleep 10; 
// node scripts/js/manualEmailSender.js 4 560; sleep 10; node scripts/js/manualEmailSender.js 5 560; sleep 10; 
// node scripts/js/manualEmailSender.js 6 560; sleep 10; node scripts/js/manualEmailSender.js 7 560; sleep 10; 
// node scripts/js/manualEmailSender.js 8 560; sleep 10; node scripts/js/manualEmailSender.js 9 560; sleep 10; 
// node scripts/js/manualEmailSender.js 10 560; sleep 10; node scripts/js/manualEmailSender.js 11 560; sleep 10; 

// for i in {0..30}; do
//   node scripts/js/manualEmailSender.js $i 562
//   sleep 120
// done
