// SELECT * FROM public.sessions WHERE enabled=true and user_id in (select id from users where access_level = 'admin')

user = {
	'id': '2076',
	'enabled': true,
	'createdAt': '2022-03-17T20:53:36.180Z',
	'updatedAt': '2022-05-06T18:07:15.375Z',
	'clientId': '181',
	'imageUrl': 'https://files.repd.us/files/574b544c-5317-455a-a36d-c28d768e4491.png',
	'firstName': 'Megan',
	'lastName': 'Burns',
	'email': '<EMAIL>',
	'phone': null,
	'zip': '80233',
	'ipa': '*************',
	'location': 'US / New York - Inwood',
	'accessLevel': 'admin'
};

client = {
	'id': '181',
	'enabled': true,
	'name': 'CDP',
	'email': '<EMAIL>',
	'link': 'https://repd.us/CDP',
	'logoURL': 'https://files.repd.us/files/3be9bfb9-41ea-4c69-b3c5-ff65e2ef77b6.png',
	'websiteURL': 'https://www.coloradodems.org/',
	'donationURL': 'https://secure.actblue.com/donate/cdp-mp?refcode=website',
	'topBarColour': '#035db8',
	'videoLinksColour': '#035db8',
	'plusAskPillColour': '#035db8',
	'newQuestionColour': '#035db8',
	'openQuestionsAndAnswersColour': '#39b0e6',
	'ngpVanUsername': '••••••••',
	'ngpVanApiKey': '••••-••••-••••',
	'categories': 'Affordable Healthcare ,Civil Rights ,Climate Crisis ,Crime Rates ,Diversity and Inclusion ,Economic Justice ,Education Equity ,Green Economy ,Homelessness ,Jobs ,Opioid Crisis ',
	'isPublished': true,
	'donationDisplaysPermanently': false,
	'campaignName': 'Colorado Democratic Party'
};

localStorage.token = 'bd69fc63-2b16-4d2c-9924-c1e3355537ec';
localStorage.client = JSON.stringify(client);
localStorage.user = JSON.stringify(user);