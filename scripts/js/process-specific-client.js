import { processSpecificClient } from '../../src/schedules/internalStatsSchedule.js';

const clientId = process.argv[2];
const kind = process.argv[3] || 'both';

if (!clientId) {
	console.error('Usage: node scripts/js/process-specific-client.js <clientId> [kind]');
	console.error('Kind options: questionsAndUsers, stats, both (default)');
	process.exit(1);
}

processSpecificClient(parseInt(clientId), kind)
	.then(() => {
		console.log('Processing completed successfully');
		process.exit(0);
	})
	.catch(error => {
		console.error('Processing failed:', error);
		process.exit(1);
	});