// Script to disable imported users for a specific client that match unsubscribed emails from CSV
import importedUsersModel from '../../src/models/importedUsersModel.js';
import clientModel from '../../src/models/clientModel.js';
import { Op } from 'sequelize';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import csv from 'csv-parser';
import os from 'os';

// Setup proper path resolution for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from the correct path
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Get client ID from command line arguments
const clientId = process.argv[2];
if (!clientId) {
	console.error('Please provide a client ID as an argument');
	process.exit(1);
}

async function readUnsubscribedEmailsFromCSV() {
	return new Promise((resolve, reject) => {
		const results = [];
		const csvPath = path.join(os.homedir(), 'Downloads', 'SendGrid Suppression Unsubscribes.csv');

		console.log(`Reading unsubscribed emails from ${csvPath}...`);

		fs.createReadStream(csvPath)
			.pipe(csv())
			.on('data', (data) => {
				// Assuming the CSV has an 'email' column - adjust if the column name is different
				if (data.email) {
					results.push(data.email.toLowerCase());
				}
			})
			.on('end', () => {
				console.log(`Read ${results.length} unsubscribed emails from CSV`);
				resolve(results);
			})
			.on('error', (error) => {
				console.error('Error reading CSV:', error);
				reject(error);
			});
	});
}

async function disableImportedUsers(clientId) {
	try {
		console.log(`Starting to process client ID: ${clientId}`);

		// Get client information
		const client = await clientModel.findByPk(clientId);
		if (!client) {
			console.error(`Client with ID ${clientId} not found`);
			return;
		}

		console.log(`Processing client: ${client.name}`);

		// // Enable all users to start out with:
		// await importedUsersModel.update(
		// 	{ enabled: true },
		// 	{
		// 		where: {
		// 			client_id: clientId
		// 		}
		// 	}
		// );

		// Check total imported users (both enabled and disabled)
		const totalImportedUsers = await importedUsersModel.count({
			where: {
				client_id: clientId
			}
		});

		console.log(`Total imported users for this client (enabled and disabled): ${totalImportedUsers}`);

		// Get all unsubscribed emails from CSV
		const unsubscribedEmails = await readUnsubscribedEmailsFromCSV();
		console.log(`Found ${unsubscribedEmails.length} unsubscribed emails in CSV`);

		// Get all imported users for this client
		const importedUsers = await importedUsersModel.findAll({
			where: {
				client_id: clientId,
				enabled: true
			},
			attributes: ['id', 'email'],
			raw: true
		});

		console.log(`Found ${importedUsers.length} active imported users to check`);

		// If no active users, check if they might have been disabled already
		if (importedUsers.length === 0) {
			const disabledUsers = await importedUsersModel.count({
				where: {
					client_id: clientId,
					enabled: false
				}
			});
			console.log(`Found ${disabledUsers} disabled imported users for this client`);
		}

		// Find users that match unsubscribed emails
		const matchingUsers = importedUsers.filter(user =>
			user.email && unsubscribedEmails.includes(user.email.toLowerCase())
		);

		console.log(`Found ${matchingUsers.length} users that match unsubscribed emails`);

		// Debug: Show some sample matches
		if (matchingUsers.length > 0) {
			console.log('Sample matches (first 5):');
			matchingUsers.slice(0, 5).forEach(user => {
				console.log(`- ID: ${user.id}, Email: ${user.email}`);
			});
		}

		// Disable matching users
		if (matchingUsers.length > 0) {
			const userIds = matchingUsers.map(user => user.id);

			const result = await importedUsersModel.update(
				{ enabled: false },
				{
					where: {
						id: userIds
					}
				}
			);

			console.log(`Disabled ${result[0]} imported users`);
		}

		// Get count after disabling to verify
		const afterCount = await importedUsersModel.count({
			where: {
				client_id: clientId,
				enabled: true,
				email: {
					[Op.in]: unsubscribedEmails
				}
			}
		});

		console.log('=== Disable Summary ===');
		console.log(`Client: ${client.name} (ID: ${clientId})`);
		console.log(`Total imported users checked: ${importedUsers.length}`);
		console.log(`Matching unsubscribed emails: ${matchingUsers.length}`);
		console.log(`Users disabled: ${matchingUsers.length}`);
		console.log(`Remaining matches after disabling: ${afterCount}`);

	} catch (error) {
		console.error('Error running script:', error);
	}
}

// Execute the function
console.log('Starting imported users disable process...');
disableImportedUsers(clientId)
	.then(() => console.log('Process completed'))
	.catch(error => console.error('Process failed:', error));
