const ngpVanExportRequestsModel = require( '../../models/ngpVanExportRequestsModel' );
const ngpVanEmailsModel = require( '../../models/ngpVanEmailsModel' );

async function createEmails () {
	const exportRequests = await ngpVanExportRequestsModel.findAll();

	console.log('exportRequests', exportRequests.length);

	for (var index in exportRequests) {
		const exportRequest = exportRequests[index];
		console.log('exportRequest', exportRequest);

		const attributes = {
			enabled: true,
			savedListId: exportRequest.savedListId,
			savedListName: exportRequest.savedListName
		};

		for (var messageIndex in exportRequest.messageIds) {
			const messageId = exportRequest.messageIds[messageIndex];
			const person = exportRequest.people[messageIndex] || {};

			console.log('index', index, messageId, person);

			attributes.email = person.Email || person.email || '';
			attributes.recipientName = `${ person.FirstName || ''} ${ person.LastName || ''}`;
			attributes.messageId = messageId;

			console.log('attributes', attributes);

			return ngpVanEmailsModel.findOrCreate( {
				where: { messageId: messageId },
				defaults: attributes
			} ).then( results => console.log('results', results) );
		}
	}
}

createEmails();
