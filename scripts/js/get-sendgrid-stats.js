require( 'dotenv' ).config();

// const { SendGridMail } = require('../global/mail');
const logger = require('../../global/log');
// const env = require('../global/environment');
const fetch = require('node-fetch');

// Your SendGrid API key from the environment
const apiKey = process.env['mail.send_grid.key'];

// List of message IDs foir Broadview
const messageIds = [
	'9zmqIvmMTFif7B9xdmajAQ',
	'HJ2OiGHgSN6hk2LGjtho5Q',
	'T_H4wuf8Qf6H8VVxPKIepg',
	'DDpMDWdRQxWsqIDn4vRzwA',
	'8Jv0THIKTGy98KK3BW9JDA',
	'2dnxWr60S8CCeLVAVEbL-g',
	'SmJ7gK6hTgmxzR0spPiDfQ',
	'2-CCJ09RTZO_pWm114wP5Q',
	'7C2XQp9URCOCE1uxLCCnSQ',
	'NDY_gPA7TU6qVCY2NGwI8w',
	'G9ELSD_oSYOBbeaZ40HzPg'
];

// Base URL for the SendGrid API
const url = 'https://api.sendgrid.com/v3/messages';

// Function to get message stats
async function getMessageStats(messageId) {
	try {
		const response = await fetch(`${url}/${messageId}`, {
			method: 'GET',
			headers: {
				'Authorization': `Bearer ${apiKey}`,
				'Content-Type': 'application/json'
			}
		});

		if (response.ok) {
			const data = await response.json();
			logger.info(`Stats for message ID ${messageId}:`);
			logger.info(data);
			return data;
		} else {
			logger.error(`Failed to get stats for message ID ${messageId}. Status code: ${response.status}`);
			return null;
		}
	} catch (error) {
		logger.error(`Error fetching stats for message ID ${messageId}:`, error);
		return null;
	}
}

// Function to fetch stats for all message IDs
async function fetchAllStats() {
	for (const messageId of messageIds) {
		await getMessageStats(messageId);
	}
}

// Start fetching stats
fetchAllStats();