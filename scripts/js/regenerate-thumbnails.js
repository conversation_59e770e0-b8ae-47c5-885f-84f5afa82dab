import path from 'path';
import uploadFileToS3 from '../../../lambdas/src/common/uploadFileToS3.js';
import execCommand from '../../../lambdas/src/common/utils/execCommand.js';

async function generateAndUploadThumbnails({
	sourceKey,
	localVideoFilePath,
	targetBucket,
	duration,
}) {
	try {
		console.log(`Video duration: ${duration}`);
		const numThumbnails = 10;
		const interval = (duration == 0 ? 10 : duration ) / numThumbnails;
		console.log(`Thumbnail interval: ${interval}`);
		const outputDir = '/tmp';
		const now = Date.now();

		const thumbnailFilePaths = Array(numThumbnails)
			.fill(0)
			.map((_, i) => path.join(outputDir, `thumbnail-${now}-${i}-001.png`));

		console.log(`Thumbnail paths: ${JSON.stringify(thumbnailFilePaths)}`);

		const conversionCommands = thumbnailFilePaths.map(
			(thumbnailPath, i) =>
				`ffmpeg -ss ${
					i * interval
				} -i ${localVideoFilePath} -vframes 1 ${thumbnailPath.replace(
					'001',
					'%03d'
				)}`
		);

		console.log(`Conversion commands: ${JSON.stringify(conversionCommands)}`);

		const convertResult = await Promise.all(
			conversionCommands.map(execCommand)
		);
		const stdout = await execCommand(`ls ${outputDir}`);
		const files = stdout.split('\n');
		const generatedThumbnailFilePaths = files.filter(
			(f) =>
				f.split('-')[0] === 'thumbnail' && f.split('-')[1] === now.toString()
		);
		console.log(`LS OUTPUT: ${stdout}`);
		console.log(
			`generatedThumbnailFilePaths: ${JSON.stringify(
				generatedThumbnailFilePaths
			)}`
		);

		// convertResult.forEach((stdout, i) => {
		//   console.log(`CONVERT OUTPUT - ${i}: ${stdout}`);
		// });

		await Promise.all(
			generatedThumbnailFilePaths.map((filePath, index) =>
				uploadFileToS3({
					targetBucket,
					filePath: path.join(outputDir, filePath),
					targetFileKey: `thumbnails/${path.basename(sourceKey)}-${index}.png`,
				})
			)
		);

		console.log('Uploaded generated thumbnails to s3');
	} catch (error) {
		console.error('Error generating thumbnails: ', error);
	}
}

async function handler() {
	const sourceBucket = 'https://repd-api-files.s3.amazonaws.com/video_parts';
	const sourceKey = 'e4d0603d-07fc-478b-b024-f5de852d9e32.mov';
	const targetBucket = 'https://repd-api-files.s3.amazonaws.com/thumbnails';

	const inputData = await downLoadFileFromS3({ sourceBucket, sourceKey });
	generateAndUploadThumbnails({ inputData, sourceKey, targetBucket });
}

handler();
