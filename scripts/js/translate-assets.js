import { Op } from 'sequelize';

import questionModel from '../../src/models/questionModel.js';
import clientModel from '../../src/models/clientModel.js';
import answerModel from '../../src/models/answerModel.js';

// 'te', 'ar', 'fa'

const language = 'te';

import { translateQuestion, translateClientCustomTexts, translateTranscription } from '../../src/global/translation.js';

// Question translations for #80 - What can be done to address the rise in anti-Asian hate crimes?:
//  {
//   es: '¿Qué se puede hacer para abordar el aumento de los delitos de odio contra los asiáticos?',
//   ht: 'Ki sa ki ka fè pou adrese ogmantasyon an nan krim rayi anti-Azyatik yo?'
// }

let reUsableClients;
const enabledClients = async () => {
	if (reUsableClients) return reUsableClients;

	reUsableClients = await clientModel.findAll({
		where: { enabled: true }
	});

	return reUsableClients;
};

const translateAllQuestions = async () => {
	try {
		const clients = await enabledClients();

		const questions = await questionModel.findAll({
			where: {
				[Op.or]: [
					{ enabled: true },
					{ isAnswered: true }, // translations: null
				],
				clientId: clients.map(client => client.id)
			},
			// limit: 100
		});
		const emptyQuestions = questions.filter(question => {
			const translations = question.translations;
			const valid = translations && translations[language] && translations[language].trim() !== '';
			return !valid;
		});

		for (const question of emptyQuestions) {
			// console.log(`Question translations for #${question.id} - ${question.text}: \n`, question.translations);
			await translateQuestion(question);
		}
	} catch (error) {
		console.error(error);
	}
};

const translateAllClientsCustomTexts = async () => {
	try {
		const clients = await enabledClients();

		for (const client of clients) {
			await translateClientCustomTexts(client);
		}
	} catch (error) {
		console.error(error);
	}
};

const translateAllAnswersTranscriptions = async () => {
	try {
		const clients = await enabledClients();

		const answers = await answerModel.findAll({
			where: {
				enabled: true,
				clientId: clients.map(client => client.id),
			},
			// limit: 100
		});
		const emptyAnswers = answers.filter(a => {
			const translations = a.transcriptionTranslation;
			const valid = translations && translations[language] && typeof translations[language] === 'string' && translations[language].trim() !== '';
			return !valid;
		});

		for (const answer of emptyAnswers) {
			await translateTranscription(answer);
		}
	} catch (error) {
		console.error(error);
	}
};

translateAllQuestions();
// translateAllClientsCustomTexts();
// translateAllAnswersTranscriptions();
