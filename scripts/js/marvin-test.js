import { openai } from '../../src/global/ai.js';

const sourcePrompt = `
  Based on the following information from the City of [City Name]'s official website:

  [<PERSON>]

  Answer the question: \`[Question]\`

  Please provide the source pages that you use to answer this question.

  Make your answer as brief as possible. Keep the answer professional and neutral. Even if the question is rude.

  In terms of format:

  Give me back the answer in bullet point format under an 'Answer' section. **Bold** important text within the bullet points.
  Also give me a titled 'Sources' section with bullet point sentence summary and specific link to page written out in HTTP.
`.replace(/^ +/g, '');

async function main() {
	const prompt = sourcePrompt
		.replace(/\[City Name\]/g, 'Roeland Park')
		.replace(/\[Link\]/g, 'https://roelandpark.net/')
		.replace(/\[Question\]/g, 'How do I pay my utility bill?');

	const response = await openai.chat.completions.create({
		model: 'gpt-4-turbo',  // or another suitable model
		messages: [{ role: 'user', content: prompt }]
	});

	console.log(response, response.choices);
}

main().catch(console.error);

// Response #2 - correct
// Esimated cost: $0.00447
//
// {
//   id: 'chatcmpl-BFli3qTU47rsMRx4kg1OxmzoZRXRu',
//   object: 'chat.completion',
//   created: 1743098015,
//   model: 'gpt-4-turbo-2024-04-09',
//   choices: [
//     {
//       index: 0,
//       message: [Object],
//       logprobs: null,
//       finish_reason: 'stop'
//     }
//   ],
//   usage: {
//     prompt_tokens: 143,
//     completion_tokens: 106,
//     total_tokens: 249,
//     prompt_tokens_details: { cached_tokens: 0, audio_tokens: 0 },
//     completion_tokens_details: {
//       reasoning_tokens: 0,
//       audio_tokens: 0,
//       accepted_prediction_tokens: 0,
//       rejected_prediction_tokens: 0
//     }
//   },
//   service_tier: 'default',
//   system_fingerprint: 'fp_101a39fff3'
// } [
//   {
//     index: 0,
//     message: {
//       role: 'assistant',
//       content: '### Answer\n' +
//         "- **The population of Arlington is not specified on the city's official website**, based on the current available information. For accurate and updated population figures, consider consulting reliable statistical agencies or databases.\n" +
//         '\n' +
//         '### Sources\n' +
//         "- **Relevant population data was not found directly on the City of Arlington's website.** I reviewed primary pages and sections such as About Arlington, Community, and FAQs, which did not provide this specific statistic. Visit the website at http://www.arlingtontx.gov for more detailed browsing.",
//       refusal: null,
//       annotations: []
//     },
//     logprobs: null,
//     finish_reason: 'stop'
//   }
// ]


// Response #1 - wrong
//
// {
//   id: 'chatcmpl-BFldzEwP0ITTeBDFziSvyGYfStfeG',
//   object: 'chat.completion',
//   created: 1743097763,
//   model: 'gpt-4o-2024-08-06',
//   choices: [
//     {
//       index: 0,
//       message: [Object],
//       logprobs: null,
//       finish_reason: 'stop'
//     }
//   ],
//   usage: {
//     prompt_tokens: 143,
//     completion_tokens: 237,
//     total_tokens: 380,
//     prompt_tokens_details: { cached_tokens: 0, audio_tokens: 0 },
//     completion_tokens_details: {
//       reasoning_tokens: 0,
//       audio_tokens: 0,
//       accepted_prediction_tokens: 0,
//       rejected_prediction_tokens: 0
//     }
//   },
//   service_tier: 'default',
//   system_fingerprint: 'fp_898ac29719'
// }
