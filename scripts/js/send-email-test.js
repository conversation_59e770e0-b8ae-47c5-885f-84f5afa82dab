const { SendGridMail } = require( '../../global/mail' );
const logger = require( '../../global/log' );

const env = require( '../../global/environment' );

var message = {
	to:   null,
	bcc:  null,
	ccc:  null,
	from: '<EMAIL>'
};

function sendApprovedQuestionEmail () {
	setMailDefaults();
	message.templateId = env.getProperty( 'mail.template.new_question.template_id' );

	const CTA = env.getProperty( 'mail.template.new_question.url' )
		.replace( /\:client/, 'kam'.replace( /[^a-z0-9]+/ig, '-' ) )
		.replace( /\:questionId/, '' );

	message.dynamic_template_data = {
		subject: 'Test - <PERSON><PERSON> approved your question!',
		title: 'Congratulations! Your question was approved.',
		body: 'What’s your plan to help high schoolers prepare for college or training programs?',

		logoURL: 'https://files.repd.us/files/9aefa291-f622-4f60-a296-ed4ac230558b.png',
		ctaText: 'See the latest Answers',
		CTA: CTA
	};
	message.subject = message.dynamic_template_data.subject;

	message.to = [
		'<EMAIL>', 
		'<EMAIL>',
		'<EMAIL>'
	];
	send();
}

function sendNewQuestionEmail () {
	setMailDefaults();
	message.templateId = env.getProperty( 'mail.template.new_question.template_id' );

	const CTA = 'https://admin.repd.us/:client?questionId=:questionId'
		.replace( /\:client/, 'Kam'.replace( /[^a-z0-9]+/ig, '-' ) )
		.replace( /\:questionId/, '?questionvId=5' );

	message.dynamic_template_data = {
		subject: 'Test - A New Voter Question!',
		title: 'A New Voter Question!',
		body: 'What’s your plan to help high schoolers prepare for college or training programs?',

		logoURL: 'https://files.repd.us/files/9aefa291-f622-4f60-a296-ed4ac230558b.png',
		ctaText: 'Click to Approve!',
		CTA: CTA
	};
	message.subject = 'New Question Added!';
	message.from = '<EMAIL>';

	message.to = [
		'<EMAIL>', 
		'<EMAIL>', 
		'<EMAIL>',
		'<EMAIL>'
	];
	send();
}


// Private

function setMailDefaults ( user = {} ) {
	const to  = [ user.email ];
	const BCC = [];
	const CC  = [];

	message.to  = to;
	message.bcc = BCC;
	message.ccc = CC;
	message.html = '<strong></strong>';
	// message.text = '<strong></strong>';
	message.from = '<EMAIL>';
}

function send () {
	console.log(message);

	return SendGridMail.send( message ).catch( error => {
		logger.log( 'error', 'Event sending email', error );
	} );
}

// sendApprovedQuestionEmail()
sendNewQuestionEmail();