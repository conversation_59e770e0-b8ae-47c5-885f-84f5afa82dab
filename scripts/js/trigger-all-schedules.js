/**
 * <PERSON><PERSON><PERSON> to manually trigger all scheduled jobs
 * Run with: node scripts/js/trigger-all-schedules.js [scheduleName]
 * If scheduleName is provided, only that schedule will be triggered
 */
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

// Get directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import all schedule modules
async function triggerAllSchedules() {
	try {
		console.log('Loading all schedule modules...');
    
		// Path to the schedules directory
		const schedulesDir = path.join(__dirname, '../../src/schedules');
    
		// Get all schedule files
		const scheduleFiles = fs.readdirSync(schedulesDir)
			.filter(file => file.endsWith('Schedule.js') && file !== 'index.js');
    
		console.log(`Found ${scheduleFiles.length} schedule files to trigger`);
    
		// Check if specific schedule was requested
		const requestedSchedule = process.argv[2];
		if (requestedSchedule) {
			const targetFile = scheduleFiles.find(file => file.toLowerCase().includes(requestedSchedule.toLowerCase()));
			if (!targetFile) {
				console.error(`Schedule "${requestedSchedule}" not found!`);
				console.log('Available schedules:');
				scheduleFiles.forEach(file => console.log(`- ${path.basename(file, '.js')}`));
				process.exit(1);
			}
      
			console.log(`Triggering only ${targetFile}...`);
			await triggerSchedule(targetFile, schedulesDir);
			return;
		}
    
		// Trigger each schedule
		for (const file of scheduleFiles) {
			await triggerSchedule(file, schedulesDir);
		}
    
		console.log('All schedules triggered successfully');
	} catch (error) {
		console.error('Error triggering schedules:', error);
	}
}

async function triggerSchedule(file, schedulesDir) {
	const scheduleName = path.basename(file, '.js');
	try {
		console.log(`Triggering ${scheduleName}...`);
    
		// Import the schedule module
		const scheduleModule = await import(path.join(schedulesDir, file));
    
		// Execute the default export (which should be the main function)
		const scheduleFunction = scheduleModule.default;
    
		if (typeof scheduleFunction === 'function') {
			await scheduleFunction();
			console.log(`✓ ${scheduleName} executed successfully`);
		} else {
			console.warn(`✗ ${scheduleName} doesn't export a default function`);
		}
	} catch (error) {
		console.error(`✗ Error executing ${scheduleName}:`, error);
	}
}

// Execute the main function
triggerAllSchedules().catch(error => {
	console.error('Fatal error:', error);
	process.exit(1);
});
