/**
 * <PERSON><PERSON><PERSON> to automatically process recent question shares
 * Usage: node scripts/js/create-question-share.js
 */
import { db } from '../../src/global/database.js';
import env from '../../src/global/environment.js';
import { Op } from 'sequelize';

import '../../src/models/index.js';

import questionShareModel from '../../src/models/questionShareModel.js';
import userModel from '../../src/models/userModel.js';
import questionModel from '../../src/models/questionModel.js';
import sessionService from '../../src/services/sessionService.js';
import questionMailer from '../../src/mailers/questionMailer.js';

async function processRecentQuestionShares() {
	try {
		// Find all question shares from the last week
		const shares = await questionShareModel.findAll({
			where: {
				createdAt: {
					[Op.gt]: db.literal('NOW() - INTERVAL \'1 week\'')
				},
				// shareLink: null, // Only process shares that don't have a link yet
				enabled: true
			},
			include: [
				{
					model: questionModel,
					as: 'question',
					include: [
						{ model: db.models.clients, as: 'client' }
					]
				},
				{
					model: userModel,
					as: 'user'
				}
			]
		});

		console.log(`Found ${shares.length} recent shares to process`);

		for (const share of shares) {
			try {
				// Create session and generate share link
				const session = await sessionService.promiseCreateAndTransform(share.user);
				const shareLink = env.getProperty('mail.template.share_question.url')
					.replace(/\:questionId/, share.questionId)
					.replace(/\:sessionToken/, session.token)
					.replace(/\:userId/, Buffer.from(`|${session.userId}|`).toString('base64'));

				// Update share link
				await share.update({ shareLink });

				// Send email
				await questionMailer.sendQuestionInviteEmail(share.question, session);

				console.log(share.clientId, share.questionId, share.userId, share.question.text, session.token, '');

				// Update question status
				await share.question.update({ isShared: true });

				console.log(`Processed share for user ${share.user.email} with link: ${shareLink}`);
			} catch (error) {
				console.error(`Error processing share ${share.id}:`, error);
				// Continue with next share even if one fails
				continue;
			}
		}

		console.log('Finished processing question shares');
		process.exit(0);
	} catch (error) {
		console.error('Error processing question shares:', error);
		process.exit(1);
	}
}

processRecentQuestionShares();