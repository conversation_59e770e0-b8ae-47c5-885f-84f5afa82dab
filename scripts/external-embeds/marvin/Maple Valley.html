<script>
  // Replace this object with your actual client data
  const client = {
    id: '279', // should be a string
    name: 'MapleValley',
  };

  (function embedRepdWidget(client) {
    const iframe = document.createElement('iframe');
    iframe.id = 'repd-embed';

    // Get the current page URL
    const referral = window.location.href;

    // Construct embed URL using client name
    const embedUrl = new URL(`https://embed.repd.us/${client.name.toLowerCase()}`);
    embedUrl.searchParams.append('referrer', encodeURIComponent(referral));

    iframe.src = embedUrl.toString();
    iframe.style.cssText = `
      position: fixed;
      bottom: 0rem;
      left: 0rem;
      border: none;
      z-index: 2147483648;
      transition: all 0.3s ease;
      background: transparent;
      width: 200px;
      height: 200px;
      max-height: calc(100vh - 40px);
      max-width: 98vw;
      border-radius: 10px;
    `;

    // Add mobile-specific positioning (2 inches = 144px)
    const isMobile = window.innerWidth <= 768;
    if (isMobile) {
      iframe.style.bottom = '124px';
      iframe.style.left = 'auto';
      iframe.style.right = '-20px';
    }

    const handleMessage = (event) => {
      if (event.origin !== 'https://embed.repd.us') return;

      const { type, dimensions } = event.data;
      const initialHeight = dimensions.height === '200px';
      const isMobile = window.innerWidth <= 768;

      if (type === 'resize') {
        iframe.style.width = dimensions.width;
        const newHeight = initialHeight
          ? '200px'
          : parseInt(dimensions.height) + 400 + 'px';
        iframe.style.height = newHeight;

        // Adjust bottom position on mobile when expanded to keep it anchored at bottom
        if (isMobile && !initialHeight) {
          iframe.style.bottom = '0rem';
          iframe.style.right = '0px';
        } else if (isMobile) {
          iframe.style.bottom = '124px';
          iframe.style.right = '-20px';
        }
      }
    };

    requestAnimationFrame(() => {
      iframe.style.transition = 'all 0.3s ease';
    });

    // Handle window resize to adjust positioning
    const handleResize = () => {
      const isMobile = window.innerWidth <= 768;
      if (isMobile) {
        iframe.style.bottom = '124px';
        iframe.style.left = 'auto';
        iframe.style.right = '-20px';
      } else {
        iframe.style.bottom = '0rem';
        iframe.style.left = '0rem';
        iframe.style.right = 'auto';
      }
    };

    window.addEventListener('message', handleMessage);
    window.addEventListener('resize', handleResize);
    document.body.appendChild(iframe);

    // Optional cleanup if you ever remove the iframe manually:
    window.cleanupEmbed = function () {
      window.removeEventListener('message', handleMessage);
      window.removeEventListener('resize', handleResize);
      iframe.remove();
    };
  })(client);
</script>