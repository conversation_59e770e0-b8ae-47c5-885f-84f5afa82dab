<script>
  // Replace this object with your actual client data
  const client = {
    id: '293', // should be a string
    name: 'Mount Jackson',
  };

  (function embedRepdWidget(client) {
    if (client.id !== '293') return;

    // Create style element
    const style = document.createElement('style');
    style.textContent = `
      #repd-embed {
        position: fixed;
        bottom: 20rem;
        left: 0rem;
        border: none;
        z-index: 50;
        transition: all 0.3s ease;
        background: transparent;
        width: 200px;
        height: 200px;
        max-height: calc(100vh - 40px);
        max-width: 98vw;
        border-radius: 10px;
      }
      
      @media (max-width: 640px) {
        #repd-embed {
          bottom: 0;
        }
      }
    `;
    document.head.appendChild(style);

    const iframe = document.createElement('iframe');
    iframe.id = 'repd-embed';

    // Get the current page URL
    const referral = window.location.href;

    // Construct embed URL using client name
    const embedUrl = new URL(`https://embed.repd.us/${client.name.toLowerCase().replace(/\s/g, '')}`);
    embedUrl.searchParams.append('referrer', encodeURIComponent(referral));

    iframe.src = embedUrl.toString();
    
    // No inline styles here, using the style tag instead

    const handleMessage = (event) => {
      if (event.origin !== 'https://embed.repd.us') return;

      const { type, dimensions } = event.data;
      const initialHeight = dimensions.height === '200px';

      if (type === 'resize') {
        iframe.style.width = dimensions.width;
        iframe.style.height = initialHeight
          ? '200px'
          : parseInt(dimensions.height) + 400 + 'px';
      }
    };

    requestAnimationFrame(() => {
      iframe.style.transition = 'all 0.3s ease';
    });

    window.addEventListener('message', handleMessage);
    document.body.appendChild(iframe);

    // Optional cleanup if you ever remove the iframe manually:
    window.cleanupEmbed = function () {
      window.removeEventListener('message', handleMessage);
      iframe.remove();
      style.remove(); // Also remove the style element
    };
  })(client);
</script>
