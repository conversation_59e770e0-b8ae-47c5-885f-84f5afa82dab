<script>
  // Replace this object with your actual client data
  const client = {
    id: '260', // should be a string
    name: '<PERSON>',
  };

  (function embedRepdWidget(client) {
    if (client.id !== '260') return;

    const iframe = document.createElement('iframe');
    iframe.id = 'repd-embed';

    // Get the current page URL
    const referral = window.location.href;

    // Construct embed URL using client name
    const embedUrl = new URL(`https://embed.repd.us/${client.name.toLowerCase()}`);
    embedUrl.searchParams.append('referrer', encodeURIComponent(referral));

    iframe.src = embedUrl.toString();
    iframe.style.cssText = `
      position: fixed;
      bottom: 0rem;
      left: 0rem;
      border: none;
      z-index: 50;
      transition: all 0.3s ease;
      background: transparent;
      width: 200px;
      height: 200px;
      max-height: calc(100vh - 40px);
      max-width: 98vw;
      border-radius: 10px;
    `;

    const handleMessage = (event) => {
      if (event.origin !== 'https://embed.repd.us') return;

      const { type, dimensions } = event.data;
      const initialHeight = dimensions.height === '200px';

      if (type === 'resize') {
        iframe.style.width = dimensions.width;
        iframe.style.height = initialHeight
          ? '200px'
          : parseInt(dimensions.height) + 400 + 'px';
      }
    };

    requestAnimationFrame(() => {
      iframe.style.transition = 'all 0.3s ease';
    });

    window.addEventListener('message', handleMessage);
    document.body.appendChild(iframe);

    // Optional cleanup if you ever remove the iframe manually:
    window.cleanupEmbed = function () {
      window.removeEventListener('message', handleMessage);
      iframe.remove();
    };
  })(client);
</script>