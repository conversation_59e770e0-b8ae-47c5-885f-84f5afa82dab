BEGIN TRANSACTION;

-- Insert missing stat events for AI queries (ensuring 1:1 correspondence by ID)
INSERT INTO stats.stat_events (
  created_at, updated_at, enabled, user_id, question_id, answer_id, client_id, export_job_id, saved_list_id,
  event_action, event_category, event_label, origin, host, ipa, question_text, category, search_term, video_feedback_amount,
  first_name, last_name, email, phone, zip, access_level, export_job_date, original, ai_search_query
)
SELECT
    aq.created_at, aq.created_at, true, aq.user_id, aq.question_id, aq.answer_id, aq.client_id, null, null,
    'repd_morpheus_ai', 'search_query', 'manuallyCreatedQueryStatEvent',
    CASE WHEN COALESCE(u.access_level, '') ILIKE '%admin%' THEN
        'https://admin.repd.us'
    ELSE
        'https://app.repd.us'
    END AS origin,
    'undefined', 
    COALESCE(u.ipa, 'unknown'), 
    null, 
    aq.category, 
    null, 
    null,
    COALESCE(u.first_name, 'Unknown'), 
    COALESCE(u.last_name, 'User'), 
    u.email, 
    u.phone, 
    u.zip, 
    COALESCE(CAST(u.access_level AS VARCHAR), 'guest'), 
    null, 
    COALESCE(aq.original, '{}'), 
    aq.query
FROM ai_queries aq
LEFT JOIN users u ON aq.user_id = u.id
WHERE aq.enabled = true 
AND aq.client_id IS NOT NULL
AND aq.id NOT IN (
    SELECT DISTINCT CAST(SUBSTRING(original FROM '"id":([0-9]+)') AS INTEGER)
    FROM stats.stat_events 
    WHERE event_category = 'search_query' 
    AND event_action = 'repd_morpheus_ai'
    AND original ~ '"id":[0-9]+'
    AND SUBSTRING(original FROM '"id":([0-9]+)') IS NOT NULL
);

-- Alternative approach if the above regex doesn't work reliably:
-- Use a simpler approach based on created_at, user_id, client_id, and query matching
DELETE FROM stats.stat_events WHERE event_label = 'manuallyCreatedQueryStatEvent';

INSERT INTO stats.stat_events (
  created_at, updated_at, enabled, user_id, question_id, answer_id, client_id, export_job_id, saved_list_id,
  event_action, event_category, event_label, origin, host, ipa, question_text, category, search_term, video_feedback_amount,
  first_name, last_name, email, phone, zip, access_level, export_job_date, original, ai_search_query
)
SELECT
    aq.created_at, aq.created_at, true, aq.user_id, aq.question_id, aq.answer_id, aq.client_id, null, null,
    'repd_morpheus_ai', 'search_query', 'manuallyCreatedQueryStatEvent',
    CASE WHEN COALESCE(u.access_level, '') ILIKE '%admin%' THEN
        'https://admin.repd.us'
    ELSE
        'https://app.repd.us'
    END AS origin,
    'undefined', 
    COALESCE(u.ipa, 'unknown'), 
    null, 
    aq.category, 
    null, 
    null,
    COALESCE(u.first_name, 'Unknown'), 
    COALESCE(u.last_name, 'User'), 
    u.email, 
    u.phone, 
    u.zip, 
    COALESCE(CAST(u.access_level AS VARCHAR), 'guest'), 
    null, 
    COALESCE(aq.original, '{}'), 
    aq.query
FROM ai_queries aq
LEFT JOIN users u ON aq.user_id = u.id
WHERE aq.enabled = true 
AND aq.client_id IS NOT NULL
AND NOT EXISTS (
    SELECT 1 
    FROM stats.stat_events se
    WHERE se.event_category = 'search_query' 
    AND se.event_action = 'repd_morpheus_ai'
    AND se.created_at = aq.created_at
    AND COALESCE(se.user_id, -1) = COALESCE(aq.user_id, -1)
    AND se.client_id = aq.client_id
    AND se.ai_search_query = aq.query
);

-- Verify the results for client 263
SELECT 'AI Queries Count' as table_name, COUNT(*) as count
FROM ai_queries 
WHERE client_id = 263 AND enabled = true
UNION ALL
SELECT 'Stat Events Count' as table_name, COUNT(*) as count
FROM stats.stat_events 
WHERE client_id = 263 AND event_category = 'search_query' AND event_action = 'repd_morpheus_ai';

-- Show missing records that should be created
SELECT aq.id, aq.user_id, aq.query, aq.created_at
FROM ai_queries aq
WHERE aq.client_id = 263 
AND aq.enabled = true
AND NOT EXISTS (
    SELECT 1 
    FROM stats.stat_events se
    WHERE se.event_category = 'search_query' 
    AND se.event_action = 'repd_morpheus_ai'
    AND se.created_at = aq.created_at
    AND COALESCE(se.user_id, -1) = COALESCE(aq.user_id, -1)
    AND se.client_id = aq.client_id
    AND se.ai_search_query = aq.query
);

ROLLBACK TRANSACTION;