import logger from "./utils/logger";
import AWS from "aws-sdk";

const S3 = new AWS.S3();

export default async function downLoadFileFromS3({ sourceBucket, sourceKey }) {
  const params = {
    Bucket: sourceBucket,
    Key: `video_parts/${sourceKey}`,
  };

  logger.info("Downloading file from S3");

  const inputData = await S3.getObject(params).promise();

  logger.info("Successfully downloaded file from S3");

  return inputData;
}
