export const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Credentials": "true",
  "Access-Control-Allow-Methods": "OPTIONS,POST,GET",
  "Access-Control-Allow-Headers":
    "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
};

const headers = { ...corsHeaders, "Content-Type": "application/json" };
export const successResponse = (data) => {
  return {
    statusCode: 200,
    headers,
    body: JSON.stringify(data),
  };
};

export const internalErrorResponse = () => {
  return {
    statusCode: 500,
    headers,
    body: JSON.stringify({ error: "Internal server error" }),
  };
};

export const badDataResponse = (message) => {
  return {
    statusCode: 400,
    headers,
    body: JSON.stringify({ error: message }),
  };
};

export const notFoundResponse = () => {
  return {
    statusCode: 404,
    headers,
    body: JSON.stringify({ error: "Not Found" }),
  };
};

export const methodNotAllowedResponse = () => {
  return {
    statusCode: 405,
    headers,
    body: JSON.stringify({ error: "Method Not Allowed" }),
  };
};
