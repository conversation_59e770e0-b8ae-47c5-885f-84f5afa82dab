function Logger() {
  function log(logLevel, ...message) {
    switch (logLevel) {
      case "Error":
        console.error(...message);
        break;
      case "Warn":
        console.warn(...message);
        break;
      case "Debug":
        console.debug(...message);
        break;
      case "Audit":
        console.log("AUDIT", ...message);
        break;
      default:
        console.log(...message);
        break;
    }
  }

  function info(...message) {
    log("Info", ...message);
  }

  function audit(...message) {
    log("Audit", ...message);
  }

  function debug(...message) {
    log("Debug", ...message);
  }

  function warn(...message) {
    log("Warn", ...message);
  }

  function error(...message) {
    log("Error", ...message);
  }

  return {
    info,
    audit,
    debug,
    warn,
    error,
  };
}

export default Logger();
