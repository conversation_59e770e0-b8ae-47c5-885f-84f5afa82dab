import logger from "./utils/logger";

export default function parseEvent(event) {
  logger.info("Parsing event");

  const { body } = event;

  const parsed = {
    sourceBucket: body.sourceBucket || "repd-api-files",
    sourceKey: `${body.sourceKey}`,
    duration: `${body.duration}`,
    targetBucket: body.targetBucket || "repd-api-files",
  };

  logger.debug("Parsed event: ", JSON.stringify(parsed));

  return parsed;
}
