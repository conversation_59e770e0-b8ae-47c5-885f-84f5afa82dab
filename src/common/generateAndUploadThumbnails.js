import logger from "./utils/logger";
import path from "path";
import uploadFileToS3 from "./uploadFileToS3";
import execCommand from "./utils/execCommand";

export default async function generateAndUploadThumbnails({
  sourceKey,
  localVideoFilePath,
  targetBucket,
  duration,
}) {
  try {
    logger.debug(`Video duration: ${duration}`);
    const numThumbnails = 10;
    const interval = (duration == 0 ? 10 : duration ) / numThumbnails;
    logger.debug(`Thumbnail interval: ${interval}`);
    const outputDir = "/tmp";
    const now = Date.now();

    const thumbnailFilePaths = Array(numThumbnails)
      .fill(0)
      .map((_, i) => path.join(outputDir, `thumbnail-${now}-${i}-001.png`));

    logger.debug(`Thumbnail paths: ${JSON.stringify(thumbnailFilePaths)}`);

    const conversionCommands = thumbnailFilePaths.map(
      (thumbnailPath, i) =>
        `ffmpeg -ss ${
          i * interval
        } -i ${localVideoFilePath} -vframes 1 ${thumbnailPath.replace(
          "001",
          "%03d"
        )}`
    );

    logger.debug(`Conversion commands: ${JSON.stringify(conversionCommands)}`);

    const convertResult = await Promise.all(
      conversionCommands.map(execCommand)
    );
    const stdout = await execCommand(`ls ${outputDir}`);
    const files = stdout.split("\n");
    const generatedThumbnailFilePaths = files.filter(
      (f) =>
        f.split("-")[0] === "thumbnail" && f.split("-")[1] === now.toString()
    );
    logger.debug(`LS OUTPUT: ${stdout}`);
    logger.debug(
      `generatedThumbnailFilePaths: ${JSON.stringify(
        generatedThumbnailFilePaths
      )}`
    );

    // convertResult.forEach((stdout, i) => {
    //   logger.debug(`CONVERT OUTPUT - ${i}: ${stdout}`);
    // });

    await Promise.all(
      generatedThumbnailFilePaths.map((filePath, index) => {
        const folderWithId = path.dirname(sourceKey); // e.g., 'video_parts/f60aaf99-5629-4199-aecc-21ce71d85930'
        const uniqueId = path.basename(folderWithId); // e.g., 'f60aaf99-5629-4199-aecc-21ce71d85930'

        return uploadFileToS3({
          targetBucket,
          filePath: path.join(outputDir, filePath),
          targetFileKey: `thumbnails/${uniqueId}-${index}.png`,
        });
      })
    );

    logger.debug(`Uploaded generated thumbnails to s3`);
  } catch (error) {
    logger.error("Error generating thumbnails: ", error);
  }
}
