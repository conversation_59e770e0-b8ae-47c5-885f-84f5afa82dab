import AWS from "aws-sdk";
import logger from "./utils/logger";
import env from "./env";
const lambda = new AWS.Lambda();

export default function invokeLambda({
  name,
  payload,
  invocationType = "Event",
}) {
  return new Promise((resolve, reject) => {
    const nameInEnvironment = `${name}--${env.environment}`;
    const params = {
      FunctionName: nameInEnvironment,
      InvocationType: invocationType,
      // InvocationType: 'RequestResponse',
      Payload: JSON.stringify(payload),
    };
    logger.debug("Will invoke with params: ", JSON.stringify(params));
    lambda.invoke(params, function (err, data) {
      if (err) {
        logger.error(
          `Error invoking ${nameInEnvironment} Lambda function:`,
          err
        );
        reject(`Error invoking ${nameInEnvironment} Lambda function:`, err);
      } else {
        logger.debug(
          `Successfully invoked lambda ${nameInEnvironment}. Output:`,
          JSON.stringify(data.Payload)
        );
        resolve(data.Payload);
      }
    });
  });
}
