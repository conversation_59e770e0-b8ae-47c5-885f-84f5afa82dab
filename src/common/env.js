import { config } from "dotenv";

config({ path: `.env.${process.env.NODE_ENV}` });

export default {
  db: {
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
  },
  mail: {
    sendGridKey: process.env.SEND_GRID_KEY,
    sendGridWebhookKey: process.env.SEND_GRID_WEBHOOK_KEY,
  },
  sessionSecret: process.env.SESSION_SECRET,
  environment: process.env.environment,
  supabase: {
    url: process.env.SUPABASE_URL,
    publicKey: process.env.SUPABASE_PUBLIC_KEY,
    serviceKey: process.env.SUPABASE_SERVICE_ROLE,
  },
  openai: {
    key: process.env.OPEN_AI_DEV_KEY,
  },
};
