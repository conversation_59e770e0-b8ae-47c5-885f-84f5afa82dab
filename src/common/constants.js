import env from "./env";

export default {
  userAccessLevels: [
    "super admin",
    "admin",
    "user",
    "visitor",
    "guest admin",
    "blocked",
    "manager",
  ],
  statKinds: ["pageView", "videoAnswerView", "donationClick"],
  videoFileExtensions: [
    "mp4",
    "mpeg",
    "mov",
    "mkv",
    "flv",
    "3gp",
    "avi",
    "wmv",
  ],
  defaultClientCategories: [
    "Immigration",
    "Covid-19",
    "Justice Reform",
    "Climate",
    "Economy",
    "Education",
    "Healthcare",
  ],
  defaultGovernmentClientCategories: [
    "Budget",
    "City Planning",
    "Climate",
    "Education",
    "Parks & Recreation",
    "Public Safety",
    "Public Works",
    "Transportation",
  ],
  defaultCodeFields: ["Repd User", "Repd Click Volunteer", "Repd Click Donate"],
  defaultActivistCodeFields: [
    "Repd User",
    "Repd Click Volunteer",
    "Repd Click Donate",
  ],
  ngpVanDbmodes: ["MyVoters", "MyCampaign"],
  dataServiceUrl: `https://${env.environment === 'prod' ? '7osp39he1i' : '0v1o58pn3b' }.execute-api.us-east-1.amazonaws.com/${env.environment}/data-service`,
};
