import { dbInstance } from "../database";
import SuperModel from "../SuperModel";
import { Sequelize } from "sequelize";

export default dbInstance.define(
  "votes",
  SuperModel({
    amount: { field: "amount", type: Sequelize.NUMBER, allowNull: false },

    userId: { field: "user_id", type: Sequelize.BIGINT, allowNull: false },
    answerId: { field: "answer_id", type: Sequelize.BIGINT, allowNull: true },
    questionId: {
      field: "question_id",
      type: Sequelize.BIGINT,
      allowNull: true,
    },
  }),
  { timestamps: true }
);
