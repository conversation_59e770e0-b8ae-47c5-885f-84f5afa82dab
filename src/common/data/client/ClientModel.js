import { dbInstance } from "../database";
import SuperModel from "../SuperModel";
import { Sequelize } from "sequelize";

export default dbInstance.define(
  "clients",
  SuperModel({
    name: { field: "name", type: Sequelize.STRING, allowNull: false },
    email: { field: "email", type: Sequelize.STRING, allowNull: false },
    clientType: { field: "client_type", type: Sequelize.STRING },
    blockedUsersIds: {
      field: "blocked_users_ids",
      type: Sequelize.ARRAY(Sequelize.INTEGER),
    },
    link: { field: "link", type: Sequelize.STRING },
    subDomainAdmin: { field: "sub_domain_admin", type: Sequelize.STRING },
    logoURL: { field: "logo_url", type: Sequelize.STRING },
    websiteURL: { field: "website_url", type: Sequelize.STRING },
    donationURL: { field: "donation_url", type: Sequelize.STRING },
    volunteerURL: { field: "volunteer_url", type: Sequelize.STRING },
    backgroundImageURL: {
      field: "background_image_url",
      type: Sequelize.STRING,
    },
    topBarColour: { field: "top_bar_colour", type: Sequelize.STRING },
    videoLinksColour: { field: "video_links_colour", type: Sequelize.STRING },
    plusAskPillColour: {
      field: "plus_ask_pill_colour",
      type: Sequelize.STRING,
    },
    newQuestionColour: { field: "new_question_colour", type: Sequelize.STRING },
    openQuestionsAndAnswersColour: {
      field: "open_questions_and_answers_colour",
      type: Sequelize.STRING,
    },
    textOptions: { field: "text_options", type: Sequelize.JSONB },
    translations: { field: "translations", type: Sequelize.JSONB },
    categories: { field: "categories", type: Sequelize.STRING },
    ngpVanUsername: { field: "ngp_van_username", type: Sequelize.STRING },
    ngpVanApiKey: { field: "ngp_van_api_key", type: Sequelize.STRING },
    isPublished: { field: "is_published", type: Sequelize.BOOLEAN },
    donationDisplaysPermanently: {
      field: "donation_displays_permanently",
      type: Sequelize.BOOLEAN,
    },
    isLocked: { field: "is_locked", type: Sequelize.BOOLEAN },
    fromEmail: { field: "email_from", type: Sequelize.STRING },
    subject: { field: "email_subject", type: Sequelize.STRING },
    topMessage: { field: "email_top_message", type: Sequelize.STRING },
    campaignName: { field: "legal_campaign_name", type: Sequelize.STRING },
    campaignAddress: { field: "campaign_address", type: Sequelize.STRING },
    donateText: { field: "donate_text", type: Sequelize.STRING },
    donateCtaText: { field: "donate_cta_text", type: Sequelize.STRING },
    headerHomeLinkText: {
      field: "header_home_link_text",
      type: Sequelize.STRING,
    },
    headerDonateLinkText: {
      field: "header_donate_link_text",
      type: Sequelize.STRING,
    },
    headerVolunteerLinkText: {
      field: "header_volunteer_link_text",
      type: Sequelize.STRING,
    },
    emailDonateCtaText: {
      field: "email_donate_cta_text",
      type: Sequelize.STRING,
    },
    postQuestionText: { field: "post_question_text", type: Sequelize.STRING },
    postQuestionBtnText: {
      field: "post_question_btn_text",
      type: Sequelize.STRING,
    },
  }),
  { timestamps: true }
);
