import UserModel from "./user/UserModel";
import ClientModel from "./client/ClientModel";
import QuestionModel from "./question/QuestionModel";
import VoteModel from "./vote/VoteModel";
import AnswerModel from "./answer/AnswerModel";
import QuestionShareModel from "./questionShare/QuestionShareModel";

UserModel.belongsTo(ClientModel, { as: "client" });
// userModel.hasMany( sessionModel, { as: 'sessions' } );
UserModel.hasMany(QuestionModel, { as: "questions" });
UserModel.hasMany(VoteModel, { as: "votes" });
// userModel.hasMany( commentModel, { as: 'comments' } );

// sessionModel.belongsTo( userModel, { as: 'user' } );

QuestionModel.belongsTo(UserModel, { as: "user" });
QuestionModel.belongsTo(ClientModel, { as: "client" });
QuestionModel.hasMany(VoteModel, { as: "votes" });
QuestionModel.hasMany(AnswerModel, { as: "answers" });
QuestionModel.hasMany(QuestionShareModel, { as: "questionShares" });

AnswerModel.belongsTo(UserModel, { as: "user" });
AnswerModel.belongsTo(ClientModel, { as: "client" });
AnswerModel.belongsTo(QuestionModel, { as: "question" });
AnswerModel.hasMany(VoteModel, { as: "votes" });
// AnswerModel.hasMany( likeModel, { as: 'likes' } );
// AnswerModel.hasMany( commentModel, { as: 'comments' } );
// AnswerModel.hasMany( questionShareModel, { as: 'question_shares' } );

VoteModel.belongsTo(UserModel, { as: "user" });
VoteModel.belongsTo(QuestionModel, { as: "question" });
VoteModel.belongsTo(AnswerModel, { as: "answer" });

QuestionShareModel.belongsTo(UserModel, { as: "user" });
QuestionShareModel.belongsTo(QuestionModel, { as: "question" });
QuestionShareModel.belongsTo(AnswerModel, { as: "answer" });
QuestionShareModel.belongsTo(ClientModel, { as: "client" });
