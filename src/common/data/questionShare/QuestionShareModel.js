import { dbInstance } from "../database";
import SuperModel from "../SuperModel";
import { Sequelize } from "sequelize";

export default dbInstance.define(
  "question_shares",
  SuperModel({
    clientId: { field: "client_id", type: Sequelize.BIGINT, allowNull: false },
    questionId: {
      field: "question_id",
      type: Sequelize.BIGINT,
      allowNull: false,
    },
    userId: { field: "user_id", type: Sequelize.BIGINT, allowNull: false },
    answerId: { field: "answer_id", type: Sequelize.BIGINT, allowNull: true },
    isAnswered: {
      field: "is_answered",
      type: Sequelize.BOOLEAN,
      default: false,
    },
  }),
  { timestamps: true }
);
