import { Sequelize } from "sequelize";
import pg from "pg";
import env from "../env";
import logger from "../utils/logger";

function DbInstance() {
  const {
    db: { database, host, user, name, password, port },
  } = env;

  const options = {
    host: host,
    dialect: "postgres",
    dialectModule: pg,
    storage: null,
    benchmark: true,
    pool: { max: 5, min: 0, idle: 10000 },
    define: {
      // Don't add the timestamp attributes ( updatedAt, createdAt )
      timestamps: false,
      // Prevent sequelize from pluralizing table names
      freezeTableName: true,
    },
  };
  const dbInstance = new Sequelize(database, user, password, options);
  dbInstance
    .authenticate()
    .then(() => {
      logger.info("Database connection has been established successfully.");
    })
    .catch((err) => {
      logger.error("Unable to connect to the database.", JSON.stringify(err));
    });

  return dbInstance;
}

export const dbInstance = DbInstance();
