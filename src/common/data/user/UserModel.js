import { dbInstance } from "../database";
import SuperModel from "../SuperModel";
import { Sequelize } from "sequelize";
import constants from "../../constants";

export default dbInstance.define(
  "users",
  SuperModel({
    clientId: {
      field: "client_id",
      type: Sequelize.NUMBER,
      references: { model: "clients", key: "id" },
    },

    imageUrl: { field: "image_url", type: Sequelize.STRING },
    firstName: { field: "first_name", type: Sequelize.STRING },
    lastName: { field: "last_name", type: Sequelize.STRING },
    email: { field: "email", type: Sequelize.STRING },
    phone: { field: "phone", type: Sequelize.STRING },
    zip: { field: "zip", type: Sequelize.STRING },

    ipa: { field: "ipa", type: Sequelize.STRING },
    location: { field: "location", type: Sequelize.STRING },

    accessLevel: {
      field: "access_level",
      type: Sequelize.ENUM,
      allowNull: false,
      values: constants.userAccessLevels,
    },

    passwordEncrypted: { field: "password_encrypted", type: Sequelize.STRING },
    verificationToken: { field: "verification_token", type: Sequelize.STRING },
    isVerified: { field: "is_verified", type: Sequelize.BOOLEAN },
    isSocialAccount: { field: "is_social_account", type: Sequelize.BOOLEAN },
    ignoreNotifications: {
      field: "ignore_notifications",
      type: Sequelize.BOOLEAN,
    },
  }),
  {
    timestamps: true,
  }
);
