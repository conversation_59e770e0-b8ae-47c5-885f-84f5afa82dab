import AnswerModel from "./AnswerModel";
import ClientModel from "../client/ClientModel";
import UserModel from "../user/UserModel";
import QuestionModel from "../question/QuestionModel";
import VoteModel from "../vote/VoteModel";
import logger from "../../utils/logger";
import QuestionShareModel from "../questionShare/QuestionShareModel";

function AnswerRepository() {
  async function getById(id) {
    logger.info(`AnswerRepository.getById(${id})`);

    try {
      const answer = await AnswerModel.findOne({
        where: { id, enabled: true },
        id,
        include: [
          {
            as: "user",
            model: UserModel,
            required: false,
          },
          {
            as: "client",
            model: ClientModel,
            required: false,
          },
          {
            as: "question",
            model: QuestionModel,
            isAnswered: false,
            enabled: true,
            required: false,
            include: [
              { as: "user", model: UserModel, required: false },
              {
                where: { enabled: true },
                as: "votes",
                model: VoteModel,
                include: [{ as: "user", model: UserModel, required: false }],
                required: false,
              },
            ],
          },
        ],
      });
      logger.debug("Answer model: ", JSON.stringify(answer));
      return answer;
    } catch (e) {
      logger.error(`AnswerRepository.getById(${id}): `, e);
    }
  }

  return { getById };
}

export default AnswerRepository();
