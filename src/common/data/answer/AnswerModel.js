import { dbInstance } from "../database";
import SuperModel from "../SuperModel";
import { Sequelize } from "sequelize";

export default dbInstance.define(
  "answers",
  SuperModel({
    mp4VideoStatus: {
      field: "mp4_video_status",
      type: Sequelize.STRING,
      allowNull: true,
    },
    ogvVideoStatus: {
      field: "ogv_video_status",
      type: Sequelize.STRING,
      allowNull: true,
    },
    webmVideoStatus: {
      field: "webm_video_status",
      type: Sequelize.STRING,
      allowNull: true,
    },
    videoCompletionCount: {
      field: "video_completion_count",
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    videoCompletionNotified: {
      field: "video_completion_notified",
      type: Sequelize.BOOLEAN,
      allowNull: true,
    },

    imageUrl: { field: "image_url", type: Sequelize.STRING, allowNull: false },
    videoUrl: { field: "video_url", type: Sequelize.STRING, allowNull: false },
    videoUrls: { field: "video_urls", type: Sequelize.JSON, allowNull: false },

    videoDuration: { field: "video_duration", type: Sequelize.FLOAT },
    subtitles: { field: "subtitles", type: Sequelize.JSON },
    subtitlesSpeed: { field: "subtitles_speed", type: Sequelize.INTEGER },
    subtitlesTranslations: {
      field: "subtitles_translations",
      type: Sequelize.JSONB,
    },

    userId: { field: "user_id", type: Sequelize.BIGINT, allowNull: false },
    clientId: { field: "client_id", type: Sequelize.BIGINT, allowNull: false },
    questionId: {
      field: "question_id",
      type: Sequelize.BIGINT,
      allowNull: false,
    },

    isDraft: { field: "is_draft", type: Sequelize.BOOLEAN },
    isApproved: { field: "is_approved", type: Sequelize.BOOLEAN },
    isDenied: { field: "is_denied", type: Sequelize.BOOLEAN },
    isShared: { field: "is_shared", type: Sequelize.BOOLEAN },
  }),
  { timestamps: true }
);
