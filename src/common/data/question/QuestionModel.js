import { dbInstance } from "../database";
import SuperModel from "../SuperModel";
import { Sequelize } from "sequelize";

export default dbInstance.define(
  "questions",
  SuperModel({
    text: {
      field: "text",
      type: Sequelize.STRING,
      allowNull: false,
      unique: true,
    },
    category: { field: "category", type: Sequelize.STRING, allowNull: false },

    suggestedNotes: { field: "suggested_notes", type: Sequelize.JSON },

    categoryIcon: {
      field: "category_icon",
      type: Sequelize.STRING,
      allowNull: true,
    },

    clientId: {
      field: "client_id",
      type: Sequelize.BIGINT,
      references: { model: "clients", key: "id" },
    },
    userId: { field: "user_id", type: Sequelize.BIGINT, allowNull: false },

    isApproved: { field: "is_approved", type: Sequelize.BOOLEAN },
    isDenied: { field: "is_denied", type: Sequelize.BOOLEAN },
    isAnswered: { field: "is_answered", type: Sequelize.BOOLEAN },
    isShared: { field: "is_shared", type: Sequelize.BOOLEAN },

    originalLanguage: { field: "original_language", type: Sequelize.STRING },
    translations: { field: "translations", type: Sequelize.JSONB },
  }),
  { timestamps: true }
);
