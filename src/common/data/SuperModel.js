import { Sequelize } from "sequelize";

export default function SuperModel(modelAttributes) {
  const globalAttributes = {
    id: {
      field: "id",
      type: Sequelize.BIGINT,
      primaryKey: true,
      autoIncrement: true,
    },
    createdAt: { field: "created_at", type: Sequelize.DATE, allowNull: false },
    updatedAt: { field: "updated_at", type: Sequelize.DATE, allowNull: false },
    enabled: { field: "enabled", type: Sequelize.BOOLEAN, allowNull: true },
  };

  return Object.assign(globalAttributes, modelAttributes);
}
