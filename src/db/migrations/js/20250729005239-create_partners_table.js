'use strict';

const globals = require( '../_globals' );

export default {
	up: ( queryInterface, Sequelize ) => {
		return queryInterface.sequelize.transaction( t => {
			return Promise.all( [
				queryInterface.createTable( 'partners',
					globals.modelSuper( Sequelize, {
						apiEnabled: { field: 'api_enabled', type: Sequelize.BOOLEAN, allowNull: false, defaultValue: false },
						name: { field: 'name', type: Sequelize.STRING, allowNull: false },
						clientIds: { field: 'client_ids', type: Sequelize.ARRAY( Sequelize.BIGINT ), allowNull: true },
						features: { field: 'features', type: Sequelize.ARRAY( Sequelize.STRING ), allowNull: true },
						apiKeyIds: { field: 'api_key_ids', type: Sequelize.ARRAY( Sequelize.BIGINT ), allowNull: true },
						enabled: { field: 'enabled', type: Sequelize.BOOLEAN, allowNull: false, defaultValue: true }
					}
					), { transaction: t } )
			] );
		} );
	},

	down: ( queryInterface, Sequelize ) => {
		return queryInterface.sequelize.transaction( t => {
			return Promise.all( [
				queryInterface.dropTable( 'partners', { transaction: t } )
			] );
		} );
	}
};
