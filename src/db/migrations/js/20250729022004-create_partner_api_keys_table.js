'use strict';

const globals = require( '../_globals' );

export default {
	up: ( queryInterface, Sequelize ) => {
		return queryInterface.sequelize.transaction( t => {
			return Promise.all( [
				queryInterface.createTable( 'partner_api_keys',
					globals.modelSuper( Sequelize, {
						key: { field: 'key', type: Sequelize.STRING, allowNull: false, unique: true },
						keySample: { field: 'key_sample', type: Sequelize.STRING(4), allowNull: false },
						clientId: { field: 'client_id', type: Sequelize.BIGINT, allowNull: false, references: { model: { tableName: 'clients' }, key: 'id' } },
						partnerId: { field: 'partner_id', type: Sequelize.BIGINT, allowNull: false, references: { model: { tableName: 'partners' }, key: 'id' } }
					}
					), { transaction: t } )
			] );
		} );
	},

	down: ( queryInterface, Sequelize ) => {
		return queryInterface.sequelize.transaction( t => {
			return Promise.all( [
				queryInterface.dropTable( 'partner_api_keys', { transaction: t } )
			] );
		} );
	}
};
