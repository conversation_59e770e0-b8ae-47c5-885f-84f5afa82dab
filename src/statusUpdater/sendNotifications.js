import logger from "../common/utils/logger";
import AnswerRepository from "../common/data/answer/AnswerRepository";
import invoke<PERSON>ambda from "../common/invokeLambda";
import QuestionShareModel from "../common/data/questionShare/QuestionShareModel";
import UserModel from "../common/data/user/UserModel";

async function sendNotifications(answerId) {
  logger.info(`Fetching answer model: ${answerId}`);
  const answer = await AnswerRepository.getById(answerId);

  logger.debug("Fetched answer model", JSON.stringify(answer));

  logger.info(`Fetching questionShare model: ${answerId}`);
  const questionShare = await QuestionShareModel.findOne({
    where: {
      questionId: answer.questionId,
      isAnswered: false,
    },
    include: [
      {
        as: "user",
        model: UserModel,
        required: false,
      },
    ],
  });
  logger.debug("Fetched questionShare model", JSON.stringify(questionShare));
  logger.info(
    `Invoking lambda for sending completion emails for video ID: ${answerId}`
  );
  if (answer.question.isShared && answer.question.questionShare) {
    return invokeLambda({
      name: "email_sender",
      payload: {
        templateName: "answeredSharedQuestion",
        payload: { answer, questionShare },
      },
    });
  } else {
    return invokeLambda({
      name: "email_sender",
      payload: { templateName: "answeredQuestion", payload: answer },
    });
  }
}

export default sendNotifications;
