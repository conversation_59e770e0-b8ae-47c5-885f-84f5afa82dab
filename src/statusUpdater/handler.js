import updateVideoStatus from "./updateVideoStatus";
import "../common/data/associations";
import sendNotifications from "./sendNotifications";

export function handler(event, context) {
  const {
    body: { format, videoKey, status, publish },
  } = event;

  if (publish) {
    const { answerId } = publish;
    return sendNotifications(answerId);
  }
  return updateVideoStatus({ format, videoKey, status });
}
