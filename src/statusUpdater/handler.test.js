import { handler } from "./handler";
import updateVideoStatus from "./updateVideoStatus";
import sendNotifications from "./sendNotifications";

jest.mock("./updateVideoStatus");
jest.mock("./sendNotifications");

describe("handler", () => {
  const context = {}; // Mock context if needed

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("should call sendNotifications when publish is defined", () => {
    const event = {
      body: {
        format: "mp4",
        videoKey: "video123",
        status: "completed",
        publish: {
          answerId: "answer123",
        },
      },
    };

    handler(event, context);

    expect(sendNotifications).toHaveBeenCalledWith("answer123");
    expect(updateVideoStatus).not.toHaveBeenCalled();
  });

  test("should call updateVideoStatus when publish is not defined", () => {
    const event = {
      body: {
        format: "mp4",
        videoKey: "video123",
        status: "completed",
        publish: null,
      },
    };

    handler(event, context);

    expect(updateVideoStatus).toHaveBeenCalledWith({
      format: "mp4",
      videoKey: "video123",
      status: "completed",
    });
    expect(sendNotifications).not.toHaveBeenCalled();
  });

  test("should call updateVideoStatus when publish is undefined", () => {
    const event = {
      body: {
        format: "mp4",
        videoKey: "video123",
        status: "completed",
      },
    };

    handler(event, context);

    expect(updateVideoStatus).toHaveBeenCalledWith({
      format: "mp4",
      videoKey: "video123",
      status: "completed",
    });
    expect(sendNotifications).not.toHaveBeenCalled();
  });
});
