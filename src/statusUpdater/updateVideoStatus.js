import env from "../common/env";
import { Pool } from "pg";
import logger from "../common/utils/logger";

const { db: dbConfig } = env;
const pool = new Pool(dbConfig);

const MAX_RETRIES = 3;

async function updateVideoStatus({ format, videoKey, status }) {
  if (!format || !videoKey || !status) {
    logger.info("400 bad data: ", JSON.stringify({ format, videoKey, status }));
    return;
  }

  logger.info(
    `${format} - Started video status update request..`,
    JSON.stringify({ format, videoKey, status })
  );
  const client = await pool.connect();

  const bucketUrl = "https://files.repd.us/video_parts";
  const mp4Url = `${bucketUrl}/${videoKey}.mp4`;
  
  // Extract the base videoKey without extension for more flexible matching
  const baseVideoKey = videoKey.replace(/\.(mp4|webm|ogv)$/, '');
  
  // Create multiple patterns to match different URL formats in the database
  const queryText = `
    UPDATE answers 
    SET ${format}_video_status = $1, video_completion_count = video_completion_count + 1 
    WHERE video_urls->>\'mp4\' = $2 
    OR video_url ILIKE '%${baseVideoKey}%' 
    OR video_urls::text ILIKE '%${baseVideoKey}%'
    OR video_urls::text ILIKE '%${baseVideoKey}.mp4%'
    OR video_urls->>\'mp4\' ILIKE '%${baseVideoKey}%'
    RETURNING video_completion_count, video_completion_notified, id;
  `;

  // Log the actual values being used in the query for debugging
  logger.info(`Searching for video with key: ${baseVideoKey}`);
  logger.info(`Full mp4Url being searched: ${mp4Url}`);

  let shouldNotify = false;
  let answerId = undefined;

  console.log("queryText", queryText);

  try {
    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      logger.debug(
        `${format} - Attempt ${attempt} to update ${format} video status`
      );

      try {
        await client.query("BEGIN ISOLATION LEVEL SERIALIZABLE;");

        const updateResult = await client.query(queryText, [status, mp4Url]);

        logger.info("updateResult.rows", updateResult.rows);

        if (updateResult.rows.length === 0) {
          logger.warn(
            `${format} - No rows updated. Check if the video URL matches the database entry.`,
            JSON.stringify({ mp4Url })
          );
          await client.query("ROLLBACK;");
          return;
        }

        let video_completion_count, video_completion_notified, id;
        if (updateResult?.rows?.length > 0) {
          logger.info("updateResult.rows", updateResult.rows);

          ({ video_completion_count, video_completion_notified, id } = updateResult.rows[0]);
        } else {
          logger.error(
            `${format} - No rows returned. Check if the video URL matches the database entry.`,
            JSON.stringify({ mp4Url })
          );

          await client.query("ROLLBACK;");

          return;
        }

        answerId = id;

        logger.debug(
          `${format} - Update result: `,
          JSON.stringify({
            videoCompletionCount: video_completion_count,
            videoCompletionNotified: video_completion_notified,
            answerId,
          })
        );

        await client.query("COMMIT;");

        logger.info(
          `${format} - Query was successful at attempt ${attempt}. Will break.......`
        );
        break;
      } catch (error) {
        await client.query("ROLLBACK;");
        logger.error(`${format} - Attempt ${attempt} failed`);
        if (attempt === MAX_RETRIES) throw error;
      }
    }
  } finally {
    logger.info(`${format} - Release client`);
    client.release();
  }
}

export default updateVideoStatus;
