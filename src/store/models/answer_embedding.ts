import { DataTypes, Model } from "sequelize";
import { sequelize } from "@/store/instance";

export interface AnswerEmbeddingAttributes {
  id: number;
  supabaseId: number;
  question?: string;
  // question_embedding is managed by direct SQL
  transcription?: string;
  // transcriptionEmbedding?: number[];
  // questionEmbedding?: number[];
  // transcription_embedding is managed by direct SQL
  repdAnswerId?: number;
  repdClientId?: number;
  questionEs?: string;
  // question_es_embedding is managed by direct SQL
  transcriptionEs?: string;
  // transcription_es_embedding is managed by direct SQL
  createdAt?: Date;
  updatedAt?: Date;
}

export type AnswerEmbeddingModel = Model<AnswerEmbeddingAttributes, Partial<AnswerEmbeddingAttributes>>;

export const AnswerEmbedding = sequelize.define<AnswerEmbeddingModel>(
  "answer_embedding",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    supabaseId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      unique: true,
      field: "supabase_id",
    },
    question: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    // Vector embeddings are managed by direct SQL
    // questionEmbedding: {
    //   type: DataTypes.TEXT,
    //   allowNull: true,
    //   field: "question_embedding",
    // },
    // transcriptionEmbedding: {
    //   type: DataTypes.TEXT,
    //   allowNull: true,
    //   field: "transcription_embedding",
    // },
    transcription: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    repdAnswerId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: "repd_answer_id",
    },
    repdClientId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: "repd_client_id",
    },
    questionEs: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: "question_es",
    },
    transcriptionEs: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: "transcription_es",
    },
    createdAt: {
      type: DataTypes.DATE,
      field: "created_at",
    },
    updatedAt: {
      type: DataTypes.DATE,
      field: "updated_at",
      defaultValue: DataTypes.NOW,
    },
  },
  {
    schema: "public",
    tableName: "answer_embeddings",
    timestamps: false,
  }
);