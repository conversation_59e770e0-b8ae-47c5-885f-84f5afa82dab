import AnswerModel from "../../common/data/answer/AnswerModel";
import UserModel from "../../common/data/user/UserModel";
import QuestionModel from "../../common/data/question/QuestionModel";
import logger from "../../common/utils/logger";
import { Op } from "sequelize";

function AnswerService() {
  const defaultInclude = [
    {
      as: "question",
      model: QuestionModel,
      isAnswered: false,
      enabled: true,
      required: false,
      include: [{ as: "user", model: UserModel, required: false }],
    },
  ];
  const create = async (data) => {
    return AnswerModel.create(data);
  };

  const get = async (id) => {
    return AnswerModel.findOne(id);
  };

  const getAll = async ({ clientId, excludeIds }) => {
    logger.debug(
      `Answer service - getAll - clientId: ${clientId} - excludeIds: ${excludeIds}`
    );

    return AnswerModel.findAll({
      where: {
        id: {
          [Op.notIn]: excludeIds || [],
        },
        client_id: {
          [Op.eq]: clientId,
        },
      },
      include: defaultInclude,
      // limit: 50,
    });
  };

  return {
    create,
    get,
    getAll,
  };
}

export default AnswerService();
