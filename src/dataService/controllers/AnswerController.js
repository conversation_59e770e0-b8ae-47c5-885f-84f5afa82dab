import AnswerService from "../services/AnswerService.js";
import {
  internalErrorResponse,
  successResponse,
} from "../../common/utils/responseUtil.js";
import logger from "../../common/utils/logger";

function AnswerController() {
  const create = async (data) => {
    try {
      const result = await AnswerService.create(data);
      return successResponse(result);
    } catch (error) {
      logger.error(JSON.stringify(error));
      return internalErrorResponse();
    }
  };

  const get = async (data) => {
    try {
      const result = await AnswerService.get(data.id);
      return successResponse(result);
    } catch (error) {
      logger.error(JSON.stringify(error));
      return internalErrorResponse();
    }
  };

  const getAll = async (data) => {
    logger.debug(`Answer controller - getAll: ${JSON.stringify(data)}`);

    try {
      const { clientId, excludeIds } = data;

      const result = await AnswerService.getAll({ clientId, excludeIds });

      return successResponse(result);
    } catch (error) {
      logger.error(JSON.stringify(error));
      return internalErrorResponse();
    }
  };

  return {
    get,
    create,
    getAll,
  };
}

export default AnswerController();
