import {
  internalErrorResponse,
  successResponse,
} from "../../common/utils/responseUtil.js";
import logger from "../../common/utils/logger";
import ClientModel from "../../common/data/client/ClientModel";

function ClientController() {
  const getAll = async (data) => {
    logger.debug(`Client controller - getAll: ${JSON.stringify(data)}`);

    try {
      const result = await ClientModel.findAll();
      return successResponse(result);
    } catch (error) {
      logger.error(JSON.stringify(error));
      return internalErrorResponse();
    }
  };

  return {
    getAll,
  };
}

export default ClientController();
