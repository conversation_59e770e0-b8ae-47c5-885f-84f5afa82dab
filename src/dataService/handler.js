import "../common/data/associations";
import logger from "../common/utils/logger";
import route from "./routes";
import { corsHeaders } from "../common/utils/responseUtil";

export async function handler(event) {
  // Handle OPTIONS request for CORS preflight
  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: "",
    };
  }

  logger.debug("Received event: ", event);

  return await route(event);
}
