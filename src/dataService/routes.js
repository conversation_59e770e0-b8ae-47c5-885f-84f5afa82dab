import AnswerController from "./controllers/AnswerController.js";
import {
  methodNotAllowedResponse,
  notFoundResponse,
} from "../common/utils/responseUtil";
import logger from "../common/utils/logger";
import ClientController from "./controllers/ClientController";

const route = async (event) => {
  const eventObject = typeof (event.body || event || {}) === "string" ? JSON.parse(event.body || event || {}) : event.body || event || {};

  const { resource, payload, method } = eventObject;
  logger.debug(JSON.stringify({ resource, payload, method }));

  switch (resource) {
    case "/answers":
      switch (method.toUpperCase()) {
        case "POST":
          return AnswerController.create(payload);
        case "GET":
          return AnswerController.getAll(payload);
        default:
          return methodNotAllowedResponse();
      }
    case "/clients":
      switch (method.toUpperCase()) {
        case "GET":
          return ClientController.getAll();
        default:
          return methodNotAllowedResponse();
      }
    default:
      return notFoundResponse();
  }
};

export default route;
