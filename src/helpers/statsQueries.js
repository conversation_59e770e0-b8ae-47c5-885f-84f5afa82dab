import moment from 'moment/moment.js';

// Filter out City of Tulare IPs
const statExclusionFilter = (prefix = '') => `
	${prefix}ipa NOT ILIKE '%**************%' AND ${prefix}ipa NOT ILIKE '%**************%'
`;

// Filter out Arlington (211) for super admins
const clientExclusionFilter = (prefix = '') => `
	${prefix}client_id NOT IN (211)
`;

// Filter out disabled clients
const enabledClientFilter = (prefix = 'c') => `
	${prefix}.enabled = true AND ${prefix}.is_locked = false
`;

// Filter out specific embed stat events
const embedStatExclusionFilter = (prefix = '') => `
	NOT (
		(${prefix}event_action IN ('module_close', 'font_size_change', 'language_change') AND ${prefix}host = 'embed.repd.us' AND ${prefix}event_action IS NOT NULL AND ${prefix}host IS NOT NULL) OR
		(${prefix}event_category = 'Auth' AND ${prefix}host = 'embed.repd.us' AND ${prefix}host IS NOT NULL AND ${prefix}event_category IS NOT NULL) OR
		(${prefix}event_label = 'Marvin Embed View' AND ${prefix}event_label IS NOT NULL) OR
    (${prefix}host = 'admin.repd.us' AND ${prefix}host IS NOT NULL)
	)
`;

export const mailStatsQuery = `
	SELECT
		emails.*
	FROM
		ngp_van_emails emails
	LEFT JOIN
		ngp_van_saved_lists saved_lists
	ON
		emails.saved_list_name::TEXT = saved_lists.saved_list_name::TEXT
	WHERE
		saved_lists.client_id = :clientId
`;

// export const statsQuery = (clientId) => `
// 	SELECT * FROM looker_studio.precomputed_stat_events_${clientId} c
// 	WHERE TO_TIMESTAMP(event_created, 'Mon DD, YYYY') BETWEEN :startDate AND :endDate
// `;

export const analyticsQuery = (clientId, location) => clientId ? `
  SELECT *
  FROM mv_analytics_data
  WHERE client_id = :clientId
  AND ${statExclusionFilter()}
  AND ${embedStatExclusionFilter()}
  AND created_at BETWEEN :startDate AND :endDate
` : `
  WITH mv AS (
      SELECT *
      FROM   mv_analytics_data
      WHERE  created_at BETWEEN :startDate AND :endDate
        AND  ${statExclusionFilter()}
        AND  ${embedStatExclusionFilter()}
        AND  ${clientExclusionFilter()}
        AND  client_id IN (
              SELECT id
              FROM   clients c
              WHERE  ${enabledClientFilter()}
              ${location.length ? `AND state IN (${location.map(loc => db.raw('?', [loc])).join(', ')})` : ''}
            )
  )
  SELECT * FROM mv
  UNION ALL
  SELECT * FROM stats.stat_events
  WHERE  NOT EXISTS (SELECT 1 FROM mv)
    AND  created_at BETWEEN :startDate AND :endDate
    AND  ${statExclusionFilter()}
    AND  ${embedStatExclusionFilter()}
    AND  ${clientExclusionFilter()}
    AND  client_id IN (
          SELECT id
          FROM   clients c
          WHERE  ${enabledClientFilter()}
          ${location.length ? `AND state IN (${location.map(loc => db.raw('?', [loc])).join(', ')})` : ''}
        );
`;

export const aiQueriesQuery = (clientId, location) => clientId ? `
  SELECT
    DATE_TRUNC('day', created_at) AS day,
    COUNT(*) AS amount
  FROM
    ai_queries
  WHERE
    ai_queries.client_id = :clientId AND
    ai_queries.created_at BETWEEN :startDate AND :endDate
  GROUP BY
    day
  ORDER BY
    day;
` : `
  SELECT
    DATE_TRUNC('day', ai_queries.created_at) AS day,
    COUNT(*) AS amount
  FROM
    ai_queries
  JOIN
    clients c ON ai_queries.client_id = c.id
  WHERE
    ${location.length ? `c.state IN (${location.map(loc => `'${loc}'`).join(', ')}) AND` : ''}
    ai_queries.created_at BETWEEN :startDate AND :endDate
    AND ${clientExclusionFilter('ai_queries.')}
    AND ${enabledClientFilter()}
  GROUP BY
    day
  ORDER BY
    day;
`;

export const sentimentQuery = (clientId, location) => clientId ? `
	SELECT
		date_trunc('day', created_at) AS date,
		SUM(CASE WHEN sentiment = 1 THEN 1 ELSE 0 END) AS happy,
		SUM(CASE WHEN sentiment = 2 THEN 1 ELSE 0 END) AS neutral,
		SUM(CASE WHEN sentiment = 3 THEN 1 ELSE 0 END) AS unhappy
	FROM (
		SELECT created_at, sentiment FROM questions WHERE client_id = :clientId
	UNION ALL
		SELECT created_at, sentiment FROM ai_queries WHERE client_id = :clientId
	) AS combined
	WHERE created_at BETWEEN :startDate AND :endDate
	GROUP BY date
	ORDER BY date;
` : `
	SELECT
		date_trunc('day', created_at) AS date,
		SUM(CASE WHEN sentiment = 1 THEN 1 ELSE 0 END) AS happy,
		SUM(CASE WHEN sentiment = 2 THEN 1 ELSE 0 END) AS neutral,
		SUM(CASE WHEN sentiment = 3 THEN 1 ELSE 0 END) AS unhappy
	FROM (
		SELECT q.created_at, q.sentiment FROM questions q
		JOIN clients c ON q.client_id = c.id
	WHERE q.created_at BETWEEN :startDate AND :endDate
		${location.length ? `AND c.state IN (${location.map(loc => `'${loc}'`).join(', ')})` : ''}
        AND ${clientExclusionFilter('q.')}
        AND ${enabledClientFilter()}
	UNION ALL
		SELECT a.created_at, a.sentiment FROM ai_queries a
	JOIN clients c ON a.client_id = c.id
	WHERE a.created_at BETWEEN :startDate AND :endDate
		${location.length ? `AND c.state IN (${location.map(loc => `'${loc}'`).join(', ')})` : ''}
        AND ${clientExclusionFilter('a.')}
        AND ${enabledClientFilter()}
	) AS combined
	GROUP BY date
	ORDER BY date;
`;

export const aiQuerySentimentQuery = (clientId, location) => clientId ? `
	SELECT
		date_trunc('day', created_at) AS date,
		SUM(CASE WHEN sentiment = 1 THEN 1 ELSE 0 END) AS happy,
		SUM(CASE WHEN sentiment = 2 THEN 1 ELSE 0 END) AS neutral,
		SUM(CASE WHEN sentiment = 3 THEN 1 ELSE 0 END) AS unhappy
	FROM (
		SELECT created_at, sentiment FROM ai_queries WHERE client_id = :clientId
	) AS combined
	WHERE created_at BETWEEN :startDate AND :endDate
	GROUP BY date
	ORDER BY date;
` : `
	SELECT
		date_trunc('day', created_at) AS date,
		SUM(CASE WHEN sentiment = 1 THEN 1 ELSE 0 END) AS happy,
		SUM(CASE WHEN sentiment = 2 THEN 1 ELSE 0 END) AS neutral,
		SUM(CASE WHEN sentiment = 3 THEN 1 ELSE 0 END) AS unhappy
	FROM (
		SELECT a.created_at, a.sentiment FROM ai_queries a
	JOIN clients c ON a.client_id = c.id
	WHERE a.created_at BETWEEN :startDate AND :endDate
		${location.length ? `AND c.state IN (${location.map(loc => `'${loc}'`).join(', ')})` : ''}
        AND ${clientExclusionFilter('a.')}
        AND ${enabledClientFilter()}
	) AS combined
	GROUP BY date
	ORDER BY date;
`;

export const questionsStatsQuery = (clientId, location, startDate) => clientId ? `
    WITH config AS (SELECT 'dfaa912cce5f5e3e7f88137a74cfacf635670b6be12e596cdce1a2816439d8b4' AS key)
    SELECT
      category,
      COUNT(*) FILTER (WHERE created_at BETWEEN :startDate AND :endDate) AS current_period_count,
      COUNT(*) FILTER (WHERE created_at BETWEEN '${moment(startDate).subtract(3, 'months').toISOString()}' AND :startDate) AS previous_period_count,
      AVG(sentiment) AS sentiment,
      (
        SELECT json_agg(json_build_object('id', sub_q.id, 'text', sub_q.text, 'email', decrypt_data(u.email, (SELECT key FROM config)), 'created_at', sub_q.created_at))
        FROM (
          SELECT q.id, q.text, q.user_id, q.created_at, q.category
          FROM questions q
          LEFT JOIN answers a ON q.id = a.question_id
          WHERE q.client_id = :clientId AND q.category = q_outer.category AND a.question_id IS NULL AND q.created_at BETWEEN :startDate AND :endDate
          UNION ALL
          SELECT ai.id, ai.query, ai.user_id, ai.created_at, ai.category
          FROM ai_queries ai
          WHERE ai.client_id = :clientId AND ai.category = q_outer.category AND ai.created_at BETWEEN :startDate AND :endDate
          ORDER BY created_at DESC
        ) sub_q
        LEFT JOIN users u ON sub_q.user_id = u.id
      ) AS questions
    FROM (
      SELECT q.created_at, q.sentiment, q.category, q.client_id FROM questions q
      LEFT JOIN answers a ON q.id = a.question_id
      WHERE q.client_id = :clientId AND a.question_id IS NULL
      UNION ALL
      SELECT created_at, sentiment, category, client_id FROM ai_queries WHERE client_id = :clientId
    ) q_outer
    WHERE
      q_outer.client_id = :clientId
    GROUP BY
      category;
` : `
    WITH config AS (SELECT 'dfaa912cce5f5e3e7f88137a74cfacf635670b6be12e596cdce1a2816439d8b4' AS key)
    SELECT
      category,
      COUNT(*) FILTER (WHERE created_at BETWEEN :startDate AND :endDate) AS current_period_count,
      COUNT(*) FILTER (WHERE created_at BETWEEN '${moment(startDate).subtract(3, 'months').toISOString()}' AND :startDate) AS previous_period_count,
      AVG(sentiment) AS sentiment,
      (
        SELECT json_agg(json_build_object('id', sub_q.id, 'text', sub_q.text, 'email', decrypt_data(u.email, (SELECT key FROM config)), 'created_at', sub_q.created_at))
        FROM (
          SELECT q.id, q.text, q.user_id, q.created_at, q.category
          FROM questions q
          LEFT JOIN answers a ON q.id = a.question_id
          JOIN clients c ON q.client_id = c.id
          WHERE q.category = q_outer.category AND a.question_id IS NULL
          AND q.created_at BETWEEN :startDate AND :endDate
          AND ${enabledClientFilter()}
          UNION ALL
          SELECT a.id, a.query, a.user_id, a.created_at, a.category
          FROM ai_queries a
          JOIN clients c ON a.client_id = c.id
          WHERE a.category = q_outer.category
          AND a.created_at BETWEEN :startDate AND :endDate
          AND ${enabledClientFilter()}
          ORDER BY created_at DESC
        ) sub_q
        JOIN users u ON sub_q.user_id = u.id
      ) AS questions
    FROM (
      SELECT q.created_at, q.sentiment, q.category FROM questions q
      JOIN clients c ON q.client_id = c.id
      LEFT JOIN answers a ON q.id = a.question_id
      WHERE q.created_at BETWEEN :startDate AND :endDate AND a.question_id IS NULL
      ${location.length ? `AND c.state IN (${location.map(loc => `'${loc}'`).join(', ')})` : ''}
      AND ${clientExclusionFilter('q.')}
      AND ${enabledClientFilter()}
      UNION ALL
      SELECT a.created_at, a.sentiment, a.category FROM ai_queries a
      JOIN clients c ON a.client_id = c.id
      WHERE a.created_at BETWEEN :startDate AND :endDate
      ${location.length ? `AND c.state IN (${location.map(loc => `'${loc}'`).join(', ')})` : ''}
      AND ${clientExclusionFilter('a.')}
      AND ${enabledClientFilter()}
    ) q_outer
    GROUP BY
      category;
`;

export const trendingStatsQuery = (clientId, location) => clientId ? `
	SELECT * FROM (
		SELECT
			'questions' AS source,
			category,
			COUNT(*) AS question_count,
			AVG(sentiment) AS average_sentiment
		FROM
			questions
		WHERE
			client_id = :clientId AND
			created_at BETWEEN :startDate AND :endDate
		GROUP BY
			category
		ORDER BY
			question_count DESC
	) AS questions_data
	UNION ALL
	SELECT * FROM (
		SELECT
			'ai_queries' AS source,
			category,
			COUNT(*) AS question_count,
			AVG(sentiment) AS average_sentiment
		FROM
			ai_queries
		WHERE
			client_id = :clientId AND
			created_at BETWEEN :startDate AND :endDate
		GROUP BY
			category
		ORDER BY
			question_count DESC
	) AS ai_queries_data;
` : `
	SELECT * FROM (
		SELECT
			'questions' AS source,
			category,
			COUNT(*) AS question_count,
			AVG(sentiment) AS average_sentiment
		FROM
			questions q
		JOIN clients c ON q.client_id = c.id
		WHERE
			q.created_at BETWEEN :startDate AND :endDate
			${location.length ? `AND c.state IN (${location.map(loc => `'${loc}'`).join(', ')})` : ''}
      AND ${clientExclusionFilter('q.')}
      AND ${enabledClientFilter()}
		GROUP BY
			category
		ORDER BY
			question_count DESC
	) AS questions_data
	UNION ALL
	SELECT * FROM (
		SELECT
			'ai_queries' AS source,
			category,
			COUNT(*) AS question_count,
			AVG(sentiment) AS average_sentiment
		FROM
			ai_queries a
		JOIN clients c ON a.client_id = c.id
		WHERE
			a.created_at BETWEEN :startDate AND :endDate
			${location.length ? `AND c.state IN (${location.map(loc => `'${loc}'`).join(', ')})` : ''}
      AND ${clientExclusionFilter('a.')}
      AND ${enabledClientFilter()}
		GROUP BY
			category
		ORDER BY
			question_count DESC
	) AS ai_queries_data;
`;

export const emailsStatsQuery = (clientId, location) => clientId ? `
	SELECT
		SUM(opens) AS opens,
		SUM(cta) AS clicks,
		SUM(delivered) AS deliveries
	FROM
		public.ngp_van_emails
	WHERE
		saved_list_id::TEXT IN (
			SELECT
				saved_list_id::TEXT
			FROM
				ngp_van_saved_lists
			WHERE
				client_id = :clientId
		)
	AND created_at BETWEEN :startDate AND :endDate;
` : `
	SELECT
		SUM(opens) AS opens,
		SUM(cta) AS clicks,
		SUM(delivered) AS deliveries
	FROM
		public.ngp_van_emails
	WHERE
		saved_list_id::TEXT IN (
			SELECT
				ngp_van_saved_lists.saved_list_id::TEXT
			FROM
				ngp_van_saved_lists
			JOIN
				clients c ON ngp_van_saved_lists.client_id = c.id
			WHERE
        ${clientExclusionFilter('ngp_van_saved_lists.')} AND
        ${enabledClientFilter()} AND
				${location.length ? `c.state IN (${location.map(loc => `'${loc}'`).join(', ')})` : 'TRUE'}
		)
	AND created_at BETWEEN :startDate AND :endDate;
`;

// export const oldBulkSendsStatsQuery = (clientId, location) => clientId ? `
//   WITH
//     averages AS (
//       SELECT
//         AVG(delivered) AS avg_delivered,
//         AVG(opens) AS avg_opens
//       FROM
//         public.ngp_van_emails
//       LEFT JOIN clients c ON ngp_van_emails.client_id = c.id
//       WHERE
//         saved_list_id IS NOT NULL
//         AND c.enabled = true
//     ),
//     email_data AS (
//       SELECT
//         e.client_id,
//         e.saved_list_id,
//         e.subject,
//         e.last_event_time::DATE as created_at,
//         SUM(e.delivered) AS delivered,
//         SUM(e.opens) AS opens,
//         SUM(e.cta) AS clicks,
//         SUM(e.unsubscribes) AS unsubscribes,
//         SUM(e.dropped) AS blocks
//       FROM
//         public.ngp_van_emails e
//         JOIN clients c ON e.client_id = c.id
//       WHERE
//         e.saved_list_id IS NOT NULL
//         AND c.id = :clientId
//         AND e.last_event_time BETWEEN :startDate AND :endDate
//       GROUP BY
//         e.client_id, e.last_event_time::DATE, e.saved_list_id, e.subject
//     )
//   SELECT
//     e.created_at::DATE,
//     sl.saved_list_name,
//     sl.users_count,
//     e.subject,
//     a.id,
//     a.video_url AS "videoUrl",
//     a.image_url AS "thumbnailUrl",
//     q.text AS title,
//     q.category,
//     q.created_at::DATE AS question_created_at,
//     e.delivered,
//     e.opens,
//     e.clicks,
//     e.unsubscribes,
//     e.blocks,
//     (SELECT COUNT(*) FROM stats.stat_events WHERE event_action = 'setVideo' AND answer_id = a.question_id) AS views,
//     (avg_opens / NULLIF(avg_delivered, 0)) * 100 AS "avgOpenRate"
//   FROM
//     email_data e
//     JOIN public.ngp_van_saved_lists sl ON e.saved_list_id::TEXT = sl.saved_list_id::TEXT
//     LEFT JOIN public.ngp_van_export_requests er ON e.saved_list_id::TEXT = er.saved_list_id::TEXT
//     LEFT JOIN public.answers a ON er.answer_id = a.id
//     LEFT JOIN public.questions q ON a.question_id = q.id,
//     averages
//   WHERE
//     sl.client_id = :clientId AND
//     e.subject is not null AND
//     e.subject != '' AND
//     e.delivered > 100
//   ORDER BY
//     e.created_at::DATE DESC;
// ` : `
//   WITH
//   averages AS (
//     SELECT
//       AVG(delivered) AS avg_delivered,
//       AVG(opens) AS avg_opens
//     FROM
//       public.ngp_van_emails
//     LEFT JOIN clients c ON ngp_van_emails.client_id = c.id
//     WHERE
//       saved_list_id IS NOT NULL
//       AND c.enabled = true
//   ),
//   export_requests AS (
//     SELECT DISTINCT
//       er.created_at::DATE,
//       er.saved_list_id,
//       er.answer_id,
//       er.id AS export_request_id,
//       c.id AS client_id
//     FROM
//       public.ngp_van_export_requests er
//       JOIN public.ngp_van_saved_lists sl ON er.saved_list_id::TEXT = sl.saved_list_id::TEXT
//       JOIN clients c ON sl.client_id = c.id
//     WHERE
//       er.created_at::DATE BETWEEN :startDate AND :endDate
//       ${location.length ? `AND c.state IN (${location.map(loc => `'${loc}'`).join(', ')})` : ''}
//       AND ${clientExclusionFilter('sl.')}
//       AND ${enabledClientFilter()}
//   ),
//   email_data AS (
//     SELECT
//       e.saved_list_id,
//       e.subject,
//       er.export_request_id,
//       SUM(e.delivered) AS delivered,
//       SUM(e.opens) AS opens,
//       SUM(e.cta) AS clicks,
//       SUM(e.unsubscribes) AS unsubscribes,
//       SUM(e.dropped) AS blocks
//     FROM
//       public.ngp_van_emails e
//     JOIN (
//       SELECT
//         saved_list_id,
//         MIN(created_at) as first_export_date,
//         export_request_id
//       FROM export_requests
//       GROUP BY saved_list_id, export_request_id
//     ) er ON e.saved_list_id::TEXT = er.saved_list_id::TEXT
//     JOIN clients c ON e.client_id = c.id
//     WHERE
//       e.saved_list_id IS NOT NULL AND
//       e.last_event_time >= er.first_export_date
//       AND e.last_event_time <= er.first_export_date + INTERVAL '3 days'
//       ${location.length ? `AND c.state IN (${location.map(loc => `'${loc}'`).join(', ')})` : ''}
//       AND ${clientExclusionFilter('e.')}
//       AND ${enabledClientFilter()}
//     GROUP BY
//       e.saved_list_id, e.subject, er.export_request_id
//   )
//   SELECT
//     er.created_at::DATE,
//     sl.saved_list_name,
//     sl.users_count,
//     ed.subject,
//     a.id,
//     a.video_url AS "videoUrl",
//     a.image_url AS "thumbnailUrl",
//     q.text AS title,
//     q.category,
//     q.created_at::DATE AS question_created_at,
//     ed.delivered,
//     ed.opens,
//     ed.clicks,
//     ed.unsubscribes,
//     ed.blocks,
//     (SELECT COUNT(*) FROM stats.stat_events WHERE event_action = 'setVideo' AND answer_id = a.question_id AND ${statExclusionFilter()}) AS views,
//     (avg_opens / NULLIF(avg_delivered, 0)) * 100 AS "avgOpenRate"
//   FROM
//     export_requests er
//   JOIN public.ngp_van_saved_lists sl ON er.saved_list_id::TEXT = sl.saved_list_id::TEXT
//   LEFT JOIN email_data ed ON er.saved_list_id::TEXT = ed.saved_list_id::TEXT AND er.export_request_id = ed.export_request_id
//   LEFT JOIN public.answers a ON er.answer_id = a.id
//   LEFT JOIN public.questions q ON a.question_id = q.id,
//   averages
//   ORDER BY
//     er.created_at::DATE DESC;
// `;

export const bulkSendsStatsQuery = (clientId, location) => clientId ? `
  SELECT DISTINCT
    e.last_event_time::DATE as date,
    e.subject
  FROM
    public.ngp_van_emails e
    JOIN clients c ON e.client_id = c.id
    JOIN public.ngp_van_saved_lists sl ON e.saved_list_id::TEXT = sl.saved_list_id::TEXT
  WHERE
    e.saved_list_id IS NOT NULL
    AND c.id = :clientId
    AND e.last_event_time BETWEEN :startDate AND :endDate
    AND sl.client_id = :clientId
    AND e.subject IS NOT NULL
    AND e.subject != ''
  ORDER BY
    e.last_event_time::DATE DESC;
` : `
  SELECT DISTINCT
    e.last_event_time::DATE as date,
    e.subject
  FROM
    public.ngp_van_emails e
    JOIN clients c ON e.client_id = c.id
    JOIN public.ngp_van_saved_lists sl ON e.saved_list_id::TEXT = sl.saved_list_id::TEXT
  WHERE
    e.saved_list_id IS NOT NULL
    AND e.last_event_time BETWEEN :startDate AND :endDate
    ${location.length ? `AND c.state IN (${location.map(loc => `'${loc}'`).join(', ')})` : ''}
    AND ${clientExclusionFilter('sl.')}
    AND ${enabledClientFilter()}
    AND e.subject IS NOT NULL
    AND e.subject != ''
  ORDER BY
    e.last_event_time::DATE DESC;
`;

export const emailsChartStatsQuery = (clientId, location) => clientId ? `
	SELECT
		SUM(opens) AS opens,
		SUM(delivered) AS deliveries,
		DATE(created_at) AS created_at
	FROM public.ngp_van_emails
	WHERE saved_list_id::TEXT IN (
		SELECT saved_list_id::TEXT
		FROM ngp_van_saved_lists
		WHERE client_id = :clientId
	)
	AND created_at BETWEEN :startDate AND :endDate
	GROUP BY DATE(created_at)
	ORDER BY created_at ASC;
` : `
	SELECT
		SUM(opens) AS opens,
		SUM(delivered) AS deliveries,
		DATE(created_at) AS created_at
	FROM public.ngp_van_emails
	WHERE saved_list_id::TEXT IN (
		SELECT saved_list_id::TEXT
		FROM ngp_van_saved_lists
		JOIN clients c ON ngp_van_saved_lists.client_id = c.id
		WHERE
    ${clientExclusionFilter('ngp_van_saved_lists.')} AND
    ${enabledClientFilter()} AND
    ${location.length ? `c.state IN (${location.map(loc => `'${loc}'`).join(', ')})` : 'TRUE'}
	)
	AND created_at BETWEEN :startDate AND :endDate
	GROUP BY DATE(created_at)
	ORDER BY created_at ASC;
`;

export const engagementsChartStatsQuery = (clientId, location) => clientId ? `
	SELECT
		DATE(questions.created_at) AS created_at,
		sentiment,
		COUNT(*) AS count
	FROM questions
	WHERE client_id = :clientId
	AND questions.created_at BETWEEN :startDate AND :endDate
	GROUP BY DATE(questions.created_at), sentiment
	ORDER BY DATE(questions.created_at) ASC;
` : `
	SELECT
		DATE(q.created_at) AS created_at,
		sentiment,
		COUNT(*) AS count
	FROM questions q
	JOIN clients c ON q.client_id = c.id
	WHERE q.created_at BETWEEN :startDate AND :endDate
	${location.length ? `AND c.state IN (${location.map(loc => `'${loc}'`).join(', ')})` : ''}
  AND ${clientExclusionFilter('q.')}
  AND ${enabledClientFilter()}
	GROUP BY DATE(q.created_at), sentiment
	ORDER BY DATE(q.created_at) ASC;
`;

export const locationsStatsQuery = `
	SELECT users.zip, count(q.user_id) as amount
	FROM users
	LEFT JOIN LATERAL (
			SELECT user_id
			FROM questions
			WHERE questions.user_id = users.id
	) q ON true
	WHERE users.enabled = true
	AND users.zip != ''
	AND users.client_id = :clientId
	GROUP BY zip
	ORDER BY amount DESC
	LIMIT 3;
`;

export const votesStatsQuery = `
	WITH relevant_questions AS (
			SELECT id
			FROM questions
			WHERE client_id = :clientId
	)
	SELECT date_trunc('day', created_at) AS day,
				count(*) + count(distinct user_id) AS amount
	FROM votes
	WHERE enabled = true
	AND question_id IN (SELECT id FROM relevant_questions)
	GROUP BY day
	ORDER BY day;
`;

export const categoriesStatsQuery = `
	SELECT q.category, count(*) + count(distinct q.user_id) AS amount
	FROM questions q
	LEFT JOIN votes v ON v.question_id = q.id
	WHERE q.client_id = :clientId
	GROUP BY q.category
	ORDER BY amount DESC
	LIMIT 4;
`;

export const pageViewsStatsQuery = `
	SELECT to_char(day, 'Mon, DD, YY') AS day,
				amount::integer
	FROM mv_daily_pageviews
	WHERE client_id = :clientId
	AND day > CURRENT_DATE - interval '7 days'
	ORDER BY day DESC;
`;

export const videoViewsStatsQuery = `
	WITH video_stats AS (
			SELECT
					EXTRACT(DAY FROM NOW() - MIN(created_at))::text AS days,
					answer_id,
					client_id,
					count(*) as amount
			FROM stats.stat_events
			WHERE event_action = 'playVideo'
			AND ${statExclusionFilter()}
			AND client_id = :clientId
			AND answer_id IS NOT NULL
			GROUP BY answer_id, client_id
	)
	SELECT
			s.days,
			s.answer_id AS id,
			s.client_id,
			a.image_url AS thumbnail,
			s.amount::integer,
			q.text AS question
	FROM video_stats s
	JOIN answers a ON a.id = s.answer_id
	JOIN questions q ON q.id = a.question_id
	WHERE a.enabled = true
	AND a.client_id = :clientId
	ORDER BY days::integer ASC, amount DESC
	LIMIT 3;
`;

export const mobileDesktopQuery = (clientId, state) => clientId ? `
	SELECT
		device,
		COUNT(*) AS count
	FROM
		stats.google_analytics_data
	WHERE
		client_id = :clientId
		AND created_at BETWEEN :startDate AND :endDate
	GROUP BY
		device;
` : `
	SELECT
		device,
		COUNT(*) AS count
	FROM
		stats.google_analytics_data gad
	JOIN clients c ON gad.client_id = c.id
	WHERE
		gad.created_at BETWEEN :startDate AND :endDate
		${state.length ? `AND c.state IN (${state.map(st => `'${st}'`).join(', ')})` : ''}
    AND ${clientExclusionFilter('gad.')}
    AND ${enabledClientFilter()}
	GROUP BY
		device;
`;

export const referralPercentageQuery = (clientId, state) => clientId ? `
  SELECT
    referrer AS source,
    COUNT(*) AS count,
    ROUND((COUNT(*) * 100.0 / SUM(COUNT(*)) OVER ()), 2) AS percentage
  FROM (
    SELECT referrer FROM stats.google_analytics_data_lp WHERE client_id = :clientId AND created_at BETWEEN :startDate AND :endDate
    UNION ALL
    SELECT referrer FROM stats.google_analytics_data WHERE client_id = :clientId AND created_at BETWEEN :startDate AND :endDate
  ) AS combined
  WHERE combined.referrer NOT LIKE '%repd.us%'
  GROUP BY source;
` : `
  SELECT
    referrer AS source,
    COUNT(*) AS count,
    ROUND((COUNT(*) * 100.0 / SUM(COUNT(*)) OVER ()), 2) AS percentage
  FROM (
    SELECT gad.referrer, c.state
    FROM stats.google_analytics_data_lp gad
    JOIN clients c ON gad.client_id = c.id
    WHERE gad.created_at BETWEEN :startDate AND :endDate
    ${state.length ? `AND c.state IN (${state.map(st => `'${st}'`).join(', ')})` : ''}
    AND ${clientExclusionFilter('gad.')}
    AND ${enabledClientFilter()}

    UNION ALL

    SELECT gad.referrer, c.state
    FROM stats.google_analytics_data gad
    JOIN clients c ON gad.client_id = c.id
    WHERE gad.created_at BETWEEN :startDate AND :endDate
    ${state.length ? `AND c.state IN (${state.map(st => `'${st}'`).join(', ')})` : ''}
    AND ${clientExclusionFilter('gad.')}
    AND ${enabledClientFilter()}
  ) AS combined
  WHERE combined.referrer NOT LIKE '%repd.us%'
  GROUP BY source;
`;

// This gets all videos for all time when the clientId is defined, only the views are time filtered
// When clientId is not defined, videos are returned for the time period
export const videoInfoQuery = (clientId, state) => clientId ? `
  SELECT
    id,
    title,
    image_url AS "thumbnailUrl",
    video_url AS "videoUrl",
    mp4_video_status AS "mp4VideoStatus",
    SUM(CASE WHEN created_at BETWEEN :startDate AND :endDate THEN views ELSE 0 END) AS views,
    category,
    sentiment,
    video_duration as duration,
    created_at::DATE AS "createdAt"
  FROM
    mv_video_info_stats
  WHERE
    client_id = :clientId
  GROUP BY
    id, title, image_url, video_url, category, sentiment,
    video_duration, created_at::DATE, mp4_video_status
  ORDER BY
    views DESC, created_at::DATE DESC;
` : `
  SELECT
    v.id,
    v.title,
    v.image_url AS "thumbnailUrl",
    v.video_url AS "videoUrl",
    v.mp4_video_status AS "mp4VideoStatus",
    v.views,
    v.category,
    v.sentiment,
    v.video_duration as duration,
    v.created_at::DATE AS "createdAt",
    s.created_at::DATE AS "eventCreatedAt"
  FROM
    mv_video_info_stats v
  JOIN clients c ON v.client_id = c.id
  LEFT JOIN stats.stat_events s
    ON v.id = s.answer_id
    AND s.event_action IN ('setVideo', 'playVideo', 'pauseVideo')
  WHERE
    s.created_at BETWEEN :startDate AND :endDate
    ${state?.length ? `AND c.state IN (${state.map(st => `'${st}'`).join(', ')})` : ''}
    AND ${clientExclusionFilter('v.')}
    AND ${enabledClientFilter()}
  GROUP BY
    v.id, v.title, v.image_url, v.video_url, v.category, v.sentiment,
    v.video_duration, v.created_at::DATE, s.created_at::DATE, v.mp4_video_status, v.views
  ORDER BY
    v.views DESC, s.created_at::DATE DESC;
`;

export const videoPlayStatsQuery = (clientId) => clientId ? `
	SELECT
		s.created_at as "created_at",
		s.event_action,
		a.video_duration,
		a.id as "answer_id",
		CONCAT(
			COALESCE(s.first_name, 'FN'),
			' ',
			COALESCE(s.last_name, 'LN'),
			' - ',
			COALESCE(s.zip, 'No ZIP'),
			' / ',
			COALESCE(s.ipa, 'No IPA')
		) as "user"
	FROM stats.stat_events s
	LEFT OUTER JOIN answers a on a.id = s.answer_id
	WHERE
		s.client_id = :clientId AND
		s.event_action IN ('setVideo', 'playVideo', 'pauseVideo', 'playerSliderDragged')
		AND s.created_at BETWEEN :startDate AND :endDate
	ORDER by s.created_at ASC;
` : `
  SELECT
    s.created_at as "created_at",
    s.event_action,
    a.video_duration,
    a.id as "answer_id",
    CONCAT(
      COALESCE(s.first_name, 'FN'),
      ' ',
      COALESCE(s.last_name, 'LN'),
      ' - ',
      COALESCE(s.zip, 'No ZIP'),
      ' / ',
      COALESCE(s.ipa, 'No IPA')
    ) as "user"
  FROM stats.stat_events s
  LEFT OUTER JOIN answers a on a.id = s.answer_id
  JOIN clients c ON s.client_id = c.id
  WHERE
    s.event_action IN ('setVideo', 'playVideo', 'pauseVideo', 'playerSliderDragged')
    AND s.created_at BETWEEN :startDate AND :endDate
    AND ${enabledClientFilter()}
  ORDER by s.created_at ASC;
`;

export const feedbackQuery = (clientId, state) => clientId ? `
  SELECT
    answer_id as id,
    SUM(CASE WHEN video_feedback_amount IN (4, 5) THEN 1 ELSE 0 END) AS likes,
    SUM(CASE WHEN video_feedback_amount IN (1, 2) THEN 1 ELSE 0 END) AS dislikes
  FROM
    stats.stat_events
  WHERE
    client_id = :clientId
	AND ${statExclusionFilter()}
    AND event_action = 'videoFeedbackSelected'
  GROUP BY
    answer_id;
` : `
  SELECT
    answer_id as id,
    SUM(CASE WHEN video_feedback_amount IN (4, 5) THEN 1 ELSE 0 END) AS likes,
    SUM(CASE WHEN video_feedback_amount IN (1, 2) THEN 1 ELSE 0 END) AS dislikes
  FROM
    stats.stat_events se
  JOIN
    clients c ON se.client_id = c.id
  WHERE
    event_action = 'videoFeedbackSelected'
	AND ${statExclusionFilter()}
    ${state.length ? `AND c.state IN (${state.map(st => `'${st}'`).join(', ')})` : ''}
    AND ${clientExclusionFilter('se.')}
    AND ${enabledClientFilter()}
  GROUP BY
    answer_id;
`;

export const completionRateQuery = (clientId, state) => clientId ? `
  WITH play_events AS (
    SELECT
      answer_id,
      COUNT(*) AS play_count
    FROM
      stats.stat_events se
    JOIN answers a ON se.answer_id = a.id
    WHERE
      se.client_id = :clientId
      AND ${statExclusionFilter()}
      AND event_action = 'playVideo'
      AND se.created_at BETWEEN :startDate AND :endDate
    GROUP BY
      answer_id
  ),
  watch_durations AS (
    SELECT
      answer_id,
      EXTRACT(EPOCH FROM (se.created_at - lag(se.created_at) OVER (PARTITION BY answer_id ORDER BY se.created_at))) as duration
    FROM
      stats.stat_events se
    WHERE
      se.client_id = :clientId
      AND ${statExclusionFilter()}
      AND event_action = 'pauseVideo'
      AND se.created_at BETWEEN :startDate AND :endDate
  ),
  pause_events AS (
    SELECT
      answer_id,
      COUNT(*) AS pause_count,
      array_agg(duration) as watch_durations
    FROM
      watch_durations
    GROUP BY
      answer_id
  )
  SELECT
    p.answer_id AS id,
    ROUND((p.play_count - COALESCE(pa.pause_count, 0)) * 100.0 / p.play_count, 2) AS completion_rate
  FROM
    play_events p
  LEFT JOIN
    pause_events pa ON p.answer_id = pa.answer_id;
` : `
  WITH play_events AS (
    SELECT
      se.answer_id,
      COUNT(*) AS play_count
    FROM
      stats.stat_events se
    JOIN clients c ON se.client_id = c.id
    WHERE
      se.event_action = 'playVideo'
      AND ${statExclusionFilter('se.')}
      ${state.length ? `AND c.state IN (${state.map(st => `'${st}'`).join(', ')})` : ''}
      AND ${enabledClientFilter()}
      AND se.created_at BETWEEN :startDate AND :endDate
    GROUP BY
      se.answer_id
  ),
  watch_durations AS (
    SELECT
      answer_id,
      EXTRACT(EPOCH FROM (se.created_at - lag(se.created_at) OVER (PARTITION BY answer_id ORDER BY se.created_at))) as duration
    FROM
      stats.stat_events se
    JOIN clients c ON se.client_id = c.id
    WHERE
      se.event_action = 'pauseVideo'
      AND ${statExclusionFilter('se.')}
      ${state.length ? `AND c.state IN (${state.map(st => `'${st}'`).join(', ')})` : ''}
      AND ${clientExclusionFilter('se.')}
      AND ${enabledClientFilter()}
      AND se.created_at BETWEEN :startDate AND :endDate
  ),
  pause_events AS (
    SELECT
      answer_id,
      COUNT(*) AS pause_count,
      array_agg(duration) as watch_durations
    FROM
      watch_durations
    GROUP BY
      answer_id
  )
  SELECT
    p.answer_id AS id,
    ROUND((p.play_count - COALESCE(pa.pause_count, 0)) * 100.0 / p.play_count, 2) AS completion_rate
  FROM
    play_events p
  LEFT JOIN
    pause_events pa ON p.answer_id = pa.answer_id;
`;

export const averageCompletionRateQuery = () => `
  WITH play_events AS (
    SELECT
      se.answer_id,
      COUNT(*) AS play_count
    FROM
      stats.stat_events se
    JOIN
      clients c ON se.client_id = c.id
    WHERE
      se.event_action = 'playVideo'
      AND se.created_at BETWEEN :startDate AND :endDate
      AND ${statExclusionFilter('se.')}
      AND ${clientExclusionFilter('se.')}
      AND ${enabledClientFilter()}
    GROUP BY
      se.answer_id
  ),
  watch_durations AS (
    SELECT
      answer_id,
      EXTRACT(EPOCH FROM (se.created_at - lag(se.created_at) OVER (PARTITION BY answer_id ORDER BY se.created_at))) as duration
    FROM
      stats.stat_events se
    JOIN
      clients c ON se.client_id = c.id
    WHERE
      se.event_action = 'pauseVideo'
      AND se.created_at BETWEEN :startDate AND :endDate
      AND ${statExclusionFilter('se.')}
      AND ${clientExclusionFilter('se.')}
      AND ${enabledClientFilter()}
  ),
  pause_events AS (
    SELECT
      answer_id,
      COUNT(*) AS pause_count,
      array_agg(duration) as watch_durations
    FROM
      watch_durations
    GROUP BY
      answer_id
  ),
  completion_rates AS (
    SELECT
      p.answer_id,
      GREATEST(
        ROUND((p.play_count - COALESCE(pa.pause_count, 0)) * 100.0 / p.play_count, 2),
        0
      ) AS completion_rate
    FROM
      play_events p
    LEFT JOIN
      pause_events pa ON p.answer_id = pa.answer_id
    LEFT OUTER JOIN answers a ON p.answer_id = a.id
    WHERE
      a.enabled = true AND
      (a.is_draft = false OR a.is_draft IS NULL) AND
      (a.is_denied = false OR a.is_denied IS NULL) AND
      (a.is_approved = true OR a.is_approved IS NULL)
  )
  SELECT
    AVG(completion_rate) AS average_completion_rate
  FROM
    completion_rates;
`;
