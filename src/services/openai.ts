/* eslint-disable @typescript-eslint/no-floating-promises */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import path from "path";
import fs from "fs";

import OpenAI from "openai";
import { QueryTypes } from "sequelize";

import { logger, trackProgress } from "@/services/logger";
import { getVectorStore } from "@/services/embeddings/create_embeddings";
import { config } from "@/config";
import { QuestionEmbedding } from "@/store/models/question_embedding";
import { SystemPrompt } from "@/prompts/system_prompts";
import { QuestionPrompt } from "@/prompts/question_prompt";
import { Site } from "@/store/models/site";
import {
  extractSourcesFromResults,
  generateSourceSummariesBatch,
  prepareSourcesForSummarization,
  applySummariesToSources,
  saveSummariesToSourceLinks,
  checkForSourceOverrides,
  logSourceDateInfo,
  extractPublicationDate,
} from "@/utils/sourceHelpers";
import { sequelize } from "@/store/instance";
import { teleprompterPrompt } from "../prompts/teleprompter";
import { videoAnswerSelectionPrompt } from "@/prompts/video_answer_selection";

const MAX_CONTEXT_LENGTH = 100000;
const MAX_ARTICLES = 4;

// Configuration for video answer matching
const MAX_KEYWORD_CANDIDATES = 5; // Number of top keyword matches to send to OpenAI for evaluation

// Stop words to filter out when analyzing word overlap
const STOP_WORDS = [
  "the",
  "and",
  "or",
  "but",
  "in",
  "on",
  "at",
  "to",
  "for",
  "of",
  "with",
  "by",
  "about",
  "as",
  "what",
  "is",
  "are",
  "where",
  "when",
  "how",
  "why",
  "can",
  "do",
  "does",
  "which",
  "who",
  "whom",
  "whose",
  "a",
  "an",
  "was",
  "were",
  "be",
  "been",
  "have",
  "has",
  "had",
  "will",
  "would",
  "could",
  "should",
  "this",
  "that",
  "these",
  "those",
  "there",
  "here",
  "then",
  "than",
  "from",
  "into",
  "out",
  "up",
  "down",
];

const tempDir = path.join(process.cwd(), "temp");

if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true });
}

const truncateContext = (context: string) => {
  if (context.length <= MAX_CONTEXT_LENGTH) return context;

  const truncated = context.slice(0, MAX_CONTEXT_LENGTH);
  const lastSpace = truncated.lastIndexOf(" ");
  return truncated.slice(0, lastSpace) + "...";
};

// Default: Arlington
const getAnswer = async (
  question: string,
  context: string,
  clientId: number = 211,
  overrideTexts: string[] = [],
  internalType: string | null = null,
  isTeleprompter: boolean = false,
) => {
  const openai = new OpenAI();

  // Log if overrides are being applied
  if (overrideTexts.length > 0) {
    logger.info(`Applying ${overrideTexts.length} overrides for question: "${question}"`);
    logger.info(`Override texts: ${JSON.stringify(overrideTexts)}`);
  } else {
    logger.info(`No overrides found for question: "${question}"`);
  }

  // Default values
  let cityName = "";
  let siteUrl = "";
  let siteSystemPrompt = SystemPrompt;
  let siteUserPrompt = QuestionPrompt;

  // If teleprompter is requested, use teleprompter prompt
  if (isTeleprompter) {
    // Import teleprompter prompt
    siteSystemPrompt = teleprompterPrompt;
  }
  // If internalType is specified, use internal prompts
  else if (internalType === "true") {
    // Import internal prompts
    const { SystemPrompt: InternalSystemPrompt } = await import("@/prompts/internal/system_prompts");
    const { QuestionPrompt: InternalQuestionPrompt } = await import("@/prompts/internal/question_prompt");

    siteSystemPrompt = InternalSystemPrompt;
    siteUserPrompt = InternalQuestionPrompt;

    logger.info(`Using internal prompts for question: "${question}"`);
  }

  // Try to get site info for this client
  try {
    const sites = await Site.findAll({
      where: {
        clientId,
        enabled: true,
      },
    });

    const site = sites[0];

    cityName = site.getDataValue("name") || "City";
    siteUrl = site.getDataValue("url");

    // Create a customized prompt for each site
    if (overrideTexts.length > 0) {
      // Format overrides to be more explicit and override the default behavior
      const formattedOverrides = overrideTexts
        .map(
          (text) =>
            `IMPORTANT: The following information is current and up-to-date. Use this information to answer the question: ${text}`,
        )
        .join("\n\n");

      // Add overrides to the beginning of the system prompt
      siteSystemPrompt =
        `OVERRIDE NOTICE: The information below takes precedence over any general guidelines about recency or dates.\n\n${formattedOverrides}\n\n${siteSystemPrompt}`
          .replace("CITY_NAME", cityName)
          .replace("CONTEXT", context)
          .replace("QUESTION", question)
          .replace("LINK", siteUrl);

      // For the user prompt, we'll make the override even more explicit
      siteUserPrompt =
        `IMPORTANT: Use the override information provided in the system message to answer this question about ${question}. This information is current regardless of its date.\n\n${siteUserPrompt}`
          .replace("CITY_NAME", cityName)
          .replace("CONTEXT", context)
          .replace("QUESTION", question)
          .replace("LINK", siteUrl);
    } else {
      // Original prompt formatting without overrides
      siteSystemPrompt = siteSystemPrompt
        .replace("CITY_NAME", cityName)
        .replace("CONTEXT", context)
        .replace("QUESTION", question)
        .replace("LINK", siteUrl);

      siteUserPrompt = siteUserPrompt
        .replace("CITY_NAME", cityName)
        .replace("CONTEXT", context)
        .replace("QUESTION", question)
        .replace("LINK", siteUrl);
    }
  } catch (error) {
    logger.warn(`Error fetching site info for clientId ${clientId}:`, error);
  }

  // Log the final prompts being sent to OpenAI
  logger.info(`Final system prompt: ${siteSystemPrompt.substring(0, 200)}...`);
  logger.info(`Final user prompt: ${siteUserPrompt.substring(0, 200)}...`);

  const response = await openai.chat.completions.create({
    model: config.completionsModel,
    messages: [
      {
        role: "system",
        content: siteSystemPrompt,
      },
      {
        role: "user",
        content: siteUserPrompt,
      },
    ],
    temperature: internalType === "internal" ? 0.1 : config.temperature,
    max_tokens: 3000,
  });

  const answer = response.choices[0].message.content || "";
  const tokenUsage = response.usage?.total_tokens || 0;

  return { answer, tokenUsage, siteSystemPrompt, siteUserPrompt };
};

const getQuestionEmbedding = async (question: string, clientId: number) => {
  const openai = new OpenAI();

  // Transform the question for better vector search
  const transformedQuestion = await transformQuestionForVectorSearch(question, clientId);

  // Log for debugging
  logger.info(`Original question: "${question}"`);
  logger.info(`Transformed question: "${transformedQuestion}"`);

  const response = await openai.embeddings.create({
    model: config.embeddingModel,
    input: transformedQuestion,
  });
  return { embedding: response.data[0].embedding, tokenUsage: response.usage.total_tokens || 0 };
};

/**
 * Transforms a user question to optimize for vector search
 * Uses OpenAI to create a more searchable version of the question
 */
const transformQuestionForVectorSearch = async (question: string, clientId: number): Promise<string> => {
  // Convert to lowercase and tokenize
  const words = question.split(/\s+/);

  // Extract important keywords (words longer than 4 chars and capitalized acronyms)
  const keywords = words.filter((word) => {
    const cleanWord = word.replace(/[^\w]/g, "");
    // Check for capitalized acronyms (all caps words of 2+ characters)
    const isAcronym = cleanWord.length >= 2 && cleanWord === cleanWord.toUpperCase();
    // Check for important words (longer than 4 chars, not in stop words)
    const isImportantWord = cleanWord.length > 4 && !STOP_WORDS.includes(cleanWord.toLowerCase());

    return isAcronym || isImportantWord;
  });

  // Filter out question words from the beginning of the query
  let contentWords = words;
  if (STOP_WORDS.includes(words[0].toLowerCase())) {
    // If query starts with a question word, remove it and possibly the next word if it's "is", "are", etc.
    contentWords = words.slice(1);
    if (
      contentWords.length > 0 &&
      ["is", "are", "was", "were", "the", "a", "an"].includes(contentWords[0].toLowerCase())
    ) {
      contentWords = contentWords.slice(1);
    }
  }

  // Join the remaining content words
  const strippedQuery = contentWords.join(" ");

  // Get city name for context
  let cityName = "";
  try {
    const site = await Site.findOne({ where: { clientId, enabled: true } });
    if (site) {
      cityName = site.getDataValue("name") || "";
    }
  } catch (error) {
    logger.warn(`Error fetching site info for clientId ${clientId}:`, error);
  }

  // Combine stripped query with boosted keywords
  // Add keywords with a boost by repeating them
  const keywordsBoost = keywords.length > 0 ? ` ${keywords.join(" ")} ${keywords.join(" ")}` : "";

  // Log the transformation for debugging
  logger.info(`Original question: "${question}"`);
  logger.info(`Stripped query: "${strippedQuery}"`);
  logger.info(`Extracted keywords: ${JSON.stringify(keywords)}`);
  logger.info(`Final search query: "${strippedQuery}${keywordsBoost}"`);

  return strippedQuery + keywordsBoost;
};

interface AnswerResult {
  answer: string;
  answerTokenUsage: number | undefined;
  searchTime: number;
  answeringTime: number;
  sources: Array<{
    url: string;
    score: number;
  }>;
  matchingAnswers: any[]; // Add this line to include matching answers in the interface
}

/**
 * Calculates word overlap between question and transcription text
 * Returns a score between 0 and 1 representing the percentage of question words found in transcription
 */
const calculateWordOverlap = (question: string, transcription: string): number => {
  if (!question || !transcription) return 0;

  // Normalize and extract meaningful words from question
  const questionWords = question
    .toLowerCase()
    .replace(/[^\w\s]/g, " ")
    .split(/\s+/)
    .filter((word) => word.length > 2) // Filter out very short words
    .filter((word) => !STOP_WORDS.includes(word));

  if (questionWords.length === 0) return 0;

  // Normalize transcription text
  const transcriptionText = transcription.toLowerCase().replace(/[^\w\s]/g, " ");

  // Count how many question words appear in transcription
  const matchingWords = questionWords.filter((word) => transcriptionText.includes(word));

  const overlapScore = matchingWords.length / questionWords.length;

  // logger.info(`Word overlap analysis:`);
  // logger.info(`Question words: ${JSON.stringify(questionWords)}`);
  // logger.info(`Matching words: ${JSON.stringify(matchingWords)}`);
  // logger.info(`Overlap score: ${overlapScore.toFixed(2)} (${matchingWords.length}/${questionWords.length})`);

  return overlapScore;
};

/**
 * Uses OpenAI to select the most relevant answer from candidates
 */
const selectBestAnswerWithAI = async (question: string, candidateAnswers: any[]): Promise<any | null> => {
  if (candidateAnswers.length === 0) {
    return null;
  }

  try {
    const openai = new OpenAI();

    // Prepare the candidate answers for OpenAI evaluation
    const candidatesText = candidateAnswers
      .map((answer, index) => {
        return `Answer ${index + 1}:
Title: ${answer.question || "No title"}
Transcript: ${answer.transcription || "No transcript"}
Answer ID: ${answer.repd_answer_id}
---`;
      })
      .join("\n\n");

    const prompt = videoAnswerSelectionPrompt(question, candidatesText);

    const response = await openai.chat.completions.create({
      model: config.completionsModel,
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: 0.1,
      max_tokens: 50,
    });

    const aiResponse = response.choices[0].message.content?.trim();

    if (!aiResponse || aiResponse === "NONE") {
      logger.info(`OpenAI determined no relevant answers for question: "${question}"`);
      return null;
    }

    // Find the selected answer by ID
    const selectedAnswerId = aiResponse;
    const selectedAnswer = candidateAnswers.find((answer) => answer.repd_answer_id?.toString() === selectedAnswerId);

    if (selectedAnswer) {
      logger.info(`OpenAI selected answer ${selectedAnswerId} for question: "${question}"`);
      return selectedAnswer;
    } else {
      logger.warn(`OpenAI selected answer ID ${selectedAnswerId} but it wasn't found in candidates`);
      return null;
    }
  } catch (error) {
    logger.error("Error using OpenAI to select best answer:", error);
    // Fallback to the first candidate if AI selection fails
    return candidateAnswers[0] || null;
  }
};

/**
 * Converts transcription items to plain text
 */
const transcriptionToText = (transcription: any): string => {
  if (!transcription || !transcription.items) {
    return "";
  }

  return transcription.items
    .map((item: any) => {
      if (item.alternatives && item.alternatives.length > 0) {
        return item.alternatives[0].content;
      }
      return "";
    })
    .join(" ")
    .trim();
};

/**
 * Gets video answers from the database (same data as frontend but from backend)
 * Applies the same filtering logic as the frontend answerService.ts
 */
const getVideoAnswersFromDatabase = async (clientId: number): Promise<any[]> => {
  try {
    const vectorStore = await getVectorStore();
    const vectorDbClient = await vectorStore.pool.connect();

    // Get all video answers for this client from answer_embeddings table
    const query = `
      SELECT
        ae.repd_answer_id,
        ae.question,
        ae.transcription,
        ae.created_at
      FROM
        answer_embeddings ae
      WHERE
        ae.repd_client_id = $1
        AND ae.repd_answer_id IS NOT NULL
        AND (ae.question IS NOT NULL OR ae.transcription IS NOT NULL)
      ORDER BY ae.created_at DESC
    `;

    const results = await vectorDbClient.query(query, [clientId]);
    vectorDbClient.release();

    if (!results.rows || results.rows.length === 0) {
      return [];
    }

    // Convert to the same format as frontend videos
    return results.rows.map((row) => ({
      id: row.repd_answer_id.toString(),
      question: { text: row.question || "" },
      transcription: { items: [] }, // We'll use the raw transcription text instead
      transcriptionText: row.transcription || "", // Store raw transcription
      stats: { answeredAt: row.created_at },
    }));
  } catch (error) {
    logger.error("Error getting video answers from database:", error);
    return [];
  }
};

/**
 * Finds matching answers using keyword matching and OpenAI selection
 */
const findMatchingAnswers = async (question: string, clientId: number): Promise<any[]> => {
  try {
    // Get videos from the database (same data as frontend)
    const videos = await getVideoAnswersFromDatabase(clientId);

    if (!videos || videos.length === 0) {
      logger.info(`No videos found for clientId: ${clientId}`);
      return [];
    }

    logger.info(`Found ${videos.length} total videos for keyword matching`);

    // Calculate word overlap scores for all videos
    const videosWithScores = videos
      .map((video) => {
        const questionText = video.question?.text || "";
        const transcriptionText = video.transcriptionText || ""; // Use raw transcription

        const questionScore = questionText ? calculateWordOverlap(question, questionText) : 0;
        const transcriptionScore = transcriptionText ? calculateWordOverlap(question, transcriptionText) : 0;

        // Use the higher of the two scores
        const overlapScore = Math.max(questionScore, transcriptionScore);

        // Log each video for debugging
        logger.info(
          `Video ${video.id}: question_score=${questionScore.toFixed(3)}, transcription_score=${transcriptionScore.toFixed(3)}, final_score=${overlapScore.toFixed(3)}`,
        );

        return {
          repd_answer_id: video.id,
          question: questionText,
          transcription: transcriptionText,
          overlapScore,
          source_type: "answer",
          created_at: video.stats?.answeredAt || new Date().toISOString(),
        };
      })
      .filter((video) => video.overlapScore > 0) // Only include videos with some keyword overlap
      .sort((a, b) => b.overlapScore - a.overlapScore) // Sort by overlap score descending
      .slice(0, MAX_KEYWORD_CANDIDATES); // Get top candidates

    logger.info(`Found ${videos.length} total videos, ${videosWithScores.length} with keyword overlap`);

    if (videosWithScores.length === 0) {
      return [];
    }

    // Log the candidates for debugging
    videosWithScores.forEach((video, index) => {
      logger.info(
        `Candidate ${index + 1}: Video ${video.repd_answer_id}, overlap_score=${video.overlapScore.toFixed(3)}`,
      );
    });

    // Use OpenAI to select the best answer from candidates
    const selectedAnswer = await selectBestAnswerWithAI(question, videosWithScores);

    return selectedAnswer ? [selectedAnswer] : [];
  } catch (error) {
    logger.error("Error finding matching answers:", error);
    return [];
  }
};

const answerUserQuestion = async (
  question: string,
  clientId: number,
  internalType: string | null = null,
  isTeleprompter: boolean = false,
  userId: number | null = null,
): Promise<AnswerResult> => {
  try {
    const vectorStore = await getVectorStore();

    // Get site URL for this client
    let siteUrl = "";
    try {
      const site = await Site.findOne({ where: { clientId, enabled: true } });
      if (site) {
        siteUrl = site.getDataValue("url");
      } else {
        logger.warn(`No enabled site found for clientId ${clientId}`);
      }
    } catch (error) {
      logger.warn(`Error fetching site info for clientId ${clientId}:`, error);
    }

    const stopTrackingGetQuestionEmbedding = trackProgress("Getting question embedding");

    const { embedding: questionEmbedding, tokenUsage: questionEmbeddingTokenUsage } = await getQuestionEmbedding(
      question,
      clientId,
    );
    const getQuestionEmbeddingTime = stopTrackingGetQuestionEmbedding();

    // Find matching answers based on keyword matching
    // Now using the same data source as the frontend (answer_embeddings table)
    let matchingAnswers: any[] = [];

    findMatchingAnswers(question, clientId)
      .then((answers) => {
        matchingAnswers = answers;
      })
      .catch((error) => {
        logger.error("Error finding matching answers in background:", error);
      });

    const stopTrackingSearchTime = trackProgress("Searching for relevant documents in vector store");

    // Get client connection from the pool
    const vectorDbClient = await vectorStore.pool.connect();

    // Format the embedding vector for PostgreSQL
    const formattedEmbedding = `[${questionEmbedding.join(",")}]`;

    // Determine schema name based on client ID
    const schema = `client_${clientId}`;
    let query = "";
    const queryParams = [formattedEmbedding];

    // Extract keywords for additional filtering
    const extractKeywordsFromQuestion = (question: string): string[] => {
      // Convert to lowercase and tokenize
      const words = question.split(/\s+/);

      // Extract important keywords (words longer than 4 chars and capitalized acronyms)
      return words.filter((word) => {
        const cleanWord = word.replace(/[^\w]/g, "");
        // Check for capitalized acronyms (all caps words of 2+ characters)
        const isAcronym = cleanWord.length >= 2 && cleanWord === cleanWord.toUpperCase();
        // Check for important words (longer than 4 chars, not in stop words)
        const isImportantWord = cleanWord.length > 4 && !STOP_WORDS.includes(cleanWord.toLowerCase());

        return isAcronym || isImportantWord;
      });
    };

    // Now we can safely use it
    const keywords = extractKeywordsFromQuestion(question);

    // Fallback to original query if no client ID is found
    query = `
      WITH ranked_results AS (
        SELECT
          content,
          metadata,
          embedding <#> $1::vector AS semantic_score,
          CASE
            WHEN metadata->>'publicationDate' ~ '^\d{4}-\d{2}-\d{2}' THEN
              EXTRACT(EPOCH FROM NOW() - (metadata->>'publicationDate')::timestamp)
            WHEN metadata->>'lastModified' ~ '^\d{4}-\d{2}-\d{2}' THEN
              EXTRACT(EPOCH FROM NOW() - (metadata->>'lastModified')::timestamp)
            WHEN metadata->>'createdAt' ~ '^\d{4}-\d{2}-\d{2}' THEN
              EXTRACT(EPOCH FROM NOW() - (metadata->>'createdAt')::timestamp)
            WHEN metadata->>'extractedDate' ~ '^\d{4}-\d{2}-\d{2}' THEN
              EXTRACT(EPOCH FROM NOW() - (metadata->>'extractedDate')::timestamp)
            ELSE 63072000 -- Default to 2 years old if no date information
            -- NOW() - INTERVAL '2 years' -- fallback
          END AS age_seconds,
          -- Add keyword matching score (0 if no match, 1 if match)
          CASE
            ${(Array.isArray(keywords) ? keywords : []).map((k: string) => `WHEN content ILIKE '%${k}%' THEN 1`).join("\n            ")}
            WHEN content LIKE 'NO CONTENT' THEN 1
            ELSE 0
          END AS keyword_match
        FROM ${schema}.mv_embeddings
      )
      SELECT
        content,
        metadata,
        semantic_score,
        age_seconds,
        keyword_match,
        -- Adjust combined score to prioritize keyword matches
        semantic_score + (age_seconds / 31536000.0) * 0.5 AS combined_score
      FROM ranked_results
      WHERE semantic_score < ${internalType === "true" ? 50 : 0.15} OR keyword_match >= 1
      ORDER BY combined_score ASC
      LIMIT ${MAX_ARTICLES}
    `;

    // Execute the query with proper string interpolation for materialized view names
    const preparedQuery = query.replace(/\$\{clientId\}/g, clientId.toString() || "");
    const results = await vectorDbClient.query(preparedQuery, queryParams);
    vectorDbClient.release();

    // Check if we have results
    if (!results.rows || results.rows.length === 0) {
      logger.warn(`No relevant documents found for question: "${question}" and clientId: ${clientId}`);
      logger.warn(
        `Query executed: ${preparedQuery.replace(/\$1/g, formattedEmbedding).replace(/\$2/g, clientId.toString())}`,
      );

      // Store the question and answer
      try {
        QuestionEmbedding.create({
          question,
          answer: "I'm sorry, I couldn't find any relevant information to answer your question.",
          embedding: questionEmbedding,
          relatedEmbeddings: "[]",
          questionEmbeddingTokenUsage,
          answerTokenUsage: 0,
          searchTime: 0,
          getQuestionEmbeddingTime,
          answeringTime: 0,
          clientId,
          sources: [
            ...(internalType === "true" ? [{ text: "Internal question", type: "internal" }] : []),
            ...results.rows.map((row) => ({ text: row.content, type: "source" })),
          ],
        });
      } catch (error) {
        // Ignore errors when storing the question embedding
        logger.warn("Failed to store question embedding:", error);
      }

      return {
        answer: "I'm sorry, I couldn't find any relevant information to answer your question.",
        answerTokenUsage: 0,
        searchTime: 0,
        answeringTime: 0,
        sources: [],
        matchingAnswers: [],
      };
    }

    // Add this function to verify if content is recent enough to be presented as current
    const isContentRecent = (
      metadata: Record<string, any>,
      content: string,
    ): { isRecent: boolean; date: string | null } => {
      // Get current date and calculate cutoff date (1 year ago)
      const currentDate = new Date();
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(currentDate.getFullYear() - 1);

      // Make the check more strict - 6 months instead of 1 year
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(currentDate.getMonth() - 6);

      // Try to get date from metadata
      let contentDate: Date | null = null;
      let dateString: string | null = null;

      // Check publication date first (most accurate)
      if (metadata.publicationDate) {
        contentDate = new Date(metadata.publicationDate);
        dateString = metadata.publicationDate;
      } else if (metadata.lastModified) {
        contentDate = new Date(metadata.lastModified);
        dateString = metadata.lastModified;
      } else if (metadata.createdAt) {
        // Only use createdAt if it's not a recent database entry for old content
        const createdDate = new Date(metadata.createdAt);
        // If created recently but content seems old based on text, don't use createdAt
        const extractedDate = extractPublicationDate(metadata.sourceUrl || "", content, metadata);
        if (extractedDate && Math.abs(createdDate.getTime() - extractedDate.getTime()) > 30 * 24 * 60 * 60 * 1000) {
          // If extracted date is more than 30 days different from createdAt, use extracted date
          contentDate = extractedDate;
          dateString = extractedDate.toISOString();
        } else {
          contentDate = createdDate;
          dateString = metadata.createdAt;
        }
      } else if (metadata.extractedDate) {
        contentDate = new Date(metadata.extractedDate);
        dateString = metadata.extractedDate;
      } else {
        // Try to extract date from content and URL
        const extractedDate = extractPublicationDate(metadata.sourceUrl || "", content, metadata);
        if (extractedDate) {
          contentDate = extractedDate;
          dateString = extractedDate.toISOString();
        }
      }

      // If we couldn't determine a date, consider it not recent
      if (!contentDate || isNaN(contentDate.getTime())) {
        return { isRecent: false, date: null };
      }

      // Check if the content date is within the last 6 months
      return {
        isRecent: contentDate >= sixMonthsAgo,
        date: dateString,
      };
    };

    // Build context with clear date information and verification
    let context = "";

    // Add a header for the most recent information
    if (results.rows.length > 0) {
      context += "MOST RECENT INFORMATION:\n";

      // Add date information for each result
      for (let i = 0; i < Math.min(2, results.rows.length); i++) {
        const row = results.rows[i];
        const metadata = row.metadata as Record<string, any>;

        // Verify if content is recent
        const { isRecent, date } = isContentRecent(metadata, row.content);

        // Try to get the date from metadata
        let dateInfo = "";
        if (date) dateInfo = `DATE: ${new Date(date).toLocaleDateString()}`;
        else if (metadata.lastModified) dateInfo = `DATE: ${new Date(metadata.lastModified).toLocaleDateString()}`;
        else if (metadata.createdAt) dateInfo = `DATE: ${new Date(metadata.createdAt).toLocaleDateString()}`;
        else if (metadata.extractedDate) dateInfo = `DATE: ${new Date(metadata.extractedDate).toLocaleDateString()}`;

        // Add recency flag to help the model identify outdated content
        if (!isRecent) {
          dateInfo += ` [OUTDATED - DO NOT PRESENT AS CURRENT]`;
          context += `${dateInfo}\n${row.content}\n\n`;
        } else {
          context += `${dateInfo}\n${row.content}\n\n`;
        }
      }

      // Add older information if available
      if (results.rows.length > 2) {
        context += "OLDER INFORMATION [OUTDATED - DO NOT PRESENT AS CURRENT]:\n";
        for (let i = 2; i < results.rows.length; i++) {
          context += results.rows[i].content + "\n\n";
        }
      }
    }

    const searchTime = stopTrackingSearchTime();

    // Extract sources using the utility function
    const sourcesWithoutSummaries = extractSourcesFromResults(results.rows);

    // Add debug logging
    logSourceDateInfo(sourcesWithoutSummaries, results.rows);

    // Check for source overrides based on keywords in the question
    let overrideTexts: string[] = [];
    const overrideTextsPromise = checkForSourceOverrides(question, clientId)
      .then((texts) => {
        overrideTexts = texts;
      })
      .catch((error) => {
        logger.error("Error checking for source overrides in background:", error);
      });

    // Wait for overrides to be checked
    await overrideTextsPromise;

    // If we have overrides, use them directly instead of the regular context
    if (overrideTexts.length > 0) {
      // Replace the context with the override text
      context = overrideTexts.join("\n\n");

      // Add a clear marker at the beginning
      context = "CURRENT AND AUTHORITATIVE INFORMATION:\n\n" + context;

      // Log that we're using override text instead of regular context
      logger.info(`Using override text as context for question: "${question}"`);
    }

    // Start the answer generation with the potentially modified context
    const answerPromise = getAnswer(question, context, clientId, overrideTexts, internalType, isTeleprompter);

    // Start the answer generation - will need to wait for overrides before using them
    const stopTrackingAnswerTime = trackProgress("Generating answer");

    // Prepare sources for summarization
    const { contentsToSummarize, contentSourceMap } = prepareSourcesForSummarization(
      sourcesWithoutSummaries,
      results.rows,
    );

    // Generate summaries in batch
    const summariesPromise = generateSourceSummariesBatch(contentsToSummarize);

    // Wait for both operations to complete
    const [summaries, { answer, tokenUsage: answerTokenUsage, siteSystemPrompt, siteUserPrompt }] = await Promise.all([
      summariesPromise,
      answerPromise,
      overrideTextsPromise,
    ]);

    const answeringTime = stopTrackingAnswerTime();

    // Apply summaries to sources
    const sources = applySummariesToSources(sourcesWithoutSummaries, contentSourceMap, summaries);

    // Save summaries to source links in the background
    (() => {
      try {
        saveSummariesToSourceLinks(contentSourceMap, summaries);
      } catch (error) {
        logger.error("Error in background summary saving:", error);
      }
    })();

    // Store the question and answer
    try {
      QuestionEmbedding.create({
        question,
        answer,
        embedding: questionEmbedding,
        relatedEmbeddings: context,
        questionEmbeddingTokenUsage,
        answerTokenUsage,
        searchTime,
        getQuestionEmbeddingTime,
        answeringTime,
        clientId,
        userId,
        sources: [
          ...(internalType === "true" ? [{ text: "Internal question", type: "internal" }] : []),
          ...overrideTexts.map((text) => ({ text, type: "override" })),
          ...sources,
          { systemPrompt: siteSystemPrompt, userPrompt: siteUserPrompt },
        ],
      });
    } catch (error) {
      // Ignore errors when storing the question embedding
      logger.warn("Failed to store question embedding:", error);
    }

    logger.info(`Answer: ${answer}`);
    logger.info(`Tokens used for answer: ${answerTokenUsage}`);
    logger.info(`Tokens used for question embedding: ${questionEmbeddingTokenUsage}`);
    // logger.info(`Sources: ${JSON.stringify(sources)}`);

    return {
      answer: answer.replace(/\*\*/g, "").replace(/```/g, ""),
      answerTokenUsage,
      searchTime,
      answeringTime,
      sources,
      matchingAnswers,
    };
  } catch (error) {
    logger.error(`Error in answerUserQuestion:`, error);
    return {
      answer: "I'm sorry, I encountered an error while processing your question.",
      answerTokenUsage: 0,
      searchTime: 0,
      answeringTime: 0,
      sources: [],
      matchingAnswers: [], // Add empty array for matching answers
    };
  }
};

export { answerUserQuestion, calculateWordOverlap };
