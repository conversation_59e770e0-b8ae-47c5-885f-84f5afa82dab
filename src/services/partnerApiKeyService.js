import crypto from 'crypto';
import bcrypt from 'bcrypt';

import partnerApi<PERSON>eyError from '../errors/partnerApiKeyError.js';
import partnerApiKeyModel from '../models/partnerApiKeyModel.js';
import serviceUtil from '../utils/serviceUtil.js';

// Services
async function createApiKey(clientId, partnerId) {
	// Generate a random 64-character hex string
	const rawKey = crypto.randomBytes(32).toString('hex');

	// Hash the key for storage
	const hashedKey = await bcrypt.hash(rawKey, 10);

	// Get the last 4 characters for the key sample
	const keySample = rawKey.slice(-4);

	// Create the API key record in the database
	const entityToCreate = {};
	serviceUtil.updateProperties(entityToCreate, transformToEntity({
		key: hashedKey,
		keySample: keySample,
		clientId: clientId,
		partnerId: partnerId,
		enabled: true
	}));

	const apiKeyRecord = await partnerApiKeyModel.create(entityToCreate);

	// Return both the raw key (only time it's returned) and the record ID
	return {
		rawKey: rawKey,
		apiKeyId: apiKeyRecord.id,
		clientId: clientId,
		apiKeyRecord: apiKeyRecord
	};
}

async function verifyApiKey(rawKey, hashedKey) {
	return await bcrypt.compare(rawKey, hashedKey);
}

async function findByRawKey(rawKey) {
	// Get all enabled API keys
	const apiKeys = await partnerApiKeyModel.findAll({
		where: {
			enabled: true
		}
	});

	// Check each hashed key against the raw key
	for (const apiKey of apiKeys) {
		const isMatch = await bcrypt.compare(rawKey, apiKey.key);
		if (isMatch) {
			return apiKey;
		}
	}

	return null;
}

async function deleteApiKey(id, auth) {
	const apiKey = await partnerApiKeyModel.findByIdAndEnabled(id);
	if (apiKey) {
		await apiKey.destroy();
	}
	return apiKey;
}

function getById(id, auth) {
	return partnerApiKeyModel.findByIdAndEnabled(id);
}

// Private functions
function transformToEntity(criteria) {
	const fields = [
		{ name: 'key', value: criteria.key },
		{ name: 'keySample', value: criteria.keySample },
		{ name: 'clientId', value: criteria.clientId },
		{ name: 'partnerId', value: criteria.partnerId },
		{ name: 'enabled', value: criteria.enabled }
	];

	return fields;
}

export default {
	createApiKey,
	verifyApiKey,
	findByRawKey,
	deleteApiKey,
	getById
};
