import { db } from '../global/database.js';
import { translateClientCustomTexts } from '../global/translation.js';

import userError from '../errors/userError.js';
import userModel from '../models/userModel.js';
import serviceUtil from '../utils/serviceUtil.js';

import clientModel from '../models/clientModel.js';
import clientMapper from '../mappers/clientMapper.js';

import sessionModel from '../models/sessionModel.js';
import sessionService from '../services/sessionService.js';

import userMailer from '../mailers/userMailer.js';
import client from '../global/client.js';

// Services

/**
 * Creates a client and user based on the provided criteria.
 * @param {Object} clientCriteria - The criteria for creating the client.
 * @param {Object} userCriteria - The criteria for creating the user.
 * @param {Object} auth - The authentication object.
 * @returns {Promise<Object>} A promise that resolves to the created user.
 */
function create( clientCriteria, userCriteria, auth ) {
	var user = {};
	var client = {};
	var attributes = {};

	async function findUser () {
		return userModel.findOne( {
			where: { email: userCriteria.email, enabled: true }
		} );
	}

	async function createClient ( duplicate ) {
		if ( duplicate )
			return Promise.reject( userError.emailDuplicated( duplicate.email ) );

		clientCriteria.enabled = true;

		serviceUtil.updateProperties( attributes, transformToEntity( clientCriteria ) );

		return clientModel.create( attributes );
	}

	async function createUser ( c ) {
		attributes = {};

		client = c;
		userCriteria.clientId = client.id;
		userCriteria.enabled = true;

		serviceUtil.updateProperties( attributes, transformUserToEntity( userCriteria ) );
		return userModel.create( attributes );
	}

	async function sendEmail ( u ) {
		user = u;
		user.client = client;

		const session = await sessionService.promiseCreateAndTransform( user );

		userMailer.joinRepdEmail( user, session );

		return user;
	}

	return(
		findUser()
			.then( createClient )
			.then( createUser )
			.then( sendEmail )
	);
}

/**
 * Lists all clients. (delegate access feature)
 * @param {*} auth 
 * @returns 
 */
async function list( auth ) {
	// super admin only
	if ( !['super admin', 'admin', 'manager'].includes(auth.user.accessLevel) ) {
		return Promise.reject( userError.notAuthorized() );
	}

	const accessLevelScope = auth.user.accessLevel === 'super admin' ? ['super admin', 'admin', 'manager'] : ['admin', 'manager'];

	const clientInclude = {
		as: 'client',
		model: clientModel,
		required: true
	};

	const userInclude = {
		as: 'users',
		model: userModel,
		where: {
			accessLevel: accessLevelScope,
			...(auth.user.accessLevel === 'super admin' ? {} : { clientId: auth.user.client.id }),
			enabled: true
		},
		required: true
	};

	const sessions = await sessionModel.findAll( {
		where: { 
			enabled: true,
		},
		include: [ {
			...userInclude,
			as: 'user',
			include: clientInclude
		} ],
	} );

	// Create Sessions for any clients with no active session
	const clients = await clientModel.findAll( { include: userInclude } );
	for (const client of clients) {
		for (const user of client.users) {
			if (!sessions.some(session => session.user?.client?.id == client?.id && session.user?.id == user?.id)) {
				console.log(`Creating session for ${user?.firstName} on behalf of ${client.name}.`);
				const missingSession = await sessionService.createForClient( user, client.id );
				sessions.push(missingSession);
			}
		}
	}

	console.log('Sessions:', sessions.filter((c, i, self) => self.findIndex((t) => t.id === c.id) === i).length);

	return sessions;
}

async function getAll() {
	return clientModel.findAll();
}

/**
 * Searches for a client based on the provided query and replacements.
 * @param {string} query - The query to search for the client.
 * @param {object} replacements - The replacements for the query.
 * @param {object} auth - The authentication object.
 * @returns {Promise<object>} - A promise that resolves to the client object if found, otherwise returns undefined.
 */
async function search( query, replacements, auth ) {
	return await db.query(
		query, { replacements: replacements }
	).then( ( r ) => {
		var client = (r[0] || [])[0];

		if (client)
			return clientMapper.toFullDTO(client);
		else console.error( 'Client Not Found', replacements );

		return client;
	} );
}

/**
 * Updates a client based on the provided criteria and authentication.
 * @param {Object} criteria - The criteria to find the client.
 * @param {Object} auth - The authentication information.
 * @returns {Promise<Object>} - A promise that resolves to the updated client.
 */
function update ( criteria, auth ) {
	const findClient = async () => await clientModel.findByPk( criteria.id );

	if (auth.user.client.id !== criteria.id && !auth.user.accessLevel.includes( 'super' )) {
		return Promise.reject( userError.notAuthorized() );
	}

	const updateClient = async ( client ) => {
		serviceUtil.updateProperties( client, transformToEntity( criteria ) );

		return client.save();
	};

	return findClient()
		.then( updateClient )
		.then( translateClientCustomTexts )
		.then( clientMapper.toFullDTO );
}
// Private

/**
 * Transforms the given criteria object into an array of field objects.
 * @param {Object} criteria - The criteria object.
 * @returns {Array} - The array of field objects.
 */
function transformToEntity( criteria ) {
	const fields = [
		{ name: 'name',  value: criteria.name },
		{ name: 'email', value: criteria.email },
		{ name: 'clientType', value: criteria.clientType },
		{ name: 'isLocked', value: criteria.isLocked },

		{ name: 'link', value: criteria.link },
		{ name: 'subDomainAdmin', value: criteria.subDomainAdmin },

		{ name: 'logoURL',      value: criteria.logoURL },
		{ name: 'whiteLogoURL', value: criteria.whiteLogoURL },
		{ name: 'expandedWhiteLogoURL', value: criteria.expandedWhiteLogoURL },
		{ name: 'websiteURL',   value: criteria.websiteURL },
		{ name: 'donationURL',  value: criteria.donationURL },
		{ name: 'volunteerURL', value: criteria.volunteerURL },
		{ name: 'customPagePath', value: criteria.customPagePath },
		{ name: 'backgroundImageURL', value: criteria.backgroundImageURL },
		{ name: 'videoBannerURL', value: criteria.videoBannerURL },

		{ name: 'topBarColour',      value: criteria.topBarColour },
		{ name: 'videoLinksColour',  value: criteria.videoLinksColour },
		{ name: 'plusAskPillColour', value: criteria.plusAskPillColour },
		{ name: 'newQuestionColour', value: criteria.newQuestionColour },
		{ name: 'openQuestionsAndAnswersColour', value: criteria.openQuestionsAndAnswersColour },

		{ name: 'embedPrimaryColour', value: criteria.embedPrimaryColour },
		{ name: 'embedAccentColour', value: criteria.embedAccentColour },
		{ name: 'embedButtonColour', value: criteria.embedButtonColour },
		{ name: 'embedProgressBarColour', value: criteria.embedProgressBarColour },
		{ name: 'textOptions',  value: criteria.textOptions },
		{ name: 'translations', value: criteria.translations },

		{ name: 'categories', value: criteria.categories },

		{ name: 'enabled',     value: criteria.enabled },
		{ name: 'isPublished', value: criteria.isPublished },

		{ name: 'subject', value: criteria.subject },
		{ name: 'topMessage', value: criteria.topMessage },
		{ name: 'campaignName', value: criteria.campaignName },
		{ name: 'campaignAddress', value: criteria.campaignAddress },

		{ name: 'donateText', value: criteria.donateText },
		{ name: 'donateCtaText', value: criteria.donateCtaText },
		{ name: 'headerHomeLinkText', value: criteria.headerHomeLinkText },
		{ name: 'headerDonateLinkText', value: criteria.headerDonateLinkText },
		{ name: 'headerVolunteerLinkText', value: criteria.headerVolunteerLinkText },
		{ name: 'emailDonateCtaText', value: criteria.emailDonateCtaText },
		{ name: 'postQuestionText', value: criteria.postQuestionText },
		{ name: 'postQuestionBtnText', value: criteria.postQuestionBtnText },

		{ name: 'aiQuestionsEnabled', value: criteria.aiQuestionsEnabled },
		{ name: 'aiAskQuestionAlwaysOn', value: criteria.aiAskQuestionAlwaysOn },

		{ name: 'state', value: criteria.state },
		{ name: 'populationSize', value: criteria.populationSize },
	];

	if ( criteria.donationDisplaysPermanently )
		fields.push( { name: 'donationDisplaysPermanently', value: criteria.donationDisplaysPermanently } );

	if ( criteria.ngpVanUsername && !criteria.ngpVanUsername.includes( '••••' ) )
		fields.push( { name: 'ngpVanUsername', value: criteria.ngpVanUsername } );
	else if ( !criteria.ngpVanUsername )
		fields.push( { name: 'ngpVanUsername', value: null } );

	if ( criteria.ngpVanApiKey && !criteria.ngpVanApiKey.includes( '••••' ) )
		fields.push( { name: 'ngpVanApiKey', value: criteria.ngpVanApiKey } );
	else if ( !criteria.ngpVanApiKey )
		fields.push( { name: 'ngpVanApiKey', value: null } );

	return fields;
}

/**
 * Transforms a user criteria object into an entity object.
 * @param {Object} criteria - The user criteria object.
 * @returns {Array} - An array of field objects representing the transformed entity.
 */
function transformUserToEntity( criteria ) {
	const fields = [
		{ name: 'clientId',    value: criteria.clientId    },
		{ name: 'firstName',   value: criteria.firstName   },
		{ name: 'lastName',    value: criteria.lastName    },
		{ name: 'email',       value: criteria.email       },
		{ name: 'accessLevel', value: criteria.accessLevel },
		{ name: 'enabled',     value: true 								 },

		{ name: 'verificationToken', value: criteria.verificationToken }
	];

	return fields;
}

export default { create, list, search, update, getAll };
