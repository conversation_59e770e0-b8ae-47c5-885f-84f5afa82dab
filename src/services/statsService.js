import { db } from '../global/database.js';
import moment from 'moment/moment.js';
import {
	mailStatsQuery, analyticsQuery, questionsStatsQuery,
	trendingStatsQuery, emailsStatsQuery, bulkSendsStatsQuery, emailsChartStatsQuery,
	locationsStatsQuery, votesStatsQuery, categoriesStatsQuery, pageViewsStatsQuery,
	videoViewsStatsQuery, mobileDesktopQuery, referralPercentageQuery, engagementsChartStatsQuery,
	videoInfoQuery,
	sentimentQuery,
	feedbackQuery,
	completionRateQuery,
	aiQueriesQuery,
	aiQuerySentimentQuery,
	videoPlayStatsQuery,
	averageCompletionRateQuery
} from '../helpers/statsQueries.js';

export const getMailStats = async (clientId) => {
	try {
		const result = await db.query(mailStatsQuery, {
			replacements: { clientId },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch mail stats');
	}
};

export const getAnalytics = async (clientId, location, startDate, endDate) => {
	try {
		const result = await db.query(analyticsQuery(clientId, location), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch analytics');
	}
};

export const getAiQueriesChart = async (clientId, location, startDate, endDate) => {
	try {
		const result = await db.query(aiQueriesQuery(clientId, location), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch ai queries chart');
	}
};

export const getSentiments = async (clientId, location, startDate, endDate) => {
	try {
		const result = await db.query(sentimentQuery(clientId, location), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch sentiments');
	}
};

export const getAiQueriesSentiments = async (clientId, location, startDate, endDate) => {
	try {
		const result = await db.query(aiQuerySentimentQuery(clientId, location), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch ai query sentiments');
	}
};

export const getQuestionsStats = async (clientId, location, startDate, endDate) => {
	try {
		const result = await db.query(questionsStatsQuery(clientId, location, startDate), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch questions stats');
	}
};

export const getTrendingStats = async (clientId, location, startDate, endDate) => {
	try {
		const result = await db.query(trendingStatsQuery(clientId, location), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch trending stats');
	}
};

export const getEmailsStats = async (clientId, location, startDate, endDate) => {
	try {
		const result = await db.query(emailsStatsQuery(clientId, location), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch emails stats');
	}
};

export const getBulkSendsStats = async (clientId, location, startDate, endDate) => {
	try {
		const result = await db.query(bulkSendsStatsQuery(clientId, location), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch bulk sends stats');
	}
};

export const getEmailsChartStats = async (clientId, location, startDate, endDate) => {
	try {
		const result = await db.query(emailsChartStatsQuery(clientId, location), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch emails chart stats');
	}
};

export const getEngagementsChartStats = async (clientId, location, startDate, endDate) => {
	try {
		const result = await db.query(engagementsChartStatsQuery(clientId, location), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch engagements chart stats');
	}
};

export const getLocationsStats = async (clientId) => {
	try {
		const result = await db.query(locationsStatsQuery, {
			replacements: { clientId },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch locations stats');
	}
};

export const getVotesStats = async (clientId) => {
	try {
		const result = await db.query(votesStatsQuery, {
			replacements: { clientId },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch votes stats');
	}
};

export const getCategoriesStats = async (clientId) => {
	try {
		const result = await db.query(categoriesStatsQuery, {
			replacements: { clientId },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch categories stats');
	}
};

export const getPageViewsStats = async (clientId) => {
	try {
		const result = await db.query(pageViewsStatsQuery, {
			replacements: { clientId },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch page views stats');
	}
};

export const getVideoViewsStats = async (clientId) => {
	try {
		const result = await db.query(videoViewsStatsQuery, {
			replacements: { clientId },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch video views stats');
	}
};

export const getMobileDesktopStats = async (clientId, state, startDate, endDate) => {
	try {
		const result = await db.query(mobileDesktopQuery(clientId, state), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch mobile/desktop stats');
	}
};

export const getReferralPercentageStats = async (clientId, state, startDate, endDate) => {
	try {
		const result = await db.query(referralPercentageQuery(clientId, state), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch referral percentage stats');
	}
};

export const getVideoInfo = async (clientId, state, startDate, endDate) => {

	console.log('getVideoInfo', clientId, state, startDate, endDate, videoInfoQuery(clientId, state));

	try {
		const result = await db.query(videoInfoQuery(clientId, state), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch video information');
	}
};

export const getVideoPlayStats = async (clientId, startDate, endDate) => {
	try {
		const result = await db.query(videoPlayStatsQuery(clientId), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch video play stats');
	}
};

export const getVideoFeedbackStats = async (clientId, state, startDate, endDate) => {
	try {
		const result = await db.query(feedbackQuery(clientId, state), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch video feedback stats');
	}
};

export const getCompletionRateStats = async (clientId, state, startDate, endDate) => {
	try {
		const result = await db.query(completionRateQuery(clientId, state), {
			replacements: { clientId, startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch video completion rate stats');
	}
};

export const getNationalCompletionRate = async (startDate, endDate) => {
	try {
		const result = await db.query(averageCompletionRateQuery(), {
			replacements: { startDate, endDate },
			type: db.QueryTypes.SELECT
		});
		return result;
	} catch (error) {
		console.error(error);
		throw new Error('Failed to fetch national video completion rate');
	}
};

// Add other reusable functions here...
