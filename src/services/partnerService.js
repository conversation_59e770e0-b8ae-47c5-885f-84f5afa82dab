import { Op } from 'sequelize';

import partnerError from '../errors/partnerError.js';
import partnerModel from '../models/partnerModel.js';
import clientModel from '../models/clientModel.js';
import partnerApiKeyModel from '../models/partnerApiKeyModel.js';
import partnerApiKeyService from './partnerApiKeyService.js';
import serviceUtil from '../utils/serviceUtil.js';
import partnerMapper from '../mappers/partnerMapper.js';
import { superSearch } from './_globals.js';

// Services
async function create( criteria, auth ) {
	// Validate required fields
	if ( !criteria.name || criteria.name.trim() === '' ) {
		return Promise.reject( partnerError.nameEmpty() );
	}

	if ( !criteria.clientIds || !Array.isArray( criteria.clientIds ) || criteria.clientIds.length === 0 ) {
		return Promise.reject( partnerError.clientIdsRequired() );
	}

	try {
		// First create the partner without API keys
		const entityToCreate = {};
		serviceUtil.updateProperties( entityToCreate, transformToEntity( {
			...criteria,
			apiKeyIds: [], // Start with empty array
			enabled: true
		}, auth ) );

		const partner = await partnerModel.create( entityToCreate );

		// Then create API keys for each client using the partner ID
		const apiKeyResults = [];
		const apiKeyIds = [];

		for ( const clientId of criteria.clientIds ) {
			const apiKeyResult = await partnerApiKeyService.createApiKey( clientId, partner.id );
			apiKeyResults.push( apiKeyResult );
			apiKeyIds.push( apiKeyResult.apiKeyId );
		}

		// Update the partner with the API key IDs
		partner.apiKeyIds = apiKeyIds;
		partner.changed('apiKeyIds', true);
		await partner.save();

		// Fetch the complete partner with API keys
		const completePartner = await partnerModel.findOne( {
			where: { id: partner.id }
		} );

		// Fetch the API keys separately since we're not using associations (only enabled ones)
		const apiKeys = await partnerApiKeyModel.findAll( {
			where: {
				id: apiKeyIds,
				enabled: true
			},
			include: [
				{ as: 'client', model: clientModel, required: false }
			]
		} );

		// Add the API keys to the partner entity for mapping
		completePartner.apiKeys = apiKeys;

		// Convert to DTO and add the raw API keys (only time they're returned)
		const partnerDTO = partnerMapper.toFullDTO( completePartner );
		partnerDTO.rawApiKeys = apiKeyResults.map( result => ({
			clientId: result.clientId,
			rawKey: result.rawKey,
			apiKeyId: result.apiKeyId
		}));

		return partnerDTO;
	} catch ( error ) {
		throw error;
	}
}

async function update( id, criteria, auth ) {
	const partner = await partnerModel.findByIdAndEnabled( id );

	// Handle client ID changes and API key management
	let newApiKeyIds = partner.apiKeyIds || [];
	let newRawApiKeys = []; // Track new raw keys to return

	if ( criteria.clientIds && Array.isArray( criteria.clientIds ) ) {
		const oldClientIds = partner.clientIds || [];
		const newClientIds = criteria.clientIds;

		// Find clients that were added
		const addedClientIds = newClientIds.filter( clientId => !oldClientIds.includes( clientId ) );

		// Find clients that were removed
		const removedClientIds = oldClientIds.filter( clientId => !newClientIds.includes( clientId ) );

		// Get existing API keys to understand current state
		const existingApiKeys = await partnerApiKeyModel.findAll( {
			where: { id: newApiKeyIds }
		} );

		// Remove API keys for removed clients
		if ( removedClientIds.length > 0 ) {
			const apiKeysToRemove = existingApiKeys.filter( apiKey =>
				removedClientIds.includes( apiKey.clientId )
			);

			// Delete the API keys entirely
			await Promise.all(
				apiKeysToRemove.map( apiKey => apiKey.destroy() )
			);

			// Remove their IDs from the partner's apiKeyIds array
			const removedApiKeyIds = apiKeysToRemove.map( apiKey => apiKey.id );
			newApiKeyIds = newApiKeyIds.filter( id => !removedApiKeyIds.includes( id ) );
		}

		// Create API keys for added clients
		if ( addedClientIds.length > 0 ) {
			const newApiKeyResults = await Promise.all(
				addedClientIds.map( clientId => partnerApiKeyService.createApiKey( clientId, id ) )
			);

			// Add the new API key IDs to the array
			const addedApiKeyIds = newApiKeyResults.map( result => result.apiKeyId );
			newApiKeyIds = [...newApiKeyIds, ...addedApiKeyIds];

			// Store the raw keys to return (only time they're returned)
			newRawApiKeys = newApiKeyResults.map( result => ({
				clientId: result.clientId,
				rawKey: result.rawKey,
				apiKeyId: result.apiKeyId
			}));
		}
	}

	// Update all partner fields
	const fieldsToUpdate = transformToEntity( {
		...criteria,
		apiKeyIds: newApiKeyIds
	}, auth );

	fieldsToUpdate.forEach( field => {
		if ( field.value !== undefined && field.value !== null ) {
			partner[ field.name ] = field.value;
		}
	} );

	await partner.save();

	// Reload the partner and fetch API keys
	const updatedPartner = await partnerModel.findOne( {
		where: { id: partner.id }
	} );

	if ( updatedPartner && updatedPartner.apiKeyIds && updatedPartner.apiKeyIds.length > 0 ) {
		// Fetch the API keys separately (only enabled ones)
		const apiKeys = await partnerApiKeyModel.findAll( {
			where: {
				id: updatedPartner.apiKeyIds,
				enabled: true
			},
			include: [
				{ as: 'client', model: clientModel, required: false }
			]
		} );
		updatedPartner.apiKeys = apiKeys;
	} else {
		// Ensure apiKeys is always set, even if empty
		updatedPartner.apiKeys = [];
	}

	// Convert to DTO and add raw API keys for new clients (only time they're returned)
	const partnerDTO = partnerMapper.toFullDTO( updatedPartner );

	if ( newRawApiKeys.length > 0 ) {
		partnerDTO.rawApiKeys = newRawApiKeys;
	}

	return partnerDTO;
}

function disable( id, auth ) {
	return partnerModel.findByIdAndEnabled( id )
		.then( partner => {
			partner.enabled = false;
			return partner.save();
		} )
		.then( partnerMapper.toFullDTO );
}

async function search( criteria, auth ) {
	// Build the where clause manually since we need custom logic
	const whereClause = {};

	if ( criteria.name ) {
		whereClause.name = { [Op.iLike]: `%${criteria.name}%` };
	}

	if ( criteria.apiEnabled !== undefined ) {
		whereClause.apiEnabled = criteria.apiEnabled;
	}

	// Add enabled filter unless showDisabled is true
	if ( !criteria.showDisabled ) {
		whereClause.enabled = true;
	}

	// Handle pagination
	const limit = criteria.limit || 20;
	const offset = criteria.page ? (criteria.page - 1) * limit : 0;

	// Get partners with count
	const { count, rows } = await partnerModel.findAndCountAll({
		where: whereClause,
		limit: limit,
		offset: offset,
		order: [['createdAt', 'DESC']]
	});

	// Load API keys for each partner
	const partnersWithApiKeys = await Promise.all(
		rows.map(async (partner) => {
			if ( partner.apiKeyIds && partner.apiKeyIds.length > 0 ) {
				// Fetch the API keys for this partner (only enabled ones)
				const apiKeys = await partnerApiKeyModel.findAll( {
					where: {
						id: partner.apiKeyIds,
						enabled: true
					},
					include: [
						{ as: 'client', model: clientModel, required: false }
					]
				} );

				// Attach API keys to the partner entity
				partner.apiKeys = apiKeys;
			} else {
				// Ensure apiKeys is always set, even if empty
				partner.apiKeys = [];
			}
			return partnerMapper.toFullDTO( partner );
		})
	);

	return [count, partnersWithApiKeys];
}

async function getById( id, auth ) {
	const partner = await partnerModel.findByIdAndEnabled( id );

	if ( partner && partner.apiKeyIds && partner.apiKeyIds.length > 0 ) {
		// Fetch the API keys separately (only enabled ones)
		const apiKeys = await partnerApiKeyModel.findAll( {
			where: {
				id: partner.apiKeyIds,
				enabled: true
			},
			include: [
				{ as: 'client', model: clientModel, required: false }
			]
		} );
		partner.apiKeys = apiKeys;
	} else if ( partner ) {
		// Ensure apiKeys is always set, even if empty
		partner.apiKeys = [];
	}

	return partnerMapper.toFullDTO( partner );
}

async function revokeApiKey( partnerId, clientId, auth ) {
	// Find the partner and verify the client belongs to them
	const partner = await partnerModel.findByIdAndEnabled( partnerId );

	// Convert both to strings for comparison to handle type mismatches
	const clientIdStr = String( clientId );
	const clientIdsAsStrings = ( partner.clientIds || [] ).map( id => String( id ) );

	if ( !partner.clientIds || !clientIdsAsStrings.includes( clientIdStr ) ) {
		return Promise.reject( partnerError.clientNotAuthorized( clientId ) );
	}

	// Find the API key for this client and partner
	const apiKey = await partnerApiKeyModel.findOne( {
		where: {
			clientId: clientId,
			partnerId: partnerId,
			enabled: true
		}
	} );

	if ( !apiKey ) {
		return Promise.reject( partnerError.apiKeyNotFoundForClient( clientId, partnerId ) );
	}

	// Delete the API key entirely
	await apiKey.destroy();

	// Remove the API key ID from the partner's array
	const apiKeyIdStr = String( apiKey.id );
	partner.apiKeyIds = partner.apiKeyIds.filter( id => String( id ) !== apiKeyIdStr );
	partner.changed('apiKeyIds', true); // Explicitly mark the field as changed for Sequelize
	await partner.save();

	return {
		message: 'API key deleted successfully',
		apiKeyId: apiKey.id,
		clientId: clientId,
		partnerId: partnerId
	};
}

async function regenerateApiKey( partnerId, clientId, auth ) {
	// Find the partner and verify the client belongs to them
	const partner = await partnerModel.findByIdAndEnabled( partnerId );

	// Convert both to strings for comparison to handle type mismatches
	const clientIdStr = String( clientId );
	const clientIdsAsStrings = ( partner.clientIds || [] ).map( id => String( id ) );

	if ( !partner.clientIds || !clientIdsAsStrings.includes( clientIdStr ) ) {
		return Promise.reject( partnerError.clientNotAuthorized( clientId ) );
	}

	// Find the existing API key for this client and partner (if any)
	const oldApiKey = await partnerApiKeyModel.findOne( {
		where: {
			clientId: clientId,
			partnerId: partnerId,
			enabled: true
		}
	} );

	console.log('Old API key:', oldApiKey);

	let oldApiKeyId = null;

	// Capture the old API key ID BEFORE destroying it
	if ( oldApiKey ) {
		oldApiKeyId = oldApiKey.id;
	}

	console.log('Old API key ID:', oldApiKeyId);

	// Create a new API key for the client
	const newApiKeyResult = await partnerApiKeyService.createApiKey( clientId, partnerId );

	// Update the partner's apiKeyIds array
	let updatedApiKeyIds = partner.apiKeyIds || [];

	if ( oldApiKey ) {
		// Remove the old API key ID and add the new one
		const oldApiKeyIdStr = String( oldApiKeyId ); // Use the captured ID
		updatedApiKeyIds = updatedApiKeyIds.filter( id => String( id ) !== oldApiKeyIdStr );
		updatedApiKeyIds.push( newApiKeyResult.apiKeyId );

		// Now destroy the old API key after we've updated the array
		await oldApiKey.destroy();
	} else {
		// No existing key, just add the new one to the array
		updatedApiKeyIds.push( newApiKeyResult.apiKeyId );
	}

	// Update the partner with the new apiKeyIds array
	partner.apiKeyIds = updatedApiKeyIds;
	partner.changed('apiKeyIds', true); // Explicitly mark the field as changed for Sequelize
	await partner.save();

	// Return the new raw API key (only time it's returned)
	return {
		message: oldApiKey ? 'API key regenerated successfully' : 'API key created successfully',
		oldApiKeyId: oldApiKeyId,
		newApiKey: {
			id: newApiKeyResult.apiKeyId,
			clientId: clientId,
			rawKey: newApiKeyResult.rawKey,
			keySample: newApiKeyResult.rawKey.slice(-4)
		}
	};
}

// Private functions
function transformToEntity( criteria, auth ) {
	const fields = [
		{ name: 'apiEnabled', value: criteria.apiEnabled },
		{ name: 'name', value: criteria.name },
		{ name: 'clientIds', value: criteria.clientIds },
		{ name: 'features', value: criteria.features },
		{ name: 'apiKeyIds', value: criteria.apiKeyIds },
		{ name: 'enabled', value: criteria.enabled }
	];

	return fields;
}

export default {
	create,
	update,
	disable,
	search,
	getById,
	revokeApiKey,
	regenerateApiKey
};
