import ffmpeg from "fluent-ffmpeg";
import logger from "../../common/utils/logger";

async function convertToWebm({ inputPath, outputPath }) {
  logger.info("Converting to webm");
  await new Promise((resolve, reject) => {
    ffmpeg(inputPath)
      .output(outputPath)
      .videoCodec("libvpx")
      .audioCodec("libvorbis")
      .format("webm")
      .on("end", () => {
        resolve();
      })
      .on("error", (err) => {
        reject(err);
      })
      .run();
  });
}

export default convertToWebm;
