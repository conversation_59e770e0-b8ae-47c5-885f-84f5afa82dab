import ffmpeg from "fluent-ffmpeg";
import logger from "../../common/utils/logger";

async function convertToOgv({ inputPath, outputPath, sourceKey }) {
  logger.info("Converting to ogv");
  await new Promise((resolve, reject) => {
    ffmpeg(inputPath)
      .output(outputPath)
      .videoCodec("libtheora")
      .audioCodec("libvorbis")
      .on("end", () => {
        resolve();
      })
      .on("error", (err) => {
        reject(err);
      })
      .run();
  });
}

export default convertToOgv;
