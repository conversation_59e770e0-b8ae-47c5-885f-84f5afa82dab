import ffmpeg from "fluent-ffmpeg";
import logger from "../../common/utils/logger";
import generateAndUploadThumbnails from "../../common/generateAndUploadThumbnails";

// Get video stream information (ffprobe)
async function analyzeVideo(inputPath) {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(inputPath, (error, metadata) => {
      if (error) {
        console.error("Error getting video stream information", error);
        reject(error);
      } else {
        const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
        resolve(videoStream);
      }
    });
  });
}

export async function convertToMp4({
  inputPath,
  outputPath,
  sourceKey,
  duration,
  targetBucket,
}) {
  logger.info("Converting to MP4");

  let command = ffmpeg(inputPath)
    .output(outputPath)
    .outputOptions('-loglevel verbose')
    .videoCodec("libx264")
    .audioCodec("aac")
    .audioBitrate(128)
    .audioChannels(2)
    .format("mp4")
    .outputOptions("-preset fast") // Use a faster preset for encoding
    .outputOptions("-crf 23") // Adjust the CRF value for a balance between quality and speed
    .outputOptions("-threads 4"); // Use 4 threads for encoding

  const videoStreamInfo = await analyzeVideo(inputPath);

  logger.info(`Video stream info: ${JSON.stringify(videoStreamInfo)}`);

  // Rotate the video if necessary
  if (videoStreamInfo?.rotation === 90) {
    command.videoFilter("transpose=1");
  } else if (videoStreamInfo?.rotation === 270) {
    command.videoFilter("transpose=2");
  }

  // Fix Janelle's video processing issue
  const isHigh10Profile = videoStreamInfo?.profile === 'High 10';
  if (isHigh10Profile) {
    command
      .videoFilter("format=yuv420p")
      .outputOptions("-profile:v main")
      .audioCodec("copy");
  } else {
    // Adjust the video codec profile and format
    command
      .videoFilter("format=yuv420p") // Ensure color depth
      .outputOptions("-profile:v high")
    }

  // Fix bitrate and resolution
  command
    .outputOptions("-level 4.1")

  await new Promise(async (resolve, reject) => {
    command
      .on("start", (cmd) => console.log(`FFmpeg command: ${cmd}`))
      .on("end", () => {
        generateAndUploadThumbnails({
          sourceKey,
          targetBucket,
          localVideoFilePath: inputPath,
          duration,
        })
          .then(() => resolve())
          .catch(() => resolve());
      })
      .on("error", (error, stdout, stderr) => {
        console.error("Error converting to MP4", error, stderr);
        reject(error);
      })
      .on("stderr", (stderrLine) => console.error(`FFmpeg stderr: ${stderrLine}`))
      .run();
  });
}

export default {
  convertToMp4,
};
