import globals from './_globals.js';

function toDTO ( entity ) {
	const globalAttributes = globals.toDTOSuper( entity );
	const modelAttributes = {
		apiEnabled: entity.apiEnabled,
		name: entity.name,
		clientIds: entity.clientIds,
		features: entity.features,
		apiKeyIds: entity.apiKeyIds
	};
	const result = Object.assign( globalAttributes, modelAttributes );

	return result;
}

function toFullDTO ( entity ) {
	const result = toDTO( entity );

	// Handle multiple API keys (one per client) - always include apiKeys array, even if empty
	if ( entity.apiKeys && entity.apiKeys.length > 0 ) {
		result.apiKeys = entity.apiKeys.map( apiKey => ({
			id: apiKey.id,
			key: apiKey.key, // This is the hashed key
			keySample: apiKey.keySample, // Last 4 characters of raw key
			displayKey: `••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••${apiKey.keySample}`, // Formatted for display with dots and last 4 chars
			isClickable: true, // Hint for frontend that this should be styled as clickable
			clientId: apiKey.clientId,
			createdAt: apiKey.createdAt,
			updatedAt: apiKey.updatedAt
		}));
	} else {
		// Ensure apiKeys is always present, even if empty
		result.apiKeys = [];
	}

	// Note: rawApiKeys array is added separately in the service when creating a partner
	// It's not part of the entity and only returned once during creation

	return result;
}

function toListDTO ( entities ) {
	return ( entities || [] ).map( toFullDTO );
}

export default {
	toDTO,
	toFullDTO,
	toListDTO
};
