import globals from './_globals.js';

function toDTO ( entity ) {
	const globalAttributes = globals.toDTOSuper( entity );
	const modelAttributes = {
		key: entity.key,
		keySample: entity.keySample,
		clientId: entity.clientId,
		revoked: entity.revoked
	};
	const result = Object.assign( globalAttributes, modelAttributes );

	return result;
}

function toListDTO ( entities ) {
	return ( entities || [] ).map( toDTO );
}

export default {
	toDTO,
	toListDTO
};
