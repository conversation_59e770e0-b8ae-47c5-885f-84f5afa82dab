import axios from "axios";

function MarvinClient() {
	const baseUrl = 'https://api.polimorphic.com/ai';

	const citySiteAnswersEndpoint = 'generate-response/';

  async function getCitySiteAnswers(
    questionText,
    marvinOrgId // = "eeea839c-8d36-43c4-9550-3a9d2a0ae66b"
  ) {
    try {
      const response = await axios.post(`${baseUrl}/${citySiteAnswersEndpoint}`, {
        // user_id: "optional to fill after the first query",
        organization_id: marvinOrgId,
        context: 'ChatSearch',
        query: questionText,
        interaction_type: "SEARCH",
      });

      return response.data;
    } catch (error) {
      console.error("Error getting city site answers:", error);
      return [];
      // throw error;
    }
  }

  return {
    getCitySiteAnswers,
  };
}

export default MarvinClient();
