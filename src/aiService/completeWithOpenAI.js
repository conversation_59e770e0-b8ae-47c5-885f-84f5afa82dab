import { openai } from "../common/lib";

async function completeWithOpenAI(prompt) {
  try {
    console.log("ENV -> OPENAI Valid:", openai.apiKey);

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: prompt }],
      max_tokens: 100,
      temperature: 0,
    });

    return response.choices[0].message.content.trim();
  } catch (error) {
    console.error("Error completing prompt with OpenAI:", error);
    return [];
    // throw error;
  }
}
export default completeWithOpenAI;
