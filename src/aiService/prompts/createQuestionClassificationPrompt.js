import findSimilarQuestions from "../findSimilarQuestions";

async function createQuestionClassificationPrompt(question) {
  const similarQuestions = await findSimilarQuestions(question);

  const context = similarQuestions
    .map((q) => `Question: ${q.question}\nClassification: ${q.classification}`)
    .join("\n\n");

  return `Classify the given question as either "Simple" or "Complex". 
    A simple question typically has a straightforward, factual answer that can be found on a city's official website. 
    A complex question often involves multiple concepts and is one that a city's elected official might want to personally answer via a recorded video.

    Examples:
    ${context}

    Now, classify the following question:
    Question: ${question}

    Your output should strictly either be "Simple" or "Complex". Nothing else.
  `.replace(/\n +/, "\n");
}

export default createQuestionClassificationPrompt;
