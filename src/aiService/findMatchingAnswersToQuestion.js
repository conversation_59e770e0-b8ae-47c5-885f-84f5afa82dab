import { openai, supabase } from "../common/lib";
import logger from "../common/utils/logger";

export default async function findMatchingAnswersToQuestion(
  questionText,
  clientId
) {
  const embeddingResponse = await openai.embeddings.create({
    model: "text-embedding-3-small",
    input: questionText,
  });

  const [{ embedding }] = embeddingResponse.data;

  const queryObject = {
    query_embedding: embedding,
    match_threshold: 0.5,
    match_count: 10,
    client_id: clientId,
  };

  const { data: matchingAnswers, error } = await supabase.rpc(
    "match_answers_on_question_text",
    queryObject
  );

  // logger.info("Querying matching answers with embedding: " + JSON.stringify(queryObject));

  if (error) {
    logger.error("Failed to obtain matching answers: " + JSON.stringify(error));
  } else {
    logger.info(
      `Obtained matching answers: ${JSON.stringify(matchingAnswers)}`
    );
  }

  return matchingAnswers;
}
