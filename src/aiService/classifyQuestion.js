import logger from "../common/utils/logger";
import createQuestionClassificationPrompt from "./prompts/createQuestionClassificationPrompt";
import completeWithOpenAI from "./completeWithOpenAI";

async function classifyQuestion(question, api = "anthropic") {
  const prompt = await createQuestionClassificationPrompt(question);
  let classification;

  try {
    classification = await completeWithOpenAI(prompt);

    if (classification !== "Simple" && classification !== "Complex") {
      throw new Error(`Unexpected classification format: ${classification}`);
    }

    return classification;
  } catch (error) {
    logger.error("Error classifying question:", error);

    return 'Complex';
    // throw error;
  }
}

export default classifyQuestion;
