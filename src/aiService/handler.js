// Udated: 2024-08-22 17:39 EST

import logger from "../common/utils/logger";
import classifyQuestion from "./classifyQuestion";
import polymorphicClient from "./polymorphicClient";
import {
  badDataResponse,
  corsHeaders,
  internalErrorResponse,
  successResponse,
} from "../common/utils/responseUtil";
import findMatchingAnswersToQuestion from "./findMatchingAnswersToQuestion";

export async function handler(event) {
  // Handle OPTIONS request for CORS preflight
  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: "",
    };
  }

  logger.debug("Received event: ", event.body);
  const origin = event.headers?.origin || event.headers?.Origin || "No Origin";
  logger.debug(`Handling request from origin: ${origin}`);

  try {
    const { question, api, clientId, polymorphicOrgId } = JSON.parse(event.body);
    logger.debug(`CLIENT_ID: ${clientId}`);

    if (!question) {
      return badDataResponse("Question is required");
    }

    const classification = await classifyQuestion(question, api);

    console.log("CLASSIFICATION: ", classification);

    const matchingAnswers = await findMatchingAnswersToQuestion(
      question,
      clientId
    );
    const marvinAnswers = await polymorphicClient.getCitySiteAnswers(
      question,
      polymorphicOrgId
    );

    return successResponse({
      classification,
      marvinAnswers,
      matchingAnswers,
    });
  } catch (error) {
    logger.error("Error:", error);

    return internalErrorResponse();
  }
}
