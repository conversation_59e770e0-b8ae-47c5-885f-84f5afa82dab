import { openai, supabase } from "../common/lib";

async function findSimilarQuestions(questionText) {
  const embeddingResponse = await openai.embeddings.create({
    model: "text-embedding-3-small",
    input: questionText,
  });

  const [{ embedding }] = embeddingResponse.data;

  const { data: similarQuestions } = await supabase.rpc("match_questions", {
    query_embedding: embedding,
    match_threshold: 0.9,
    match_count: 20,
  });

  return similarQuestions;
}

export default findSimilarQuestions;
