import Anthropic from "@anthropic-ai/sdk";
const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});
async function completeWithAnthropic(prompt) {
  const anthropicPrompt = `Human: ${prompt}\n\nAssistant: `;
  const response = await anthropic.completions.create({
    model: "claude-2.1",
    max_tokens_to_sample: 10,
    prompt: anthropicPrompt,
    temperature: 0,
  });

  return response.completion.trim();
}
export default completeWithAnthropic;
