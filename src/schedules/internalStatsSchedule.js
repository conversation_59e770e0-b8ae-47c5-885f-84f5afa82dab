import cron from 'node-cron';
import { db } from '../global/database.js';
import ClientModel from '../models/clientModel.js';

async function loopThroughClients(kind='questionsAndUsers') {
	const clients = await ClientModel.findAll({
		where: { enabled: true },
		order: [['id', 'DESC']]
	});

	console.log(`Processing ${clients.length} clients`);

	if (!kind || kind === 'questionsAndUsers') {
		for (const index in clients) {
			const client = clients[index];

			if (client) {
				console.log(`Processing client ${client.id} (${index + 1}/${clients.length})`);
				await createUsersAndQuestionsTablesForClient(client.id);
			}
		}
	}

	if (!kind || kind === 'stats') {
		// Calculate batch based on current hour (1-6 AM = batches 0-5)
		const currentHour = new Date().getHours();
		const batchIndex = currentHour - 1; // 1 AM = batch 0, 2 AM = batch 1, etc.
		const batchSize = 10;
		const startIndex = batchIndex * batchSize;
		const endIndex = startIndex + batchSize;
			
		const clientBatch = clients.slice(startIndex, endIndex);
			
		console.log(`Processing batch ${batchIndex + 1} (clients ${startIndex + 1}-${Math.min(endIndex, clients.length)}) - ${clientBatch.length} clients`);

		for (const index in clientBatch) {
			const client = clientBatch[index];
			if (client) {
				const globalIndex = startIndex + parseInt(index);
				console.log(`Processing client ${client.id} (${globalIndex + 1}/${clients.length})`);
				await createStatsTablesForClient(client.id);
			}
		}
	}
}

/**
 * Creates precomputed tables for a specific client
 */
async function createUsersAndQuestionsTablesForClient(clientId) {
	try {
		console.log(`Processing tables for client #${clientId}`);

		// Drop existing tables
		await db.query(`
			DROP TABLE IF EXISTS looker_studio.precomputed_users_${clientId} CASCADE;
			DROP TABLE IF EXISTS looker_studio.precomputed_questions_${clientId} CASCADE;
		`);

		console.log(`Dropped tables for client #${clientId}`);

		// Create precomputed_users table
		await db.query(`
			CREATE TABLE looker_studio.precomputed_users_${clientId} AS
			SELECT u.* FROM users u WHERE u.client_id = ${clientId};
		`);

		console.log(`Created precomputed_users table for client #${clientId}`);

		// Create precomputed_questions table
		await db.query(`
			CREATE TABLE looker_studio.precomputed_questions_${clientId} AS
			SELECT * FROM questions WHERE client_id = ${clientId};
		`);

		console.log(`Completed processing client #${clientId}`);
	} catch (error) {
		console.error(`Error processing client #${clientId}:`, error);
		// throw error;
	}
}

/**
 * Creates precomputed tables for a specific client
 */
async function createStatsTablesForClient(clientId) {
	try {
		console.log(`Processing tables for client #${clientId}`);

		// Drop existing tables
		await db.query(`
			DROP TABLE IF EXISTS looker_studio.precomputed_stat_events_${clientId} CASCADE;
		`);

		console.log(`Created precomputed_questions table for client #${clientId}`);

		// Create precomputed_stat_events table
		await db.query(`
			CREATE TABLE looker_studio.precomputed_stat_events_${clientId} AS
			SELECT
				s.id AS "event_id",
				s.user_id AS "event_user_id",
				s.question_id AS "event_question_id",
				s.answer_id AS "event_answer_id",
				s.client_id AS "event_client_id",
				s.event_action AS "event_action",
				s.event_category AS "event_category",
				s.event_label AS "event_label",
				s.origin AS "event_origin",
				s.host AS "event_host",
				s.ipa AS "event_ipa",
				to_char(s.created_at, 'Mon DD, YYYY, H:MM A') AS "event_created",
				s.category AS "event_question_category",
				s.search_term AS "event_search_term",
				s.video_feedback_amount AS "event_video_feedback_amount",
				s.ai_search_query AS "ai_search_query",
				v.id AS "vote_id",
				v.answer_id AS "vote_answer_id",
				v.question_id AS "vote_question_id",
				to_char(v.created_at, 'Mon DD, YYYY, H:MM A') AS "vote_created",
				l.id AS "like_id",
				l.answer_id AS "like_answer_id",
				to_char(l.created_at, 'Mon DD, YYYY, H:MM A') AS "like_created",
				CASE WHEN s.question_text IS NOT NULL THEN s.question_text ELSE q.text END AS "question_text",
				q.category AS "question_category",
				to_char(q.created_at, 'Mon DD, YYYY, H:MM A') AS "question_created",
				u.id AS "user_id",
				concat(u.first_name, ' ', u.last_name) AS name,
				u.email AS "user_email",
				u.phone AS "user_phone",
				u.access_level AS "user_access_level",
				u.location AS "user_location",
				u.ipa AS "user_ipa",
				u.zip AS "user_zip",
				to_char(u.created_at, 'Mon DD, YYYY, H:MM A') AS "user_created",
				p.van_id AS "user_van_id",
				a.id AS "answer_id",
				a.question_id AS "answer_question_id",
				to_char(a.created_at, 'Mon DD, YYYY, H:MM A') AS "answer_created",
				c.name AS "client_name",
				c.created_at AS "client_created"
			FROM stats.stat_events s
			LEFT JOIN answers a ON s.answer_id = a.id
			LEFT JOIN looker_studio.precomputed_questions_${clientId} q ON s.question_id = q.id OR a.question_id = q.id
			LEFT JOIN votes v ON s.event_action = 'vote' AND v.question_id = q.id
			LEFT JOIN likes l ON s.event_action = 'likeVideo' AND l.answer_id = a.id
			LEFT JOIN looker_studio.precomputed_users_${clientId} u ON
				(v.user_id = u.id OR l.user_id = u.id OR s.user_id = u.id OR q.user_id = u.id)
			LEFT JOIN ngp_van_people p ON p.user_id = u.id
			LEFT JOIN clients c ON (s.client_id = c.id OR u.client_id = c.id)
			WHERE
				( s.client_id = ${clientId} OR u.client_id = ${clientId} OR q.client_id = ${clientId} )
			ORDER BY s.id;

			REFRESH MATERIALIZED VIEW mv_analytics_data;
			REFRESH MATERIALIZED VIEW mv_daily_pageviews;
		`);

		console.log(`Completed processing client #${clientId}`);
	} catch (error) {
		console.error(`Error processing client #${clientId}:`, error);
		// throw error;
	}
}

// /**
//  * Creates precomputed users and questions tables for each enabled client
//  */
// async function createUsersAndQuestionsTables() {
// 	try {
// 		console.log('Starting to create users and questions tables...');

// 		const clients = await ClientModel.findAll({
// 			where: {
// 				enabled: true
// 			}
// 		});

// 		for (const client of clients) {
// 			console.log(`Processing users and questions tables for client #${client.id}`);

// 			// Create precomputed_users table
// 			await db.query(`
//         CREATE TABLE IF NOT EXISTS looker_studio.precomputed_users_${client.id} AS
//         SELECT u.* FROM users u WHERE u.client_id = ${client.id};
//       `);

// 			// Create precomputed_questions table
// 			await db.query(`
//         CREATE TABLE IF NOT EXISTS looker_studio.precomputed_questions_${client.id} AS
//         SELECT * FROM questions WHERE client_id = ${client.id};
//       `);

// 			console.log(`Completed users and questions tables for client #${client.id}`);
// 		}

// 		console.log('Completed creating all users and questions tables');
// 	} catch (error) {
// 		console.error('Error creating users and questions tables:', error);
// 	}
// }

// /**
//  * Creates precomputed stat events tables for each enabled client
//  */
// async function createStatEventsTables() {
// 	try {
// 		console.log('Starting to create stat events tables...');

// 		const clients = await ClientModel.findAll({
// 			where: {
// 				enabled: true
// 			},
// 			order: [['id', 'DESC']]
// 		});

// 		const total = clients.length;
// 		console.log(`Total clients to process: ${total}`);

// 		for (const client of clients) {
// 			console.log(`Processing stat events table for client #${client.id}`);

// 			await db.query(`
//         DROP TABLE IF EXISTS looker_studio.precomputed_stat_events_${client.id} CASCADE;
//         CREATE TABLE IF NOT EXISTS looker_studio.precomputed_stat_events_${client.id} AS
//         SELECT
//             s.id AS "event_id",
//             s.user_id AS "event_user_id",
//             s.question_id AS "event_question_id",
//             s.answer_id AS "event_answer_id",
//             s.client_id AS "event_client_id",
//             s.event_action AS "event_action",
//             s.event_category AS "event_category",
//             s.event_label AS "event_label",
//             s.origin AS "event_origin",
//             s.host AS "event_host",
//             s.ipa AS "event_ipa",
//             to_char(s.created_at, 'Mon DD, YYYY, H:MM A') AS "event_created",
//             s.category AS "event_question_category",
//             s.search_term AS "event_search_term",
//             s.video_feedback_amount AS "event_video_feedback_amount",
//             s.ai_search_query AS "ai_search_query",
//             v.id AS "vote_id",
//             v.answer_id AS "vote_answer_id",
//             v.question_id AS "vote_question_id",
//             to_char(v.created_at, 'Mon DD, YYYY, H:MM A') AS "vote_created",
//             l.id AS "like_id",
//             l.answer_id AS "like_answer_id",
//             to_char(l.created_at, 'Mon DD, YYYY, H:MM A') AS "like_created",
//             CASE WHEN s.question_text IS NOT NULL THEN s.question_text ELSE q.text END AS "question_text",
//             q.category AS "question_category",
//             to_char(q.created_at, 'Mon DD, YYYY, H:MM A') AS "question_created",
//             u.id AS "user_id",
//             concat(u.first_name, ' ', u.last_name) AS name,
//             u.email AS "user_email",
//             u.phone AS "user_phone",
//             u.access_level AS "user_access_level",
//             u.location AS "user_location",
//             u.ipa AS "user_ipa",
//             u.zip AS "user_zip",
//             to_char(u.created_at, 'Mon DD, YYYY, H:MM A') AS "user_created",
//             p.van_id AS "user_van_id",
//             a.id AS "answer_id",
//             a.question_id AS "answer_question_id",
//             to_char(a.created_at, 'Mon DD, YYYY, H:MM A') AS "answer_created",
//             c.name AS "client_name",
//             c.created_at AS "client_created"
//         FROM stats.stat_events s
//         LEFT JOIN answers a ON s.answer_id = a.id
//         LEFT JOIN looker_studio.precomputed_questions_${client.id} q ON s.question_id = q.id OR a.question_id = q.id
//         LEFT JOIN votes v ON s.event_action = 'vote' AND v.question_id = q.id
//         LEFT JOIN likes l ON s.event_action = 'likeVideo' AND l.answer_id = a.id
//         LEFT JOIN looker_studio.precomputed_users_${client.id} u ON
//             (v.user_id = u.id OR l.user_id = u.id OR s.user_id = u.id OR q.user_id = u.id)
//         LEFT JOIN ngp_van_people p ON p.user_id = u.id
//         LEFT JOIN clients c ON (s.client_id = c.id OR u.client_id = c.id)
//         WHERE
//             ( s.client_id = ${client.id} OR u.client_id = ${client.id} OR q.client_id = ${client.id} )
//         ORDER BY s.id;
//       `);

// 			console.log(`Completed stat events table for client #${client.id}`);
// 		}

// 		console.log('Completed creating all stat events tables');
// 	} catch (error) {
// 		console.error('Error creating stat events tables:', error);
// 	}
// }

// /**
//  * Drops all precomputed tables for cleaning purposes
//  */
// async function dropAllTables() {
// 	try {
// 		console.log('Starting to drop all precomputed tables...');

// 		const clients = await ClientModel.findAll();

// 		for (const client of clients) {
// 			console.log(`Dropping tables for client #${client.id}`);

// 			await db.query(`
//         -- Drop and recreate precomputed_users table
//         DROP TABLE IF EXISTS looker_studio.precomputed_users_${client.id} CASCADE;

//         -- Drop and recreate precomputed_questions table
//         DROP TABLE IF EXISTS looker_studio.precomputed_questions_${client.id} CASCADE;

//         -- Drop and recreate precomputed_stat_events table
//         DROP TABLE IF EXISTS looker_studio.precomputed_stat_events_${client.id} CASCADE;

//         -- Drop and recreate compiled_stats table
//         DROP TABLE IF EXISTS looker_studio.compiled_stats_${client.id} CASCADE;
//       `);

// 			console.log(`Completed dropping tables for client #${client.id}`);
// 		}

// 		console.log('Completed dropping all precomputed tables');
// 	} catch (error) {
// 		console.error('Error dropping precomputed tables:', error);
// 	}
// }

// /**
//  * Main function to refresh all Looker Studio tables
//  */
// async function refreshLookerStudioTables() {
// 	try {
// 		console.log(`Starting Looker Studio tables refresh at ${new Date().toISOString()}`);

// 		// Step 1: Drop existing tables to clean slate
// 		await dropAllTables();

// 		// Step 2: Create users and questions tables
// 		await createUsersAndQuestionsTables();

// 		// Step 3: Create stat events tables
// 		await createStatEventsTables();

// 		console.log(`Completed Looker Studio tables refresh at ${new Date().toISOString()}`);
// 	} catch (error) {
// 		console.error('Error refreshing Looker Studio tables:', error);
// 	}
// }

/**
 * Refreshes all materialized views
 */
async function refreshMaterializedViews() {
	try {
		console.log(`Starting materialized views refresh at ${new Date().toISOString()}`);

		const views = [
			// 'mv_mail_stats',
			// 'mv_analytics',
			// 'mv_ai_queries',
			// 'mv_sentiments',
			// 'mv_questions_raw',
			// 'mv_bulk_sends',
			// 'mv_engagements',
			// 'mv_user_questions',
			// 'mv_votes_raw',
			// 'mv_categories',
			// 'mv_pageviews_raw',
			// 'mv_video_stats',
			// 'mv_mobile_desktop',
			// 'mv_referrals_raw',
			// 'mv_video_info',
			// 'mv_video_play_stats',
			// 'mv_feedback_raw'
			
			'mv_analytics_data',
			'mv_video_info_stats',
		];

		for (const view of views) {
			try {
				await db.query(`REFRESH MATERIALIZED VIEW ${view}`);
				console.log(`Refreshed materialized view: ${view}`);
			} catch (error) {
				console.error(`Error refreshing materialized view ${view}:`, error);
				// Continue with other views even if one fails
			}
		}

		console.log(`Completed materialized views refresh at ${new Date().toISOString()}`);
	} catch (error) {
		console.error('Error in refreshMaterializedViews:', error);
	}
}

// Schedule individual client processing with staggered timing
// Process clients individually every 30 minutes starting at 1:00 AM
cron.schedule('0 */2 * * *', async () => {
	if (process.env.NODE_ENV === 'staging') return;
	try {
		loopThroughClients('stats');
	} catch (error) {
		console.error('Error in individual client processing:', error);
	}
});

cron.schedule('*/30 */4 * * *', async () => {
	if (process.env.NODE_ENV === 'staging') return;
	try {
		loopThroughClients('questionsAndUsers');
		refreshMaterializedViews();
	} catch (error) {
		console.error('Error in individual client processing:', error);
	}
});

/**
 * Process tables for a single specific client
 */
async function processSpecificClient(clientId, kind = 'both') {
	try {
		console.log(`Processing specific client ${clientId} with kind: ${kind}`);

		// Verify client exists and is enabled
		const client = await ClientModel.findOne({
			where: { id: clientId, enabled: true }
		});

		if (!client) {
			console.error(`Client ${clientId} not found or not enabled`);
			return;
		}

		if (kind === 'questionsAndUsers' || kind === 'both') {
			console.log(`Processing users and questions tables for client ${clientId}`);
			await createUsersAndQuestionsTablesForClient(clientId);
		}

		if (kind === 'stats' || kind === 'both') {
			console.log(`Processing stats tables for client ${clientId}`);
			await createStatsTablesForClient(clientId);
		}

		console.log(`Completed processing client ${clientId}`);
	} catch (error) {
		console.error(`Error processing specific client ${clientId}:`, error);
		throw error;
	}
}

// Export function for processing specific client
export { loopThroughClients, processSpecificClient };
