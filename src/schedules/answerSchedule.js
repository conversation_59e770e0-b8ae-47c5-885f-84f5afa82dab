import cron from 'node-cron';
import { Op } from 'sequelize';
import axios from 'axios';
import ffmpeg from 'fluent-ffmpeg';

import { getJobName, getTranscriptionJob, startTranscriptionJob, deleteTranscriptionJob } from '../global/transcription.js';
import { translateTranscription } from '../global/translation.js';
import { createAnswerEmbeddings } from '../helpers/ai.js';

import '../models/index.js';

import answerModel from '../models/answerModel.js';
import questionModel from '../models/questionModel.js';
import clientModel from '../models/clientModel.js';


const getTranscriptions = async () => {
	console.log('Starting transcription job...');

	const answers = await answerModel.findAll({
		where: {
			transcriptionProcessing: {
				[Op.or]: {
					[Op.eq]: 'processing',
					[Op.is]: null
				}
			},
			enabled: true,
		},
		limit: 100,
		order: [['id', 'DESC']]//,
		// logging: console.log, // Logs the SQL query
	});

	for (const answer of answers) {
		const jobName = getJobName(answer.videoUrl);
		let transcription;

		try {
			transcription = await getTranscriptionJob(jobName);
		} catch (error) {
			// console.error(error);
		}

		if (!transcription) {
			startTranscriptionJob(answer);
			continue; // Skip if no transcription job found
		}

		// Transcription job details: {
		//   TranscriptionJob: {
		//     TranscriptionJobName: '14048006-5367-4f5f-8eb1-f73458c5ec88',
		//     TranscriptionJobStatus: 'COMPLETED',
		//     LanguageCode: 'en-US',
		//     MediaSampleRateHertz: 44100,
		//     MediaFormat: 'mp4',
		//     Media: {
		//       MediaFileUri: 's3://repd-api-files/raw/14048006-5367-4f5f-8eb1-f73458c5ec88.mp4'
		//     },
		//     Transcript: {
		//       TranscriptFileUri: 'https://s3.us-east-1.amazonaws.com/aws-transcribe-us-east-1-prod/301353944123/14048006-5367-4f5f-8eb1-f73458c5ec88/f7cd4fa6-814f-4622-919d-9d919152d6ef/asrOutput.json?X-Amz-Se
		// QoJb3JpZ2luX2VjEDwaCXVzLWVhc3QtMSJGMEQCIFezt1unl8zvWSYCkkiV32ysTV0S%2FCQRZKBlni%2F%2BDrWIYJQcOFGcXmY%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20240920T130800Z&X-Amz-SignedHeaders=host&X-Amz-Expir
		// redential=ASIAUA2QCFAAXEZC3ZC3%2F20240920%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=a04609f06f956c0af8fb6878dff83fa'
		//     },
		//     StartTime: 2024-09-20T13:04:15.165Z,
		//     CreationTime: 2024-09-20T13:04:15.146Z,
		//     CompletionTime: 2024-09-20T13:04:28.677Z,
		//     Settings: { ChannelIdentification: false, ShowAlternatives: false }
		//   }
		// }

		// check if transcription job is completed
		if (transcription.TranscriptionJob.TranscriptionJobStatus === 'FAILED') {
			if (transcription.TranscriptionJob.FailureReason?.match(/timed out|timeout|time out/i) !== null) {
				console.log('Transcription job timed out. Deleting job and retrying...');
				await deleteTranscriptionJob(answer);
				await startTranscriptionJob(answer);
			} else {
				answer.transcriptionProcessing = 'failed'; // Mark as done
				answer.showTranscribedSubtitles = false; // Show subtitles by default
				await answer.save();
			}

			continue; // Skip if failed
		}
		if (transcription.TranscriptionJob.TranscriptionJobStatus !== 'COMPLETED')
			continue; // Skip if not completed

		// Download the transcription file from the provided URI
		// Write file to local storage
		// const transcriptionResponse = await fs.promises.readFile(path.join(__dirname, 'transcription.json'), 'utf-8');
		const transcriptionFileUri = transcription.TranscriptionJob.Transcript.TranscriptFileUri;
		const transcriptionResponse = await axios.get(transcriptionFileUri);
		let transcriptionData = transcriptionResponse.data;

		if (answer.clientId === 278) { // Lorain
			transcriptionData = JSON.parse(JSON.stringify(transcriptionData).replace(/Lorraine/g, 'Lorain'));
		}

		// Transform the AWS Transcribe format to our expected format with audio_segments
		const transformedTranscription = {
			audio_segments: []
		};

		// AWS Transcribe returns results with transcripts (full text) and items (word-level details)
		if (transcriptionData.results && transcriptionData.results.transcripts && transcriptionData.results.transcripts.length > 0) {
			// Get the full transcript
			const fullTranscript = transcriptionData.results.transcripts[0].transcript;

			// If there are segments in the results (like speaker segments), use those
			if (transcriptionData.results.speaker_labels && transcriptionData.results.speaker_labels.segments) {
				transformedTranscription.audio_segments = transcriptionData.results.speaker_labels.segments.map((segment, index) => {
					return {
						id: index,
						start_time: segment.start_time,
						end_time: segment.end_time,
						transcript: segment.items.map(item => {
							const matchingItem = transcriptionData.results.items.find(i => i.start_time === item.start_time);
							return matchingItem ? matchingItem.alternatives[0].content : '';
						}).join(' ')
					};
				});
			} else {
				// If no segments, create a single segment with the full transcript
				transformedTranscription.audio_segments = [{
					id: 0,
					start_time: '0.0',
					end_time: transcriptionData.results.items.length > 0 ?
						transcriptionData.results.items[transcriptionData.results.items.length - 1].end_time : '0.0',
					transcript: fullTranscript
				}];
			}
		}

		// Save the transformed transcription
		answer.transcription = transformedTranscription;
		answer.transcriptionProcessing = 'complete';
		answer.showTranscribedSubtitles = true;

		// Make sure to save before translating
		await answer.save();
		// Then translate

		try {
			await translateTranscription(answer);
		} catch (error) {
			console.error('Error translating transcription:', error);
		}
	}

	console.log('Transcription jobs are processed.');
};

// Get transcription for an answer, save it and translate it, every minute
cron.schedule('* * * * *', async () => {
	getTranscriptions();
});

// Get embeddings for an answer, every minute
cron.schedule('* * * * *', async () => {
	const answers = await answerModel.findAll({
		where: {
			transcription: {
				[Op.ne]: null
			},
			question_embedding: null,
			enabled: true //,
		},
		include: [
			{ as: 'question', model: questionModel }
		],
		limit: 1,
		order: [['id', 'DESC']],
		logging: console.log, // Logs the SQL query
	});

	for (const answer of answers) {
		await createAnswerEmbeddings(answer);
	}

	console.log('Answer embedding jobs are processed.');
});

// Sync answers to Supabase, every hour
cron.schedule('* * * * *', async () => {
	console.log('Starting to sync answers to Supabase...');

	const endpoint = 'https://0v1o58pn3b.execute-api.us-east-1.amazonaws.com/staging/data-service/answers';

	try {
		// Get all enabled clients from the database
		const clients = await clientModel.findAll({
			where: {
				enabled: true
			},
			attributes: ['id'],
			order: [['id', 'DESC']]
		});

		const clientIds = clients.map(client => client.id);
		console.log(`Found ${clientIds.length} enabled clients to sync`);

		return;
		// Not working at the moment for some reason.
		// TODO: Fix this.

		for (const clientId of clientIds) {
			try {
				const payload = {
					resource: 'answers',
					method: 'get',
					payload: {
						excludeIds: [],
						clientId: clientId,
					}
				};

				console.log(`Making request for client ID: ${clientId}`);

				// Create URL with search params
				const url = new URL(endpoint);
				Object.keys(payload).forEach(key => url.searchParams.append(key, JSON.stringify(payload[key])));

				const response = await axios.get(url.toString());
				console.log(`Status Code: ${response.status}`);
				console.log(`Synced data for client ID: ${clientId}`);
			} catch (error) {
				// console.error(`Error syncing client ID ${clientId}:`, error.message);
			}

			// Add a small delay between requests to prevent overwhelming the API
			await new Promise(resolve => setTimeout(resolve, 1000));
		}
	} catch (error) {
		console.error('Error fetching clients:', error);
	}

	console.log('Answer sync to Supabase completed.');
});

// Calculate video durations for answers, every hour
cron.schedule('0 * * * *', async () => {
	console.log('Starting video duration calculation job...');

	try {
		const answers = await answerModel.findAll({
			where: {
				enabled: true,
				videoDuration: null,
				videoUrl: {
					[Op.ne]: null
				}
			},
			limit: 50,
			order: [['createdAt', 'DESC']]
		});

		console.log(`Found ${answers.length} answers needing duration calculation`);

		for (const answer of answers) {
			try {
				const videoPath = answer.videoUrl;

				if (videoPath) {
					await new Promise((resolve, reject) => {
						ffmpeg.ffprobe(videoPath, (err, metadata) => {
							if (err) {
								console.error(`Error getting duration for ${videoPath}:`, err);
								resolve(); // Continue with next item even if there's an error
							} else {
								const videoDuration = metadata.format.duration;

								answer.videoDuration = videoDuration;
								answer.save()
									.then(() => {
										console.log(`Updated duration for answer ID ${answer.id}: ${videoDuration}s`);
										resolve();
									})
									.catch(saveErr => {
										console.error(`Error saving duration for answer ID ${answer.id}:`, saveErr);
										resolve();
									});
							}
						});
					});
				}
			} catch (error) {
				console.error(`Error processing answer ID ${answer.id}:`, error.message);
			}

			// Add a small delay between processing videos
			await new Promise(resolve => setTimeout(resolve, 500));
		}

		console.log('Video duration calculation job completed.');
	} catch (error) {
		console.error('Error in video duration calculation job:', error);
	}
});

// Start transcription jobs for answers that need them, every hour
cron.schedule('30 * * * *', async () => {
	console.log('Starting batch transcription job starter...');

	try {
		const answers = await answerModel.findAll({
			where: {
				transcription: null,
				transcriptionProcessing: {
					[Op.is]: null
				},
				videoUrl: {
					[Op.ne]: null
				},
				enabled: true
			},
			limit: 20,
			order: [['createdAt', 'DESC']]
		});

		console.log(`Found ${answers.length} answers needing transcription jobs`);

		for (const answer of answers) {
			try {
				await startTranscriptionJob(answer);
				console.log(`Started transcription job for answer ID ${answer.id}`);

				// Add a small delay between starting jobs
				await new Promise(resolve => setTimeout(resolve, 1000));
			} catch (error) {
				console.error(`Error starting transcription job for answer ID ${answer.id}:`, error.message);
			}
		}

		console.log('Batch transcription job starter completed.');
	} catch (error) {
		console.error('Error in batch transcription job starter:', error);
	}
});

// Update video statuses for old answers, every 5 minutes
cron.schedule('*/2 * * * *', async () => {
	console.log('Starting legacy video status update job...');

	try {
		// Interval: 15 minutes
		const answersFinishedProcessing = new Date(Date.now() - (15 * 60 * 1000));

		const result = await answerModel.update(
			{
				mp4_video_status: 'done',
				ogv_video_status: 'done',
				webm_video_status: 'done'
			},
			{
				where: {
					createdAt: {
						[Op.lt]: answersFinishedProcessing
					},
					[Op.and]: {
						mp4_video_status: null,
						ogv_video_status: null,
						webm_video_status: null
					}
				}
			}
		);

		console.log(`Updated video statuses for ${result[0]} answers`);
	} catch (error) {
		console.error('Error in legacy video status update job:', error);
	}
});

getTranscriptions();
