import cron from 'node-cron';
import { db } from '../global/database.js';
import { createSentiment, categorize } from '../helpers/ai.js';
import questionModel from '../models/questionModel.js';
import aiQueryModel from '../models/aiQueryModel.js';
import questionShareModel from '../models/questionShareModel.js';
import userModel from '../models/userModel.js';
import sendGridUtil from '../utils/sendGridUtil.js';
import questionMailer from '../mailers/questionMailer.js';
import sessionService from '../services/sessionService.js';

// Use https://crontab.guru/ to verify cron job intervals.

// Run at 2AM every day
cron.schedule('0 2 * * *', () => {
	if (!['production', 'staging'].includes(process.env.NODE_ENV)) return;
	trackQuestionsInStatEvents();
});

// Run at 3AM every day
cron.schedule('0 3 * * *', () => {
	if (!['production', 'staging'].includes(process.env.NODE_ENV)) return;
	updateAnsweredQuestions();
});

// Run every 30 minutes
// cron.schedule('*/30 * * * *', () => {
// 	if (!['production', 'staging'].includes(process.env.NODE_ENV)) return;
// 	migrateAIQueries();
// });

// Run at 4AM every day
cron.schedule('0 4 * * *', () => {
	if (!['production', 'staging'].includes(process.env.NODE_ENV)) return;
	generateSentimentsAndCategories();
});

// Run every hour to update email tracking data
cron.schedule('0 * * * *', () => {
	if (!['production', 'staging'].includes(process.env.NODE_ENV)) return;
	updateShareEmailTrackingData();
});

// Run every hour to send reminder emails for unanswered questions
cron.schedule('0 * * * *', () => {
	if (!['production', 'staging'].includes(process.env.NODE_ENV)) return;
	sendQuestionShareReminders();
});

// Records questions that have not been tracked in stat_events
export const trackQuestionsInStatEvents = async () => {
	console.log('Question Schedule -> trackQuestionsInStatEvents -> Starting');

	try {
		const query = `
      INSERT INTO stats.stat_events (
        created_at, updated_at, enabled, user_id, question_id, answer_id, client_id, export_job_id, saved_list_id,
        event_action, event_category, event_label, origin, host, ipa, question_text, category, search_term, video_feedback_amount,
        first_name, last_name, email, phone, zip, access_level, export_job_date, original)
      SELECT
          NOW(),NOW(),true,uq.user_id,uq.id,null,uq.client_id,null,null,
          'sendQuestion','Questions','manuallyCreatedStatQuestion',
          CASE WHEN uq.access_level ILIKE '%admin%' THEN
              'https://admin.repd.us'
          ELSE
              'https://app.repd.us'
          END AS origin,
          'undefined',uq.ipa,uq.text,uq.category,null,null,
          uq.first_name,uq.last_name,uq.email,uq.phone,uq.zip,uq.access_level,null,'{}'
      FROM (
          SELECT DISTINCT
              q.user_id,q.id,q.client_id,q.text,q.category,q.created_at,
              u.ipa,u.first_name,u.last_name,u.email,u.phone,u.zip,CAST( u.access_level AS VARCHAR )
          FROM questions q
            LEFT JOIN users u ON q.user_id = u.id
          WHERE q.text NOT IN (
              SELECT DISTINCT question_text FROM stat_events WHERE event_action='sendQuestion'
          ) AND q.client_id IS NOT NULL
      ) uq;
    `;

		const result = await db.query(query);
		console.log(`Question Schedule -> trackQuestionsInStatEvents -> Processed ${result.rowCount || 0} questions`);
	} catch (error) {
		console.error('Question Schedule -> trackQuestionsInStatEvents -> Error:', error);
	}
};

// Updates questions that have been answered to be marked as answered and disabled
export const updateAnsweredQuestions = async () => {
	console.log('Question Schedule -> updateAnsweredQuestions -> Starting');

	try {
		const query = `
			UPDATE ai_queries
			SET    original = CASE
         WHEN original::text LIKE '"{%'             -- double-encoded
         THEN ( ('[' || original || ']')::jsonb ->> 0 )::jsonb
         ELSE original::jsonb                 -- already clean
       END;

      UPDATE questions SET enabled = false, is_answered = true WHERE id in (
        SELECT q.id FROM questions q
            LEFT JOIN answers a on q.id = a.question_id
        WHERE q.enabled = true and q.id = a.question_id and a.enabled = true
      );
    `;

		const result = await db.query(query);
		console.log(`Question Schedule -> updateAnsweredQuestions -> Updated ${result.rowCount || 0} questions`);
	} catch (error) {
		console.error('Question Schedule -> updateAnsweredQuestions -> Error:', error);
	}
};

// Migrates search query stat events to the ai_queries table
// export const migrateAIQueries = async () => {
// 	console.log('Question Schedule -> migrateAIQueries -> Starting');

// 	try {
// 		const query = `
// 			INSERT INTO ai_queries (user_id, question_id, answer_id, client_id, query, original, category, sentiment, created_at)
// 			SELECT
// 					se.user_id, se.question_id, se.answer_id, se.client_id, se.ai_search_query,
// 					se.original::JSON, null, null, se.created_at
// 			FROM stats.stat_events se
// 			WHERE se.event_category = 'search_query'
// 			AND NOT EXISTS (
// 					SELECT 1
// 					FROM ai_queries aq
// 					WHERE aq.query = se.ai_search_query
// 						AND aq.created_at = se.created_at
// 			);
//     `;

// 		const result = await db.query(query);
// 		console.log(`Question Schedule -> migrateAIQueries -> Migrated ${result.rowCount || 0} AI queries`);
// 	} catch (error) {
// 		console.error('Question Schedule -> migrateAIQueries -> Error:', error);
// 	}
// };

// Generates sentiments and categories for questions and AI queries
export const generateSentimentsAndCategories = async () => {
	console.log('Question Schedule -> generateSentimentsAndCategories -> Starting');

	try {
		// Get questions without sentiment
		const questions = await questionModel.findAll({
			where: { sentiment: null, enabled: true },
			limit: 100 // Process in batches to avoid timeout
		});

		// Get AI queries without sentiment
		const aiSentimentQueries = await aiQueryModel.findAll({
			where: { sentiment: null, enabled: true },
			limit: 100
		});

		// Get AI queries without category
		const aiCategoryQueries = await aiQueryModel.findAll({
			where: { category: null, enabled: true },
			limit: 100
		});

		console.log(`Question Schedule -> generateSentimentsAndCategories -> Processing ${questions.length} questions, ${aiSentimentQueries.length} AI queries for sentiment, ${aiCategoryQueries.length} AI queries for category`);

		// Process questions for sentiment
		for (const question of questions) {
			try {
				const sentiment = await createSentiment(question.text);
				question.sentiment = sentiment;
				question.changed('sentiment', true);
				await question.save();
			} catch (err) {
				console.error(`Error processing sentiment for question ${question.id}: ${err.message}`);
			}
		}

		// Process AI queries for sentiment
		for (const aiQuery of aiSentimentQueries) {
			try {
				const sentiment = await createSentiment(aiQuery.query);
				aiQuery.sentiment = sentiment;
				aiQuery.changed('sentiment', true);
				await aiQuery.save();
			} catch (err) {
				console.error(`Error processing sentiment for AI query ${aiQuery.id}: ${err.message}`);
			}
		}

		// Process AI queries for category
		for (const aiQuery of aiCategoryQueries) {
			try {
				const category = await categorize(aiQuery.query);
				aiQuery.category = category;
				aiQuery.changed('category', true);
				await aiQuery.save();
			} catch (err) {
				console.error(`Error processing category for AI query ${aiQuery.id}: ${err.message}`);
			}
		}

		console.log('Question Schedule -> generateSentimentsAndCategories -> Completed');
	} catch (error) {
		console.error('Question Schedule -> generateSentimentsAndCategories -> Error:', error);
	}
};

// Updates share tracking data (opens and clicks) from SendGrid
export const updateShareEmailTrackingData = async () => {
	console.log('Question Schedule -> updateShareEmailTrackingData -> Starting');

	try {
		const shares = await questionShareModel.findAll({
			where: {
				enabled: true,
				[db.Sequelize.Op.or]: [
					{ isOpened: false },
					{ isClicked: false }
				]
			},
			include: [
				{
					as: 'user',
					model: userModel,
					attributes: ['id', 'email'],
					where: { enabled: true },
					required: false
				}
			],
			limit: 200
		});

		console.log(`Question Schedule -> updateShareEmailTrackingData -> Processing ${shares.length} shares`);

		let updatedShares = 0;

		for (const share of shares) {
			const email = share.user?.email || share.email;

			if (email && share.createdAt) {
				const { opens, clicks } = await sendGridUtil.getQuestionInviteEmailActivity(email, share.createdAt);

				let needsUpdate = false;
				let updateData = {};

				if (!share.isOpened && opens > 0) {
					updateData.isOpened = true;
					needsUpdate = true;
				}

				if (!share.isClicked && clicks > 0) {
					updateData.isClicked = true;
					needsUpdate = true;
				}

				if (needsUpdate) {
					await share.update(updateData);
					updatedShares++;
				}
			}

			// Add a small delay to avoid rate limiting
			await new Promise(resolve => setTimeout(resolve, 100));
		}

		console.log(`Question Schedule -> updateShareEmailTrackingData -> Updated ${updatedShares} shares`);
	} catch (error) {
		console.error('Question Schedule -> updateShareEmailTrackingData -> Error:', error);
	}
};

// Sends reminder emails for question shares that are approaching their due date
export const sendQuestionShareReminders = async () => {
	console.log('Question Schedule -> sendQuestionShareReminders -> Starting');

	try {
		const now = new Date();

		// Find shares that are not answered, have a due date
		const shares = await questionShareModel.findAll({
			where: {
				enabled: true,
				isAnswered: false,
				dueDate: {
					[db.Sequelize.Op.ne]: null
				},
			},
			include: [
				{
					as: 'question',
					model: questionModel,
					include: [
						{ as: 'client', model: db.models.clients, required: false }
					],
					required: true
				},
				{
					as: 'user',
					model: userModel,
					required: true
				}
			]
		});

		console.log(`Question Schedule -> sendQuestionShareReminders -> Found ${shares.length} unanswered shares with due dates`);

		let remindersSent = 0;
		const reminderIntervals = [12, 24, 48]; // Hours before due date

		for (const share of shares) {
			const dueDate = new Date(share.dueDate);
			const hoursUntilDue = Math.round((dueDate - now) / (1000 * 60 * 60));

			const shouldSendReminder = reminderIntervals.some(interval => {
				return Math.abs(hoursUntilDue - interval) <= 0.5;
			});

			if (shouldSendReminder) {
				const session = await sessionService.promiseCreateAndTransform(share.user);

				questionMailer.sendQuestionReminderEmail(
					share.question,
					session,
					hoursUntilDue
				);
				remindersSent++;

				await new Promise(resolve => setTimeout(resolve, 100));
			}
		}

		console.log(`Question Schedule -> sendQuestionShareReminders -> Sent ${remindersSent} reminder emails`);
	} catch (error) {
		console.error('Question Schedule -> sendQuestionShareReminders -> Error:', error);
	}
};
