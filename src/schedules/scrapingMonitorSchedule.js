
import cron from 'node-cron';
import { db<PERSON>ar<PERSON> } from '../global/database.js';
import { Op } from 'sequelize';

import '../models/index.js';
import clientModel from '../models/clientModel.js';
import globalMailer from '../mailers/_global.js';
import aiQueryModel from '../models/aiQueryModel.js';

// Check site scraping status every 24 hours at 9 AM
cron.schedule('0 9 * * *', async () => {
	if (!['production', 'staging'].includes(process.env.NODE_ENV)) return;
	await checkScrapingStatus();
});

// Sync question embeddings to ai_queries every hour
cron.schedule('0 * * * *', async () => {
	if (!['production', 'staging'].includes(process.env.NODE_ENV)) return;
	await syncQuestionEmbeddingsToAiQueries();
});

// Check scraping status for all sites and send warning email if needed
async function checkScrapingStatus() {
	try {
		console.log('Starting scraping status check...');

		// Get all enabled clients
		const clients = await clientModel.findAll({
			where: {
				enabled: true
			}
		});

		console.log(`Found ${clients.length} enabled clients`);

		// Check each client's sites
		for (const client of clients) {
			await checkClientSites(client);
		}

		console.log('Scraping status check completed');
	} catch (error) {
		console.error('Error in scraping status check:', error);
	}

	/**
	 * Check sites for a specific client
	 */
	async function checkClientSites(client) {
		try {
		// Get sites for this client from Marvin DB
			const [sites] = await dbMarvin.query(`
			SELECT id, name, url, last_scraped_at, client_id
			FROM sites 
			WHERE client_id = :clientId 
			AND enabled = true
		`, {
				replacements: { clientId: client.id }
			});

			if (sites.length === 0) {
				console.log(`No sites found for client ${client.name} (ID: ${client.id})`);
				return;
			}

			console.log(`Checking ${sites.length} sites for client ${client.name}`);

			// Check if any site has been scraped within the last 24 hours
			const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
			const recentlyScrapedSites = sites.filter(site => 
				site.last_scraped_at && new Date(site.last_scraped_at) > twentyFourHoursAgo
			);

			if (recentlyScrapedSites.length === 0) {
				console.log(`No recently scraped sites found for client ${client.name}. Sending warning email.`);
				await sendScrapingWarningEmail(client, sites);
			} else {
				console.log(`Client ${client.name} has ${recentlyScrapedSites.length} recently scraped sites`);
			}
		} catch (error) {
			console.error(`Error checking sites for client ${client.name}:`, error);
		}
	}

	/**
	 * Send warning email about scraping issues
	 */
	async function sendScrapingWarningEmail(client, sites) {
		try {
			const { message, setMailDefaults, send } = globalMailer;

			// Set email defaults
			setMailDefaults();
		
			message.to = ['<EMAIL>'];
			message.subject = `Scraping Warning: Possible issues for ${client.name}`;
		
			// Create site details for email
			const siteDetails = sites.map(site => {
				const lastScraped = site.last_scraped_at 
					? new Date(site.last_scraped_at).toLocaleString()
					: 'Never';
				return `• ${site.name || site.url} - Last scraped: ${lastScraped}`;
			}).join('\n');

			// Create email content
			message.html = `
			<h2>Scraping Warning Alert</h2>
			<p><strong>Client:</strong> ${client.name} (ID: ${client.id})</p>
			<p><strong>Issue:</strong> No sites have been scraped within the last 24 hours</p>
			
			<h3>Site Details:</h3>
			<pre>${siteDetails}</pre>
			
			<p>Please check the scraping system for potential issues.</p>
			
			<p><em>This is an automated alert from the Rep'd scraping monitoring system.</em></p>
		`;

			// Send the email
			await send();
			console.log(`Warning email sent for client ${client.name}`);
		} catch (error) {
			console.error(`Error sending warning email for client ${client.name}:`, error);
		}
	}
}

// Get queries from question embeddings from marvin db and add them to ai_queries table.
async function syncQuestionEmbeddingsToAiQueries() {
	try {
		console.log('Starting question embeddings sync to ai_queries...');

		// Get question embeddings from Marvin DB that haven't been synced yet
		const [questionEmbeddings] = await dbMarvin.query(`
			SELECT qe.id, qe.question, qe.sources, qe.client_id, qe.user_id, qe.created_at
			FROM question_embeddings qe
			ORDER BY qe.created_at DESC
		`);

		console.log(`Found ${questionEmbeddings.length} question embeddings to sync`);

		// Insert into ai_queries table with deduplication
		for (const qe of questionEmbeddings) {
			const original = JSON.stringify({ source: 'marvin.question_embeddings', sources: qe.sources, marvin_id: qe.id, user_id: qe.user_id });

			// Check if this query already exists
			const existingQuery = await aiQueryModel.findOne({
				where: {
					query: qe.question,
					clientId: qe.client_id,
					[Op.and]: [
						// origin ILIKE
						{ original: { [Op.like]: '%marvin.question_embeddings%' } },
						{ original: { [Op.like]: `%${qe.id}%` } },
						// { 'origin.source': 'marvin.question_embeddings' },
						// { 'original.marvin_id': qe.id }
					]
				}
			});

			console.log(`Query exists: ${existingQuery ? 'Yes' : 'No'}`);

			if (!existingQuery) {
				await aiQueryModel.create({
					query: qe.question,
					clientId: qe.client_id,
					userId: qe.user_id,
					original,
					createdAt: qe.created_at
				});
			}
		}

		console.log(`Successfully synced ${questionEmbeddings.length} question embeddings to ai_queries`);
	} catch (error) {
		console.error('Error syncing question embeddings to ai_queries:', error);
	}
}

syncQuestionEmbeddingsToAiQueries();

// Export function for manual triggering if needed
export { checkScrapingStatus, syncQuestionEmbeddingsToAiQueries };
