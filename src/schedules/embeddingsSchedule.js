// for i in {0..50}; do   node src/schedules/embeddingsSchedule.js $i 570;   sleep 120; done

import cron from 'node-cron';
import { db, db<PERSON><PERSON><PERSON> } from '../global/database.js';
import { Op } from 'sequelize';

import '../models/index.js';

import answerModel from '../models/answerModel.js';
import questionModel from '../models/questionModel.js';

import { openai } from '../global/ai.js';

// Sync answer embeddings from DB to Marvin DB every hour
cron.schedule('0 * * * *', async () => {
	if (!['production', 'staging'].includes(process.env.NODE_ENV)) return;
	await syncAnswerEmbeddings();
});

// create table answer_embeddings
// (
//     id                         serial
//         primary key,
//     supabase_id                bigint not null
//         unique,
//     question                   text,
//     question_embedding         vector(1536),
//     transcription              text,
//     transcription_embedding    vector(1536),
//     repd_answer_id             bigint,
//     repd_client_id             bigint,
//     question_es                text,
//     question_es_embedding      vector(1536),
//     transcription_es           text,
//     transcription_es_embedding vector(1536),
//     created_at                 timestamp,
//     updated_at                 timestamp default now()
// );

/**
 * Syncs answer embeddings from application DB to Marvin DB
 */
async function syncAnswerEmbeddings(offset = 0, limit = 100) {
	console.log(`Starting answer embeddings sync job (offset: ${offset}, limit: ${limit})...`);

	try {
		// Get last synced timestamp from Marvin DB
		const [lastSyncResult] = await dbMarvin.query(`
			SELECT MAX(last_synced_at) as last_sync
			FROM embedding_sync_status
			WHERE embedding_type = 'answer'
		`);

		const lastSyncTime = lastSyncResult[0]?.last_sync || new Date(0);
		console.log(`Last answer embeddings sync: ${lastSyncTime}`);

		// Fetch new embeddings from application DB
		const answers = await answerModel.findAll({
			where: {
				enabled: true,
			},
			include: [
				{
					model: questionModel,
					as: 'question',
					required: true
				}
			],
			order: [['id', 'ASC']],
			offset: offset,
			limit: limit
		});

		console.log(`Found ${answers.length} answers to sync (offset: ${offset})`);

		if (answers.length === 0) {
			return 0; // Return 0 to indicate no more answers to process
		}

		// Get list of already synced answer IDs with their updated timestamps
		const [syncedAnswers] = await dbMarvin.query(`
			SELECT repd_answer_id, updated_at 
			FROM answer_embeddings 
			WHERE repd_answer_id IN (:answerIds)
		`, {
			replacements: {
				answerIds: answers.map(a => a.id)
			}
		});

		// Create a map of synced answer IDs to their updated timestamps
		const syncedAnswersMap = new Map();
		syncedAnswers.forEach(sa => {
			syncedAnswersMap.set(sa.repd_answer_id, new Date(sa.updated_at));
		});

		// Process in batches
		const batchSize = 20;
		for (let i = 0; i < answers.length; i += batchSize) {
			const batch = answers.slice(i, i + batchSize);

			// Begin transaction
			const transaction = await dbMarvin.transaction();

			try {
				for (const answer of batch) {
					// Check if answer already exists in Marvin DB and is up-to-date
					const syncedTimestamp = syncedAnswersMap.get(answer.id);
					const answerUpdatedAt = new Date(answer.updatedAt);
					
					// Skip if answer is already synced and hasn't been updated since
					if (syncedTimestamp && syncedTimestamp >= answerUpdatedAt) {
						console.log(`Skipping answer #${answer.id} - already up-to-date in Marvin DB`);
						continue;
					}

					// Handle transcription - ensure it's a string
					let transcription = null;
					if (answer.transcription) {
						transcription = typeof answer.transcription === 'string'
							? answer.transcription
							: answer.transcription.audio_segments
								? answer.transcription.audio_segments.map(segment => segment.transcript).join(' ')
								: JSON.stringify(answer.transcription);
					}

					// Get question text safely
					const questionText = answer.question?.text || '';

					// Get Spanish translations if available
					const questionEs = (answer.question?.translations || {})['es'] || null;
					const transcriptionEs = (answer.transcription_translation || {})['es']?.audio_segments
						? answer.transcription_translation['es'].audio_segments.map(segment => segment.transcript).join(' ')
						: null;

					// Skip if no content to embed
					if (!questionText && !transcription) {
						console.log(`Skipping answer #${answer.id} - no content to embed`);
						continue;
					}

					// Use existing embeddings from the answer model
					let questionEmbedding = answer.questionEmbedding;
					let transcriptionEmbedding = answer.transcriptionEmbedding;
					let questionEsEmbedding = answer.questionTranslationEmbedding?.es;
					let transcriptionEsEmbedding = answer.transcriptionTranslationEmbedding?.es;

					// Generate missing embeddings
					const embeddingsToGenerate = [];
					const embeddingFields = [];

					if (!questionEmbedding && questionText) {
						embeddingsToGenerate.push(questionText);
						embeddingFields.push('question');
					}
					
					if (!transcriptionEmbedding && transcription) {
						embeddingsToGenerate.push(transcription);
						embeddingFields.push('transcription');
					}
					
					if (!questionEsEmbedding && questionEs) {
						embeddingsToGenerate.push(questionEs);
						embeddingFields.push('questionEs');
					}
					
					if (!transcriptionEsEmbedding && transcriptionEs) {
						embeddingsToGenerate.push(transcriptionEs);
						embeddingFields.push('transcriptionEs');
					}

					// Generate embeddings if needed
					if (embeddingsToGenerate.length > 0) {
						try {
							console.log(`Generating ${embeddingsToGenerate.length} missing embeddings for answer #${answer.id}`);
							
							const embeddingResponse = await openai.embeddings.create({
								model: "text-embedding-3-small",
								input: embeddingsToGenerate,
							});
							
							// Assign generated embeddings to their respective fields
							for (let i = 0; i < embeddingFields.length; i++) {
								const field = embeddingFields[i];
								const embedding = embeddingResponse.data[i].embedding;
								
								if (field === 'question') {
									questionEmbedding = embedding;
									answer.questionEmbedding = embedding;
								} else if (field === 'transcription') {
									transcriptionEmbedding = embedding;
									answer.transcriptionEmbedding = embedding;
								} else if (field === 'questionEs') {
									questionEsEmbedding = embedding;
									if (!answer.questionTranslationEmbedding) {
										answer.questionTranslationEmbedding = { es: embedding };
									} else {
										answer.questionTranslationEmbedding.es = embedding;
									}
								} else if (field === 'transcriptionEs') {
									transcriptionEsEmbedding = embedding;
									if (!answer.transcriptionTranslationEmbedding) {
										answer.transcriptionTranslationEmbedding = { es: embedding };
									} else {
										answer.transcriptionTranslationEmbedding.es = embedding;
									}
								}
							}
							
							// Save the updated embeddings to the Rep'd database
							await answer.save();
							
						} catch (error) {
							console.error(`Error generating embeddings for answer #${answer.id}:`, error);
							continue; // Skip this answer if embedding generation fails
						}
					}

					// Ensure embeddings are arrays of numbers
					const ensureNumericArray = (embedding) => {
						if (!embedding) return null;
						
						// If it's already an array of numbers, return it
						if (Array.isArray(embedding) && typeof embedding[0] === 'number') {
							return embedding;
						}
						
						// If it's a string, try to parse it
						if (typeof embedding === 'string') {
							try {
								const parsed = JSON.parse(embedding);
								if (Array.isArray(parsed)) {
									return parsed;
								}
							} catch (e) {
								console.error('Failed to parse embedding string:', e);
							}
						}
						
						return null;
					};

					const questionEmbeddingArray = ensureNumericArray(questionEmbedding);
					const transcriptionEmbeddingArray = ensureNumericArray(transcriptionEmbedding);
					const questionEsEmbeddingArray = ensureNumericArray(questionEsEmbedding);
					const transcriptionEsEmbeddingArray = ensureNumericArray(transcriptionEsEmbedding);

					// Skip if no embeddings
					if (!questionEmbeddingArray || !transcriptionEmbeddingArray) {
						console.log(`Skipping answer #${answer.id} - missing required embeddings`);
						continue;
					}

					// Insert or update embedding in Marvin DB
					// First check if the record exists
					const [existingRecord] = await dbMarvin.query(`
						SELECT repd_answer_id FROM answer_embeddings 
						WHERE repd_answer_id = :repd_answer_id
					`, {
						replacements: {
							repd_answer_id: answer.id
						},
						transaction
					});

					if (existingRecord && existingRecord.length > 0) {
						// Update existing record
						await dbMarvin.query(`
							UPDATE answer_embeddings SET
								question = :question,
								question_embedding = :question_embedding::vector,
								transcription = :transcription,
								transcription_embedding = :transcription_embedding::vector,
								question_es = :question_es,
								question_es_embedding = ${questionEsEmbeddingArray ? ':question_es_embedding::vector' : 'NULL'},
								transcription_es = :transcription_es,
								transcription_es_embedding = ${transcriptionEsEmbeddingArray ? ':transcription_es_embedding::vector' : 'NULL'},
								updated_at = NOW()
							WHERE repd_answer_id = :repd_answer_id
						`, {
							replacements: {
								question: questionText,
								question_embedding: `[${questionEmbeddingArray.join(',')}]`,
								transcription: transcription,
								transcription_embedding: `[${transcriptionEmbeddingArray.join(',')}]`,
								question_es: questionEs,
								...(questionEsEmbeddingArray ? { question_es_embedding: `[${questionEsEmbeddingArray.join(',')}]` } : {}),
								transcription_es: transcriptionEs,
								...(transcriptionEsEmbeddingArray ? { transcription_es_embedding: `[${transcriptionEsEmbeddingArray.join(',')}]` } : {}),
								repd_answer_id: answer.id
							},
							transaction
						});
					} else {
						// Insert new record
						await dbMarvin.query(`
							INSERT INTO answer_embeddings (
								question, question_embedding, transcription,
								transcription_embedding, repd_answer_id, repd_client_id,
								question_es, question_es_embedding,
								transcription_es, transcription_es_embedding,
								created_at
							) VALUES (
								:question, :question_embedding::vector, :transcription,
								:transcription_embedding::vector, :repd_answer_id, :repd_client_id,
								:question_es, ${questionEsEmbeddingArray ? ':question_es_embedding::vector' : 'NULL'},
								:transcription_es, ${transcriptionEsEmbeddingArray ? ':transcription_es_embedding::vector' : 'NULL'},
								:created_at
							)
						`, {
							replacements: {
								question: questionText,
								question_embedding: `[${questionEmbeddingArray.join(',')}]`,
								transcription: transcription,
								transcription_embedding: `[${transcriptionEmbeddingArray.join(',')}]`,
								question_es: questionEs,
								...(questionEsEmbeddingArray ? { question_es_embedding: `[${questionEsEmbeddingArray.join(',')}]` } : {}),
								transcription_es: transcriptionEs,
								...(transcriptionEsEmbeddingArray ? { transcription_es_embedding: `[${transcriptionEsEmbeddingArray.join(',')}]` } : {}),
								repd_answer_id: answer.id,
								repd_client_id: answer.clientId,
								created_at: answer.createdAt
							},
							transaction
						});
					}
				}

				// Update sync status
				const latestTimestamp = answers[answers.length - 1].updatedAt;
				await dbMarvin.query(`
					INSERT INTO embedding_sync_status (embedding_type, last_synced_at, records_synced)
					VALUES ('answer', :timestamp, :count)
				`, {
					replacements: {
						timestamp: latestTimestamp,
						count: answers.length
					},
					transaction
				});

				await transaction.commit();
				console.log(`Successfully synced batch of ${batch.length} answer embeddings`);
			} catch (error) {
				await transaction.rollback();
				console.error('Error syncing answer embeddings batch:', error);
			}
		}

		return answers.length; // Return the number of processed answers
	} catch (error) {
		console.error('Error in answer embeddings sync job:', error);
		return 0;
	}
}

// Export functions for manual triggering if needed
export default async function embeddingsSchedule() {
	try {
		console.log(`Manually starting embeddings sync at ${new Date().toISOString()}`);
		
		// Process with offset loop
		let offset = 0;
		const limit = 100;
		let processedCount = 0;
		let totalProcessed = 0;
		
		// Get offset and limit from command line arguments if provided
		const args = process.argv.slice(2);
		if (args.length >= 1) {
			offset = parseInt(args[0], 10) * limit;
		}
		
		// Get max records to process from command line arguments if provided
		const maxRecords = args.length >= 2 ? parseInt(args[1], 10) : Infinity;
		
		do {
			processedCount = await syncAnswerEmbeddings(offset, limit);
			totalProcessed += processedCount;
			offset += limit;
			
			// Break if we've processed the maximum number of records
			if (totalProcessed >= maxRecords) {
				console.log(`Reached maximum records to process (${maxRecords})`);
				break;
			}
			
		} while (processedCount > 0);
		
		console.log(`Manually completed embeddings sync at ${new Date().toISOString()}`);
		console.log(`Total records processed: ${totalProcessed}`);
	} catch (error) {
		console.error('Error in manual embeddings sync:', error);
	}
}

embeddingsSchedule();
