import Sequelize from 'sequelize';
import { db } from '../global/database.js';

import globals from './_globals.js';

const partnerApiKeyModel = db.define( 'partner_api_keys',
	globals.modelSuper( {
		key: { field: 'key', type: Sequelize.STRING, allowNull: false, unique: true },
		keySample: { field: 'key_sample', type: Sequelize.STRING(4), allowNull: false },
		clientId: { field: 'client_id', type: Sequelize.BIGINT, allowNull: false },
		partnerId: { field: 'partner_id', type: Sequelize.BIGINT, allowNull: false }
	} ),
	{ timestamps: true }
);

export default partnerApiKeyModel;
