import { DataTypes } from 'sequelize';
import { db } from '../global/database.js';

const EmailDeduplication = db.define('EmailDeduplication', {
	id: {
		type: DataTypes.INTEGER,
		primaryKey: true,
		autoIncrement: true
	},
	recipient: {
		type: DataTypes.STRING,
		allowNull: false
	},
	subject: {
		type: DataTypes.STRING,
		allowNull: false
	},
	subjectHash: {
		field: 'subject_hash',
		type: DataTypes.STRING,
		allowNull: false,
		index: true
	},
	contentHash: {
		field: 'content_hash',
		type: DataTypes.STRING,
		allowNull: false,
		index: true
	},
	count: {
		type: DataTypes.INTEGER,
		defaultValue: 1
	},
	sentAt: {
		field: 'sent_at',
		type: DataTypes.DATE,
		defaultValue: DataTypes.NOW
	}
}, {
	tableName: 'email_deduplication',
	indexes: [
		{
			unique: true,
			fields: ['recipient', 'subject_hash', 'content_hash']
		}
	]
});

export default EmailDeduplication;
