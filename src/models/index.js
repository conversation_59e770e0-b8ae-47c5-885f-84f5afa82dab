import questionModel from './questionModel.js';
import answerModel from './answerModel.js';
import commentModel from './commentModel.js';
import voteModel from './voteModel.js';
import likeModel from './likeModel.js';
import questionShareModel from './questionShareModel.js';
import statModel from './statModel.js';
import userModel from './userModel.js';
import clientModel from './clientModel.js';
import sessionModel from './sessionModel.js';
import endpointErrorModel from './endpointErrorModel.js';
import partnerModel from './partnerModel.js';
import partnerApiKeyModel from './partnerApiKeyModel.js';

// Inject custom model methods
import modelUtil from '../utils/modelUtil.js';

(async () => {
	const questionError = await import('../errors/questionError.js');
	modelUtil.injectCustomModelMethods(questionModel, questionError.default);

	const answerError = await import('../errors/answerError.js');
	modelUtil.injectCustomModelMethods(answerModel, answerError.default);

	const commentError = await import('../errors/commentError.js');
	modelUtil.injectCustomModelMethods(commentModel, commentError.default);

	const voteError = await import('../errors/voteError.js');
	modelUtil.injectCustomModelMethods(voteModel, voteError.default);

	const likeError = await import('../errors/likeError.js');
	modelUtil.injectCustomModelMethods(likeModel, likeError.default);

	const partnerError = await import('../errors/partnerError.js');
	modelUtil.injectCustomModelMethods(partnerModel, partnerError.default);

	const partnerApiKeyError = await import('../errors/partnerApiKeyError.js');
	modelUtil.injectCustomModelMethods(partnerApiKeyModel, partnerApiKeyError.default);
})();

// Associate Models

clientModel.hasMany( userModel, { as: 'users' } );

userModel.belongsTo( clientModel, { as: 'client' } );
userModel.hasMany( sessionModel, { as: 'sessions' } );
userModel.hasMany( questionModel, { as: 'questions' } );
userModel.hasMany( voteModel, { as: 'votes' } );
userModel.hasMany( commentModel, { as: 'comments' } );

sessionModel.belongsTo( userModel, { as: 'user' } );

questionModel.belongsTo( userModel, { as: 'user' } );
questionModel.belongsTo( clientModel, { as: 'client' } );
questionModel.hasMany( voteModel, { as: 'votes' } );
questionModel.hasMany( answerModel, { as: 'answers' } );
questionModel.hasMany( questionShareModel, { as: 'question_shares' } );

answerModel.belongsTo( userModel, { as: 'user' } );
answerModel.belongsTo( clientModel, { as: 'client' } );
answerModel.belongsTo( questionModel, { as: 'question' } );
answerModel.hasMany( voteModel, { as: 'votes' } );
answerModel.hasMany( likeModel, { as: 'likes' } );
answerModel.hasMany( commentModel, { as: 'comments' } );
answerModel.hasMany( questionShareModel, { as: 'question_shares' } );

commentModel.belongsTo( userModel, { as: 'user' } );
commentModel.belongsTo( answerModel, { as: 'answer' } );

voteModel.belongsTo( userModel, { as: 'user' } );
voteModel.belongsTo( questionModel, { as: 'question' } );
voteModel.belongsTo( answerModel, { as: 'answer' } );

likeModel.belongsTo( userModel, { as: 'user' } );
likeModel.belongsTo( answerModel, { as: 'answer' } );

questionShareModel.belongsTo( userModel, { as: 'user' } );
questionShareModel.belongsTo( questionModel, { as: 'question' } );
questionShareModel.belongsTo( answerModel, { as: 'answer' } );
questionShareModel.belongsTo( clientModel, { as: 'client' } );

statModel.belongsTo( answerModel, { as: 'answer' } );

// Endpoint Error associations
endpointErrorModel.belongsTo( userModel, { as: 'user' } );
userModel.hasMany( endpointErrorModel, { as: 'endpoint_errors' } );

// Partner associations - using clientIds array field, no junction table needed

// Partner API Key associations (one-to-many: partner has many API keys, one per client)
partnerApiKeyModel.belongsTo( clientModel, { as: 'client', foreignKey: 'clientId' } );
clientModel.hasMany( partnerApiKeyModel, { as: 'partnerApiKeys', foreignKey: 'clientId' } );
