import Sequelize from 'sequelize';

import { db } from '../global/database.js';
import globals from './_globals.js';

import aiQueryModel from './aiQueryModel.js';

const statModel = db.define( 'stat_events',
	globals.modelSuper( { 
		userId:      { field: 'user_id',       type: Sequelize.BIGINT, allowNull: true, references: { model: { tableName: 'users' }, key: 'id' } },
		questionId:  { field: 'question_id',   type: Sequelize.BIGINT, allowNull: true, references: { model: { tableName: 'questions' }, key: 'id' } },
		answerId:    { field: 'answer_id',     type: Sequelize.BIGINT, allowNull: true, references: { model: { tableName: 'answers' }, key: 'id' } },
		clientId:    { field: 'client_id',     type: Sequelize.BIGINT, allowNull: true, references: { model: { tableName: 'clients' }, key: 'id' } },

		exportJobId: { field: 'export_job_id', type: Sequelize.STRING, allowNull: true, limit: 64 },
		savedListId: { field: 'saved_list_id', type: Sequelize.STRING, allowNull: true, limit: 64 },

		eventAction:   { field: 'event_action',   type: Sequelize.STRING, allowNull: false, limit: 64 },
		eventCategory: { field: 'event_category', type: Sequelize.STRING, allowNull: false, limit: 128 },
		eventLabel:    { field: 'event_label',    type: Sequelize.STRING, allowNull: true,  limit: 1024 },

		origin: { field: 'origin', type: Sequelize.STRING, allowNull: false, limit: 1024 },
		host:   { field: 'host',   type: Sequelize.STRING, allowNull: false, limit: 128 },
		ipa:    { field: 'ipa',    type: Sequelize.STRING, allowNull: true, limit: 64 },

		aiSearchText: { field: 'ai_search_query', type: Sequelize.STRING, allowNull: true },
		questionText: { field: 'question_text',   type: Sequelize.STRING, allowNull: true, limit: 1024 },
		category:   { field: 'category',    type: Sequelize.STRING, allowNull: true, limit: 1024 },
		searchTerm: { field: 'search_term', type: Sequelize.STRING, allowNull: true, limit: 1024 },
		videoFeedbackAmount: { field: 'video_feedback_amount', type: Sequelize.INTEGER, limit: 2 },

		firstName:   { field: 'first_name',   type: Sequelize.STRING, allowNull: true, limit: 64 },
		lastName:    { field: 'last_name',    type: Sequelize.STRING, allowNull: true, limit: 64 },
		email:       { field: 'email',        type: Sequelize.STRING, allowNull: true, limit: 128 },
		phone:       { field: 'phone',        type: Sequelize.STRING, allowNull: true, limit: 24 },
		zip:         { field: 'zip',          type: Sequelize.STRING, allowNull: true, limit: 24 },
		accessLevel: { field: 'access_level', type: Sequelize.STRING, allowNull: true, limit: 24 },

		referrer: { field: 'referrer', type: Sequelize.STRING, allowNull: true, limit: 1024 },
		mobile: { field: 'mobile', type: Sequelize.BOOLEAN, allowNull: true },
		browser: { field: 'browser', type: Sequelize.STRING, allowNull: true, limit: 1024 },

		exportJobDate: { field: 'export_job_date', type: Sequelize.DATEONLY },

		original: { field: 'original', type: Sequelize.JSON }
	} ),
	{
		schema: 'stats',
		timestamps: true 
	}
);

statModel.addHook('afterCreate', async (statEvent, options) => {
	if (statEvent.eventCategory === 'search_query') {
		try {
			await aiQueryModel.create({
				userId: statEvent.userId,
				questionId: statEvent.questionId,
				answerId: statEvent.answerId,
				clientId: statEvent.clientId,
				query: statEvent.aiSearchText,
				original: JSON.stringify(statEvent),
				category: statEvent.category,
				sentiment: statEvent.sentiment
			});
		} catch (error) {
			console.error('Error creating AI query:', error);
		}
	}
});

export default statModel;
