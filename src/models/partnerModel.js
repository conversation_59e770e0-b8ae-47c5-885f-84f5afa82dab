import Sequelize from 'sequelize';
import { db } from '../global/database.js';

import globals from './_globals.js';

const partnerModel = db.define( 'partners',
	globals.modelSuper( {
		apiEnabled: { field: 'api_enabled', type: Sequelize.BOOLEAN, allowNull: false, defaultValue: false },
		name: { field: 'name', type: Sequelize.STRING, allowNull: false },
		clientIds: { field: 'client_ids', type: Sequelize.ARRAY( Sequelize.BIGINT ), allowNull: true },
		features: { field: 'features', type: Sequelize.ARRAY( Sequelize.STRING ), allowNull: true },
		apiKeyIds: { field: 'api_key_ids', type: Sequelize.ARRAY( Sequelize.BIGINT ), allowNull: true },
		enabled: { field: 'enabled', type: Sequelize.BOOLEAN, allowNull: false, defaultValue: true }
	} ),
	{ timestamps: true }
);

export default partnerModel;
