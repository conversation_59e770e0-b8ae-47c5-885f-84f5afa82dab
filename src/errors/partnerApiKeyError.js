function keyEmpty () {
	return {
		status: 400,
		code: 'ERROR_PARTNER_API_KEY_EMPTY',
		message: 'API key is required.'
	};
}

function keyDuplicated ( key ) {
	return {
		status: 409,
		code: 'ERROR_PARTNER_API_KEY_DUPLICATED',
		message: `API key "${key}" already exists.`
	};
}

function notFound ( id ) {
	return {
		status: 404,
		code: 'ERROR_PARTNER_API_KEY_NOT_FOUND',
		message: `Partner API key with id "${id}" was not found.`
	};
}

export default {
	keyEmpty,
	keyDuplicated,
	notFound
};
