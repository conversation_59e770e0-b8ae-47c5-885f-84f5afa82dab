function nameEmpty () {
	return {
		status: 400,
		code: 'ERROR_PARTNER_EMPTY_NAME',
		message: 'Partner name is required.'
	};
}

function clientIdsRequired () {
	return {
		status: 400,
		code: 'ERROR_PARTNER_CLIENT_IDS_REQUIRED',
		message: 'Partner must have at least one client ID.'
	};
}

function notFound ( id ) {
	return {
		status: 404,
		code: 'ERROR_PARTNER_NOT_FOUND',
		message: `Partner with id "${ id }" was not found.`
	};
}

function apiKeyInvalid () {
	return {
		status: 401,
		code: 'ERROR_PARTNER_INVALID_API_KEY',
		message: 'Invalid partner API key.'
	};
}

function apiDisabled () {
	return {
		status: 403,
		code: 'ERROR_PARTNER_API_DISABLED',
		message: 'API access is disabled for this partner.'
	};
}

function clientNotAuthorized ( clientId ) {
	return {
		status: 403,
		code: 'ERROR_PARTNER_CLIENT_NOT_AUTHORIZED',
		message: `Partner is not authorized to access client with id "${ clientId }".`
	};
}

function apiKeyNotBelongsToPartner ( apiKeyId, partnerId ) {
	return {
		status: 403,
		code: 'ERROR_API_KEY_NOT_BELONGS_TO_PARTNER',
		message: `API key with id "${ apiKeyId }" does not belong to partner with id "${ partnerId }".`
	};
}

function apiKeyNotFoundForClient ( clientId, partnerId ) {
	return {
		status: 404,
		code: 'ERROR_API_KEY_NOT_FOUND_FOR_CLIENT',
		message: `No API key found for client with id "${ clientId }" in partner with id "${ partnerId }".`
	};
}

export default {
	nameEmpty,
	clientIdsRequired,
	notFound,
	apiKeyInvalid,
	apiDisabled,
	clientNotAuthorized,
	apiKeyNotBelongsToPartner,
	apiKeyNotFoundForClient
};
