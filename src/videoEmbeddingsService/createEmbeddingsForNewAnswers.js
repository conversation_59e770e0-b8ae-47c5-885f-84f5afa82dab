import checkNewAnswers from "./checkNewAnswers";
import createAnswerEmbeddings from "./createAnswerEmbeddings";

async function createEmbeddingsForNewAnswers(clientId) {
  const newAnswers = await checkNewAnswers(clientId);

  // Assuming there are 600 results, what's a good way to batch them?
  const batchSize = 50;
  const batches = newAnswers.reduce((acc, _, i) => {
    if (i % batchSize === 0) {
      acc.push(newAnswers.slice(i, i + batchSize));
    }
    return acc;
  }, []);

  await Promise.all(batches.map((batch) => Promise.all(batch.map(createAnswerEmbeddings))));
}

export default createEmbeddingsForNewAnswers;
