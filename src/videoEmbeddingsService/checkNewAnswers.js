import logger from "../common/utils/logger";
import { supabase } from "../common/lib";
import axios from "axios";
import constants from "../common/constants";

export default async function checkNewAnswers(clientId) {
  const { data: videoEmbeddingRecords, error } = await supabase
    .from("answer_embeddings")
    .select("*")
    .eq("repd_client_id", clientId);

  if (error) logger.error(JSON.stringify(error));

  logger.debug(`Video embeddings count : ${videoEmbeddingRecords?.length || 0}`);

  const excludeIds = videoEmbeddingRecords && videoEmbeddingRecords.length ? videoEmbeddingRecords.map((record) => record.repd_answer_id) : [];

  const answersResponse = await axios.post(constants.dataServiceUrl, {
    resource: "/answers",
    method: "get",
    payload: {
      clientId,
      excludeIds,
    },
  });

  logger.debug(`Answer response: ${JSON.stringify(answersResponse.data)}`);

  return answersResponse.data;
}
