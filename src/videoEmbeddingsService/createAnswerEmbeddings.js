import { supabase, openai } from "../common/lib";
import logger from "../common/utils/logger";

export default async function createAnswerEmbeddings(answer) {
  const embeddingResponse = await openai.embeddings.create({
    model: "text-embedding-3-small",
    input: answer.question.text,
  });

  const [{ embedding }] = embeddingResponse.data;

  const { error } = await supabase.from("answer_embeddings").insert({
    question: answer.question.text,
    question_embedding: embedding,
    repd_answer_id: answer.id,
    repd_client_id: answer.clientId,
  });

  if (error) {
    logger.error('createAnswerEmbeddings -> Error', error);
    throw error;
  }
}
