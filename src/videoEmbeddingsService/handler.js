import createEmbeddingsForNewAnswers from "./createEmbeddingsForNewAnswers";
import getClients from "./getClients";
import logger from "../common/utils/logger";
import createAnswerEmbeddings from "./createAnswerEmbeddings";

export async function handler(event) {
  logger.info(`Received event: ${event}`);

  const { answer, clientId } = event;

  if (answer) {
    logger.info(`Will create embeddings for answer: ${JSON.stringify(answer)}`);

    return createAnswerEmbeddings(answer);
  }

  if (clientId) {
    logger.info(
      `Will create embeddings for all new answers for clientId: ${clientId}`
    );

    return createEmbeddingsForNewAnswers(Number(clientId));
  }

  const clients = await getClients();

  logger.info(
    `Will create embeddings for all clients: ${JSON.stringify(clients)}`
  );

  return Promise.all(
    clients.map((client) => createEmbeddingsForNewAnswers(Number(client.id)))
  );
}
