import env from '../global/environment.js';
import { isDevelopment, isStaging, message, setMailDefaults, replyToAddress, fromAddress, send } from './_global.js';

import userModel from '../models/userModel.js';

const environment = process.env.NODE_ENV ? process.env.NODE_ENV : 'development';
const baseURL = environment !== 'production' ? 'https://api-staging.repd.us' : 'https://api.repd.us';

async function getClientUserEmails(clientId) {
	const users = await userModel.findAll({
		where: { client_id: clientId, access_level: 'admin', receiveQuestionNotifications: true }
	});
	const userEmails = users.map(x => x.email).concat(['<EMAIL>', '<EMAIL>']);
	return userEmails;
}

async function sendAnsweredQuestionEmail(answer) {
	setMailDefaults();
	message.templateId = env.getProperty('mail.template.question_answer.template_id');
	const client = answer.client || answer.question.client || answer.question.user.client || {};

	let CTA;
	if (client.clientType === 'Internal') {
		CTA = `${client.websiteURL}?answerId=${answer.id}`;
	} else {
		CTA = env.getProperty('mail.template.question_answer.url')
			.replace(/\:client/, client.name.trim().replace(/[^a-z0-9]+/ig, '-'))
			.replace(/\:answerId/, `?answerId=${answer.id}`);
	}

	var title = `${client.name} answered your question!`;

	message.replyTo = replyToAddress(client);
	message.from = fromAddress(client);

	let donationButtonText = client.clientType === 'Campaign' ? 'Donate to the campaign' : null;
	if (client.donateCtaText !== null && client.donateCtaText !== '')
		donationButtonText = client.donateCtaText;
	if (client.emailDonateCtaText !== null && client.emailDonateCtaText !== '')
		donationButtonText = client.emailDonateCtaText;

	const thumbnailUrl = `${baseURL}/thumbnail/${answer.id}/prerendered.png`;

	message.dynamic_template_data = {
		isDevelopment,
		isStaging,
		isCandidate: client.clientType === 'Campaign',

		subject: title,
		title,
		body: `${answer.question.text}`,

		logoURL: client.logoURL,
		imageURL: thumbnailUrl,
		themeColor: client.topBarColour,
		donationURL: client.donationURL || client.websiteURL,
		donationText: donationButtonText,
		ctaText: 'See the Answer!',
		CTA: CTA
	};

	message.subject = `${client.name} answered your question!`;

	const clientUserEmails = await getClientUserEmails(client.id);
	const sendList = answer.question.votes.map(v => v.user.email).concat([answer.question.user.email, ...clientUserEmails]);

	for (let e of sendList) {
		message.to = [e];
		message.dynamic_template_data.unsubscribeURL = `https://repd.us/unsubscribe?email=${e}`;
		send();
	}
}

async function sendAnsweredQuestionShareEmail(answer) {
	setMailDefaults();
	message.templateId = env.getProperty('mail.template.new_question.template_id');
	const client = answer.client || answer.question.client || answer.question.user.client || {};
	const name = answer.user.lastName === null ? answer.user.email.split('@')[0] : `${answer.user.firstName || ''} ${answer.user.lastName || ''}`;

	let CTA;
	if (client.clientType === 'Internal') {
		CTA = `${client.websiteURL}?answerId=${answer.id}`;
	} else {
		CTA = env.getProperty('mail.template.new_question.url')
			.replace(/\:client/, client.name.trim().replace(/[^a-z0-9]+/ig, '-'))
			.replace(/\:answerId/, `?answerId=${answer.id}`);
	}

	var title = `${name} answered your shared question!`;

	message.replyTo = replyToAddress(client);
	message.from = fromAddress(client);

	const thumbnailUrl = `${baseURL}/thumbnail/${answer.id}/prerendered.png`;

	message.dynamic_template_data = {
		isDevelopment: isDevelopment,
		isStaging: isStaging,
		isCandidate: client.clientType === 'Campaign',

		subject: title,
		title: title,
		body: `${answer.question.text}`,

		logoURL: client.logoURL,
		imageURL: thumbnailUrl,
		ctaText: 'See the latest Answers',
		themeColor: client.topBarColour,
		CTA: CTA
	};
	message.subject = message.dynamic_template_data.subject;

	const clientUserEmails = await getClientUserEmails(client.id);

	const emailList = clientUserEmails.concat([client.email]);
	message.to = emailList;

	// Add unsubscribe URL for all recipients
	if (emailList.length === 1) {
		message.dynamic_template_data.unsubscribeURL = `https://repd.us/unsubscribe?email=${emailList[0]}`;
	} else {
		// For multiple recipients, we need to use sendgrid's substitution
		// This will be replaced by SendGrid for each recipient
		message.dynamic_template_data.unsubscribeURL = 'https://repd.us/unsubscribe?email=%recipient%';
	}

	send();
}

async function sendApprovedAnswerEmail(answer) {
	setMailDefaults({});
	message.templateId = env.getProperty('mail.template.question_answer.template_id');
	const client = answer.client || answer.question.client || answer.question.user.client || {};

	const CTA = env.getProperty('mail.template.question_answer.url')
		.replace(/\:client/, client.name.trim().replace(/[^a-z0-9]+/ig, '-'))
		.replace(/\:answerId/, `?answerId=${answer.id}`);

	var title = `${client.name} approved your answer!`;

	const clientUserEmails = await getClientUserEmails(client.id);

	message.replyTo = replyToAddress(client);
	message.from = fromAddress(client);

	const thumbnailUrl = `${baseURL}/thumbnail/${answer.id}/prerendered.png`;

	message.dynamic_template_data = {
		isDevelopment: isDevelopment,
		isStaging: isStaging,
		isCandidate: client.clientType === 'Campaign',

		subject: title,
		title: title,
		body: `${answer.question.text}`,

		logoURL: client.logoURL,
		imageURL: thumbnailUrl,
		ctaText: 'See the Answer!',
		CTA: CTA
	};

	message.subject = `${client.name} approved your question!`;

	const emailList = clientUserEmails.concat([answer.user.email]);
	message.to = emailList;

	// Add unsubscribe URL for all recipients
	if (emailList.length === 1) {
		message.dynamic_template_data.unsubscribeURL = `https://repd.us/unsubscribe?email=${emailList[0]}`;
	} else {
		// For multiple recipients, we need to use sendgrid's substitution
		// This will be replaced by SendGrid for each recipient
		message.dynamic_template_data.unsubscribeURL = 'https://repd.us/unsubscribe?email=%recipient%';
	}

	send();
}

function sendDeniedAnswerEmail(answer) {
	setMailDefaults({});
	message.templateId = env.getProperty('mail.template.new_question.template_id');
	const client = answer.client || answer.question.client || answer.question.user.client || {};

	const CTA = env.getProperty('mail.template.new_question.url')
		.replace(/\:client/, client.name.trim().replace(/[^a-z0-9]+/ig, '-'))
		.replace(/\:questionId/, `?questionId=${answer.question.id}`);

	var title = `${client.name} denied your answer!`;

	message.replyTo = replyToAddress(client);
	message.from = fromAddress(client);

	const thumbnailUrl = `${baseURL}/thumbnail/${answer.id}/prerendered.png`;

	message.dynamic_template_data = {
		isDevelopment: isDevelopment,
		isStaging: isStaging,
		isCandidate: client.clientType === 'Campaign',
		themeColor: client.topBarColour,

		subject: title,
		title: title,
		body: `${answer.question.text}`,

		logoURL: client.logoURL,
		imageURL: thumbnailUrl,
		ctaText: 'See the question!',
		CTA: CTA
	};

	message.subject = `${client.name} denied your question!`;

	message.to = [answer.user.email];
	message.dynamic_template_data.unsubscribeURL = `https://repd.us/unsubscribe?email=${answer.user.email}`;
	send();
}

export default {
	sendAnsweredQuestionEmail,
	sendAnsweredQuestionShareEmail,
	sendApprovedAnswerEmail,
	sendDeniedAnswerEmail,
};
