// Not used anymore

import logger from "../common/utils/logger";
import parseEvent from "../common/parseEvent";
import downLoadFileFromS3 from "../common/downLoadFileFromS3";
import generateAndUploadThumbnails from "../common/generateAndUploadThumbnails";

export async function handler(event, context) {
  logger.debug("Request received:", JSON.stringify(event));
  const { sourceBucket, sourceKey, targetBucket } = parseEvent(event);

  if (!sourceKey) {
    logger.error("No sourceKey present in body");

    return {
      statusCode: 400,
      body: JSON.stringify("No sourceKey present in body"),
    };
  }

  try {
    const inputData = await downLoadFileFromS3({ sourceBucket, sourceKey });
    generateAndUploadThumbnails({ inputData, sourceKey, targetBucket });

    return {
      statusCode: 200,
      body: JSON.stringify(
        `Successfully generated and uploaded thumbnails for ${sourceKey}`
      ),
    };
  } catch (error) {
    logger.error("Error generating thumbnails: ", error);
    return {
      statusCode: 500,
      body: JSON.stringify(`Error generating thumbnails: ${error}`),
    };
  }
}
