import config from "../common/config";
import replyToAddress from "./helpers/replyToAddress";
import fromAddress from "./helpers/fromAddress";
import logger from "../common/utils/logger";
import sendSingleEmail from "./helpers/sendSingleEmail";
import templateNames from "./helpers/templateNames";

const emptyThumbnailUrl =
  "https://repd-api-files.s3.amazonaws.com/others/empty.jpeg";
async function sendAnsweredQuestionEmail(answer) {
  // Early return if this is a shared question
  if (answer.question && answer.question.isShared) {
    logger.info("Will NOT send regular answer email for shared question");
    return;
  }
  
  if (answer.imageUrl === emptyThumbnailUrl) {
    logger.info("Will NOT send email with default thumbnail");
    return;
  }
  
  const isDevelopment = process.env.NODE_ENV !== "prod";
  const isStaging = process.env.NODE_ENV !== "prod";
  const message = {
    to: null,
    bcc: null,
    ccc: null,
    from: "<EMAIL>",
    html: "<strong></strong>",
  };
  message.templateId = config.mail.template.question_answer.template_id;
  const client =
    answer.client ||
    answer.question.client ||
    answer.question.user.client ||
    {};

  logger.debug("Client: ", JSON.stringify(client));

  const CTA = config.mail.template.question_answer.url
    .replace(/\:client/, client.name.trim().replace(/[^a-z0-9]+/gi, "-"))
    .replace(/\:answerId/, `?answerId=${answer.id}`);

  const title = `${client.name} answered your question!`;

  message.replyTo = replyToAddress(client);
  message.from = fromAddress(client);

  let donationButtonText = "Donate to the campaign";
  if (client.emailDonateCtaText !== null && client.emailDonateCtaText !== "")
    donationButtonText = client.emailDonateCtaText;

  message.dynamic_template_data = {
    isDevelopment,
    isStaging,
    isCandidate: client.clientType === "Campaign",

    subject: title,
    title,
    body: `${answer.question.text}`,

    logoURL: client.logoURL,
    imageURL: answer.imageUrl,
    donationURL: client.donationURL,
    donationText: donationButtonText,
    ctaText: "See the Answer!",
    CTA,
  };

  message.subject = `${client.name} answered your question!`;

  const sendList = answer.question.votes
    .map((v) => v.user.email)
    .concat([answer.question.user.email, "<EMAIL>", "<EMAIL>"]);

  logger.debug("Send List: ", JSON.stringify(sendList));

  await Promise.all(
    sendList.map((email) =>
      sendSingleEmail(
        { ...message, to: [email] },
        templateNames.answeredQuestion
      )
    )
  );
}

export default sendAnsweredQuestionEmail;
