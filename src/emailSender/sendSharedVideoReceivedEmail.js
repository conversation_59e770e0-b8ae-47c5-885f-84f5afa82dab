import config from "../common/config";
import logger from "../common/utils/logger";
import defaultFromEmailAddress from "./helpers/defaultFromEmailAddress";
import sendSingleEmail from "./helpers/sendSingleEmail";
import templateNames from "./helpers/templateNames";

function getToShareUserMessage({ questionShare, answer }) {
  const client =
    answer.client ||
    answer.question.client ||
    answer.question.user.client ||
    {};

  logger.debug("Client: ", JSON.stringify(client));

  const title = "Submission Confirmation!";
  const from = `${client.name} <${client.email}>`;

  return {
    to: [questionShare.user.email],
    from,
    replyTo: from,
    subject: title,
    templateId: config.mail.template.video_request_received.template_id,
    dynamic_template_data: {
      subject: title,
      imageURL: answer.imageUrl,
      title,
      body: `<p>Your video has begun uploading. We'll notify you with another email when the upload is complete and the video has been posted.</p><p style="margin-top: 5px;"><em>${answer.question.text}</em></p>`,
      logoURL: client.logoURL,
      ctaText: "See the Answer!",
      CTA: "https://admin.repd.us/answers-list",
    },
  };
}

function getToClientMessage({ questionShare, answer }) {
  const client =
    answer.client ||
    answer.question.client ||
    answer.question.user.client ||
    {};

  logger.debug("Client: ", JSON.stringify(client));

  const title = "Submission Confirmation!";
  const from = `Rep'd <${defaultFromEmailAddress}>`;

  return {
    to: [client.email],
    from,
    replyTo: from,
    subject: title,
    templateId: config.mail.template.question_answer.template_id,
    dynamic_template_data: {
      subject: title,
      imageURL: answer.imageUrl,
      title,
      body: `<p>
          A video for your shared question to ${questionShare.user.email} has been uploaded and is now in drafts for review. 
        </p>
        <p style="margin-top: 5px;"><em>${answer.question.text}</em></p>
      `,
      ctaText: "See the Answer!",
      CTA: "https://admin.repd.us/answers-list",
      logoURL: config.mail.repdLogo,
    },
  };
}
async function sendSharedVideoReceivedEmail({ questionShare, answer }) {
  await sendSingleEmail(
    getToShareUserMessage({ questionShare, answer }),
    templateNames.videoReceived
  );
  await sendSingleEmail(
    getToClientMessage({ questionShare, answer }),
    templateNames.videoReceived
  );
}

export default sendSharedVideoReceivedEmail;
