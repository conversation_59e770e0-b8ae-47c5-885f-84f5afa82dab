import config from "../common/config";
import logger from "../common/utils/logger";
import sendSingleEmail from "./helpers/sendSingleEmail";
import templateNames from "./helpers/templateNames";
import defaultFromEmailAddress from "./helpers/defaultFromEmailAddress";

function getToShareUserMessage({ questionShare, answer }) {
  const client =
    answer.client ||
    answer.question.client ||
    answer.question.user.client ||
    {};

  logger.debug("Client: ", JSON.stringify(client));

  const CTA = config.mail.template.question_answer.url
    .replace(/\:client/, client.name.trim().replace(/[^a-z0-9]+/gi, "-"))
    .replace(/\:answerId/, `?answerId=${answer.id}`);

  const title = `Upload Confirmation!`;
  const from = `${client.name} <${client.email}>`;

  return {
    to: [questionShare.user.email],
    replyTo: from,
    from,
    subject: title,
    templateId: config.mail.template.video_processing_done.template_id,
    dynamic_template_data: {
      isCandidate: client.clientType === "Campaign",
      subject: title,
      title,
      body: `<p>Your video has been uploaded and posted successfully!</p><p style="margin-top: 5px;"><em>${answer.question.text}</em></p>`,
      logoURL: client.logoURL,
      imageURL: answer.imageUrl,
      ctaText: "See the Answer!",
      CTA,
    },
  };
}

function getToClientMessage({ questionShare, answer }) {
  const client =
    answer.client ||
    answer.question.client ||
    answer.question.user.client ||
    {};

  // logger.debug("Client: ", JSON.stringify(client));

  const CTA = config.mail.template.question_answer.url
    .replace(/\:client/, client.name.trim().replace(/[^a-z0-9]+/gi, "-"))
    .replace(/\:answerId/, `?answerId=${answer.id}`);

  const title = `Video for Your Shared Question is Now in Drafts for Review!`;
  const from = `Rep'd <${defaultFromEmailAddress}>`;

  return {
    to: [client.email],
    replyTo: from,
    from,
    subject: title,
    templateId: config.mail.template.video_processing_done.template_id,
    dynamic_template_data: {
      isCandidate: client.clientType === "Campaign",
      subject: title,
      title,
      body: `<p>
          A video for your shared question to ${questionShare.user.email} has been processed and is now in drafts for your review. 
          You can review the video and select a thumbnail before it is published.
        </p>
        <p style="margin-top: 5px;"><em>${answer.question.text}</em></p>
      `,
      logoURL: config.mail.repdLogo,
      imageURL: answer.imageUrl,
      ctaText: "See the Answer!",
      CTA,
    },
  };
}
async function sendSharedVideoProcessingDoneEmail({ questionShare, answer }) {
  await sendSingleEmail(
    getToClientMessage({ answer, questionShare }),
    templateNames.answeredQuestion
  );

  if (answer.imageUrl === emptyThumbnailUrl) {
    logger.info("Will NOT send email with default thumbnail");
    return;
  }
  if (answer.question.isShared) {
    logger.info("Will NOT send regular answer email for shared question");
    return;
  }
  if (!answer.isApproved) {
    logger.info("Will NOT send email for unapproved answer");
    return;
  }

  await sendSingleEmail(
    getToShareUserMessage({ answer, questionShare }),
    templateNames.answeredQuestion
  );
}

export default sendSharedVideoProcessingDoneEmail;
