import config from "../common/config";
import logger from "../common/utils/logger";
import sendSingleEmail from "./helpers/sendSingleEmail";
import templateNames from "./helpers/templateNames";
import defaultFromEmailAddress from "./helpers/defaultFromEmailAddress";

async function sendVideoProcessingDoneEmail(answer) {
  const client =
    answer.client ||
    answer.question.client ||
    answer.question.user.client ||
    {};

  logger.debug("Client: ", JSON.stringify(client));

  const from = `Rep'd <${defaultFromEmailAddress}>`;

  const CTA = config.mail.template.question_answer.url
    .replace(/\:client/, client.name.trim().replace(/[^a-z0-9]+/gi, "-"))
    .replace(/\:answerId/, `?answerId=${answer.id}`);

  const title = `Upload Confirmation!`;

  await sendSingleEmail(
    {
      to: [client.email],
      replyTo: from,
      from,
      subject: title,
      templateId: config.mail.template.video_processing_done.template_id,
      dynamic_template_data: {
        isCandidate: client.clientType === "Campaign",
        subject: title,
        title,
        body: `<p>Your video has been uploaded and posted successfully!</p><p style="margin-top: 5px;"><em>${answer.question.text}</em></p>`,
        logoURL: config.mail.repdLogo,
        imageURL: answer.imageUrl,
        ctaText: "See the Answer!",
        CTA,
      },
    },
    templateNames.answeredQuestion
  );
}

export default sendVideoProcessingDoneEmail;
