import logger from "../../common/utils/logger";
import SendGridMail from "@sendgrid/mail";
import env from "../../common/env";
import crypto from 'crypto';
import { Op } from 'sequelize';
import EmailDeduplication from "../../common/data/emailDeduplicationModel";

SendGridMail.setApiKey(env.mail.sendGridKey);

async function isDuplicateEmail(recipient, subject, timeWindowHours = 24) {
  const subjectHash = crypto.createHash('md5').update(subject.toLowerCase().trim()).digest('hex');
  const cutoffTime = new Date(Date.now() - (timeWindowHours * 60 * 60 * 1000));
  
  const existing = await EmailDeduplication.findOne({
    where: {
      recipient: recipient.toLowerCase(),
      subjectHash,
      sentAt: {
        [Op.gte]: cutoffTime
      }
    }
  });
  
  if (existing) {
    // Increment the count for this duplicate attempt
    await existing.increment('count');
    return true;
  }
  
  return false;
}

async function recordSentEmail(recipient, subject) {
  const subjectHash = crypto.createHash('md5').update(subject.toLowerCase().trim()).digest('hex');
  
  await EmailDeduplication.create({
    recipient: recipient.toLowerCase(),
    subject,
    subjectHash,
    count: 1,
    sentAt: new Date()
  });
}

async function sendSingleEmail(message, template) {
  const to = message.to[0];

  console.log('SendGridMail', message);

  // Check for duplicates if subject exists
  if (message.subject) {
    if (await isDuplicateEmail(to, message.subject)) {
      logger.info(`Duplicate ${template} email prevented for: ${to}`);
      return { isDuplicate: true };
    }
  }

  logger.info(`Sending ${template} email to: ${to}`);

  try {
    await SendGridMail.send(message, true);
    logger.info(`Successfully sent ${template} email to: ${to}`);
    
    // Record the sent email
    if (message.subject) {
      await recordSentEmail(to, message.subject);
    }
    
  } catch (error) {
    logger.error(`Sending ${template} email`, JSON.stringify(error));
  }
}

export default sendSingleEmail;
