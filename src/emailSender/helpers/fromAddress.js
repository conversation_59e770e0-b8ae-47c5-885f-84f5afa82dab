import replyToAddress from "./replyToAddress";
import defaultFromEmailAddress from "./defaultFromEmailAddress";

export default function fromAddress(client = {}) {
  const tulareAddress = "<EMAIL>";
  let address = replyToAddress(client)
    .toString()
    .replace(/<[^>]+>/, `<${defaultFromEmailAddress}>`);

  if (`${client.name}`.match(/tulare/i) !== null)
    address = address.replace(/<[^>]+>/, `<${tulareAddress}>`);

  return address;
}
