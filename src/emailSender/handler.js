import sendAnsweredQuestionEmail from "./sendAnsweredQuestionEmail";
import logger from "../common/utils/logger";
import templateNames from "./helpers/templateNames";
import sendVideoReceivedEmail from "./sendVideoReceivedEmail";
import sendVideoProcessingDoneEmail from "./sendVideoProcessingDoneEmail";
import sendSharedVideoReceivedEmail from "./sendSharedVideoReceivedEmail";
import sendSharedVideoProcessingDoneEmail from "./sendSharedVideoProcessingDoneEmail";

export async function handler(event) {
  logger.debug("Received event: ", JSON.stringify(event));
  const { templateName, payload } = event;

  if (templateName === templateNames.answeredQuestion) {
    logger.info(`Executing ${templateName}`);
    await sendVideoProcessingDoneEmail(payload);
    
    // Only send the answered question email if it's not a shared question
    if (!payload.question.isShared) {
      await sendAnsweredQuestionEmail(payload);
    }
    return;
  }

  if (templateName === templateNames.answeredSharedQuestion) {
    logger.info(`Executing ${templateName}`);
    await sendSharedVideoProcessingDoneEmail(payload);
    return;
  }

  if (templateName === templateNames.videoReceived) {
    logger.info(`Executing ${templateName}`);
    return sendVideoReceivedEmail(payload);
  }

  if (templateName === templateNames.sharedVideoReceived) {
    logger.info(`Executing ${templateName}`);
    return sendSharedVideoReceivedEmail(payload);
  }

  logger.error(`Unknown template ${templateName}`);
}
