import express from 'express';

import constantUtil from '../utils/constantUtil.js';
import service from '../services/clientService.js';
import { conditionalCelebrate } from '../helpers/validationHelper.js';

const router = express.Router();

import { v4 as uuidv4 } from 'uuid'; // Updated import statement

router.get( '/api/v1.0.0/clients/list', ( request, response, next ) => {
	return(
		service.list( request.auth )
			.then( dto => {
				response.status( 200 ).json( {
					'message': 'Client Sessions Retrieved',
					'totalEntries': dto.length,
					'data': dto
				} );
			} )
			.catch( next )
	);
} );

router.get( '/api/v1.0.0/clients/', ( request, response, next ) => {
	return(
		service.getAll()
			.then( dto => {
				response.status( 200 ).json( {
					'message': 'All Clients Retrieved',
					'totalEntries': dto.length,
					'data': dto
				} );
			} )
			.catch( next )
	);
} );

router.get( '/api/v1.0.0/clients/:name', ( request, response, next ) => {
	const query = `
		SELECT * FROM clients WHERE enabled = true AND (
		  lower( name ) = ? OR
			lower( sub_domain_admin ) = ? OR
			lower( custom_page_path ) = ? OR
			lower( replace( sub_domain_admin, ' ', '' ) ) ILIKE ? OR
			lower( replace( name, ' ', '' ) ) ILIKE ? OR
			lower( replace( custom_page_path, ' ', '' ) ) ILIKE ?
		) ORDER BY updated_at DESC LIMIT 1`;
	const nameReplacement = camelCaseClientName( request.params.name ).toLowerCase();
	const hyphenatedNameReplacement = `%${ request.params.name.toString().replace( /\-/g, '' ).toLowerCase() }%`;
	const replacements = [
		nameReplacement,
		nameReplacement,
		nameReplacement,
		hyphenatedNameReplacement,
		hyphenatedNameReplacement,
		hyphenatedNameReplacement
	];

	return(
		service.search( query, replacements, request.auth )
			.then( dto => {
				response.status( 200 ).json( {
					'message': 'Client Retrieved',
					'totalEntries': 1,
					'data': [ dto ]
				} );
			} )
			.catch( next )
	);
} );

router.post( '/api/v1.0.0/clients/', conditionalCelebrate(), ( request, response, next ) => {
	const user = {
		...request.body.user,
		verificationToken: uuidv4(),
		accessLevel: 'admin',
		enabled: true
	};

	const client = {
		...request.body.client,
		email: request.body.user.email,
		enabled: true
	};

	client.name = camelCaseClientName( client.name );

	if (client.clientType === 'Government')
		client.categories = constantUtil.defaultGovernmentClientCategories.join(',');
	else
		client.categories = constantUtil.defaultClientCategories.join(',');

	return(
		service.create( client, user, request.auth )
			.then( dto => {
				response.status( 201 ).json( {
					'message': 'Admin & Client Created',
					'totalEntries': 1,
					'data': [ dto ]
				} );
			} )
			.catch( next )
	);
} );

router.put( '/api/v1.0.0/clients/', conditionalCelebrate(), ( request, response, next ) => {
	const criteria = {
		...request.body,
		// name: camelCaseClientName( request.body.name ),
		linkURL: `https://app.repd.us/${ hyphenCaseClientName( request.body.name ) }`,
		...( request.body.email && { email: request.body.email.toLowerCase() } )
	};

	return(
		service.update( criteria, request.auth )
			.then( dto => {
				response.status( 201 ).json( {
					'message': 'Client Updated',
					'totalEntries': 1,
					'data': [ dto ]
				} );
			} )
			.catch( next )
	);
} );

function hyphenCaseClientName( r ) {
	if ( !r ) r = '';
	return r.toLowerCase().replace( /[^a-z0-9]+/, '-' ).trim().replace( /(^-|-$)/, '' );
}

function camelCaseClientName( r ) {
	if ( !r ) r = '';
	return r.toLowerCase().replace( /[^a-z0-9]+/, ' ' ).trim().replace( /(^[a-z])|( [a-z])/g, function ( x ) { return x.toUpperCase(); } );
}

export default {
	router: router,
	securityConstraints: [ {
		regex: '/api/v.*/clients/',
		methods: [ 'GET' ],
		accessLevels: []
	}, {
		regex: '/api/v.*/clients/',
		methods: [ 'POST' ],
		accessLevels: [ 'super admin' ]
	}, {
		regex: '/api/v.*/clients/',
		methods: [ 'PUT' ],
		accessLevels: [ 'manager', 'admin', 'super admin' ]
	} ]
};
