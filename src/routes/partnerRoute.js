import express from 'express';

import service from '../services/partnerService.js';
import { conditionalCelebrate } from '../helpers/validationHelper.js';

const router = express.Router();

// GET /api/v1.0.0/partners - List all partners
router.get('/api/v1.0.0/partners', conditionalCelebrate(), (request, response, next) => {
	return(
		service.search(request.query, request.auth)
			.then(([totalEntries, data]) => {
				response.status(200).json({
					'message': 'Partners retrieved successfully',
					'totalEntries': totalEntries,
					'data': data
				});
			})
			.catch(next)
	);
});

// GET /api/v1.0.0/partners/:id - Get partner by ID
router.get('/api/v1.0.0/partners/:id', conditionalCelebrate(), (request, response, next) => {
	return(
		service.getById(request.params.id, request.auth)
			.then(data => {
				response.status(200).json({
					'message': 'Partner retrieved successfully',
					'totalEntries': 1,
					'data': [data]
				});
			})
			.catch(next)
	);
});

// POST /api/v1.0.0/partners - Create new partner
router.post('/api/v1.0.0/partners', conditionalCelebrate(), (request, response, next) => {
	return(
		service.create(request.body, request.auth)
			.then(data => {
				response.status(201).json({
					'message': 'Partner created successfully',
					'totalEntries': 1,
					'data': [data]
				});
			})
			.catch(next)
	);
});

// PUT /api/v1.0.0/partners/:id - Update partner
router.put('/api/v1.0.0/partners/:id', conditionalCelebrate(), (request, response, next) => {
	return(
		service.update(request.params.id, request.body, request.auth)
			.then(data => {
				response.status(200).json({
					'message': 'Partner updated successfully',
					'totalEntries': 1,
					'data': [data]
				});
			})
			.catch(next)
	);
});

// DELETE /api/v1.0.0/partners/:id - Delete (disable) partner
router.delete('/api/v1.0.0/partners/:id', conditionalCelebrate(), (request, response, next) => {
	return(
		service.disable(request.params.id, request.auth)
			.then(data => {
				response.status(200).json({
					'message': 'Partner deleted successfully',
					'totalEntries': 1,
					'data': [data]
				});
			})
			.catch(next)
	);
});

// POST /api/v1.0.0/partners/:partnerId/clients/:clientId/api-key/revoke - Revoke API key
router.post('/api/v1.0.0/partners/:partnerId/clients/:clientId/api-key/revoke', conditionalCelebrate(), (request, response, next) => {
	return(
		service.revokeApiKey(request.params.partnerId, request.params.clientId, request.auth)
			.then(data => {
				response.status(200).json({
					'message': data.message,
					'totalEntries': 1,
					'data': [data]
				});
			})
			.catch(next)
	);
});

// POST /api/v1.0.0/partners/:partnerId/clients/:clientId/api-key/regenerate - Regenerate API key
router.post('/api/v1.0.0/partners/:partnerId/clients/:clientId/api-key/regenerate', conditionalCelebrate(), (request, response, next) => {
	return(
		service.regenerateApiKey(request.params.partnerId, request.params.clientId, request.auth)
			.then(data => {
				response.status(200).json({
					'message': data.message,
					'totalEntries': 1,
					'data': [data]
				});
			})
			.catch(next)
	);
});

export default {
	router: router,
	securityConstraints: [{
		regex: '/api/v.*/partners',
		methods: ['GET', 'POST', 'PUT', 'DELETE'],
		accessLevels: ['super admin']
	}]
};
