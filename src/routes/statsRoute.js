// import Sequelize from 'sequelize';
import express from 'express';
import timeout from 'connect-timeout';
import moment from 'moment';

// import { db } from '../global/database.js';
import statEventModel from '../models/statEventModel.js';
import { conditionalCelebrate } from '../helpers/validationHelper.js';

import {
	getMailStats, getAnalytics, getQuestionsStats, getTrendingStats,
	getEmailsStats, getBulkSendsStats, getEmailsChartStats, getLocationsStats,
	getVotesStats, getCategoriesStats, getPageViewsStats, getVideoViewsStats,
	getMobileDesktopStats, getReferralPercentageStats, getEngagementsChartStats,
	getVideoInfo,
	getSentiments,
	getVideoFeedbackStats,
	getCompletionRateStats,
	getAiQueriesChart,
	getAiQueriesSentiments,
	getVideoPlayStats,
	getNationalCompletionRate
} from '../services/statsService.js';

const router = express.Router();

// SendGrid

router.get('/api/v1.0.0/mailStats', async (req, res, next) => {
	try {
		const clientEmails = await getMailStats(req.auth.user.id);
		res.status(200).json({ mailstats: clientEmails });
	} catch (error) {
		console.log(error);
		res.status(400).json({ error: error.message });
	}
});

// Routes

router.get('/api/v1.0.0/stats/analytics', timeout((60 * 5) + 's'), async function (request, response, next) {
	const clientId = request.query.clientId ? JSON.parse(request.query.clientId) : null;
	const location = request.query.location ? JSON.parse(request.query.location) : null;
	const startDate = request.query.start !== 'null' ? request.query.start : moment.unix(0).toISOString();
	const endDate = request.query.end !== 'null' ? request.query.end : moment().toISOString();

	console.log('analytics', clientId, location, startDate, endDate);

	try {
		const [analytics, mobileDesktop, referralPercentage, engagementsChart, sentiments, aiQueriesChart, aiQueriesSentiments] = await Promise.all([
			getAnalytics(clientId, location, startDate, endDate),
			getMobileDesktopStats(clientId, location, startDate, endDate),
			getReferralPercentageStats(clientId, location, startDate, endDate),
			getEngagementsChartStats(clientId, location, startDate, endDate),
			getSentiments(clientId, location, startDate, endDate),
			getAiQueriesChart(clientId, location, startDate, endDate),
			getAiQueriesSentiments(clientId, location, startDate, endDate)
		]);

		response.status(200).json({ analytics, mobileDesktop, referralPercentage, engagementsChart, sentiments, aiQueriesChart, aiQueriesSentiments });
	} catch (error) {
		console.log(error);
		response.status(400).json({ error: 'Failed to fetch analytics' });
	}
});

router.get('/api/v1.0.0/stats/questions', timeout((60 * 5) + 's'), async function (request, response, next) {
	const clientId = request.query.clientId ? JSON.parse(request.query.clientId) : null;
	const location = request.query.location ? JSON.parse(request.query.location) : null;
	const startDate = request.query.start !== 'null' ? request.query.start : moment.unix(0).toISOString();
	const endDate = request.query.end !== 'null' ? request.query.end : moment().toISOString();

	try {
		const result = await getQuestionsStats(clientId, location, startDate, endDate);
		response.status(200).json({ data: result });
	} catch (error) {
		console.log(error);
		response.status(400).json({ error: 'Failed to fetch questions stats' });
	}
});

router.get('/api/v1.0.0/stats/trending', timeout((60 * 5) + 's'), async function (request, response, next) {
	const clientId = request.query.clientId ? JSON.parse(request.query.clientId) : null;
	const location = request.query.location ? JSON.parse(request.query.location) : null;
	const startDate = request.query.start !== 'null' ? request.query.start : moment.unix(0).toISOString();
	const endDate = request.query.end !== 'null' ? request.query.end : moment().toISOString();

	try {
		const result = await getTrendingStats(clientId, location, startDate, endDate);
		response.status(200).json({ data: result });
	} catch (error) {
		console.log(error);
		response.status(400).json({ error: 'Failed to fetch trending stats' });
	}
});

router.get('/api/v1.0.0/stats/emails', timeout((60 * 5) + 's'), async function (request, response, next) {
	const clientId = request.query.clientId ? JSON.parse(request.query.clientId) : null;
	const location = request.query.location ? JSON.parse(request.query.location) : null;
	const startDate = request.query.start !== 'null' ? request.query.start : moment.unix(0).toISOString();
	const endDate = request.query.end !== 'null' ? request.query.end : moment().toISOString();

	try {
		const result = await getEmailsStats(clientId, location, startDate, endDate);
		response.status(200).json({ data: result });
	} catch (error) {
		console.log(error);
		response.status(400).json({ error: 'Failed to fetch emails stats' });
	}
});

router.get('/api/v1.0.0/stats/emails/bulk-sends', timeout((60 * 5) + 's'), async function (request, response, next) {
	const clientId = request.query.clientId ? JSON.parse(request.query.clientId) : null;
	const location = request.query.location ? JSON.parse(request.query.location) : null;
	const startDate = request.query.start !== 'null' ? request.query.start : moment.unix(0).toISOString();
	const endDate = request.query.end !== 'null' ? request.query.end : moment().toISOString();

	try {
		const [bulkSends, videoPlayStats] = await Promise.all([
			getBulkSendsStats(clientId, location, startDate, endDate),
			getVideoPlayStats(clientId, startDate, endDate)
		]);
		response.status(200).json({ bulkSends, videoPlayStats });
	} catch (error) {
		console.log(error);
		response.status(400).json({ error: 'Failed to fetch bulk sends stats' });
	}
});

router.get('/api/v1.0.0/stats/emails/chart', timeout((60 * 5) + 's'), async function (request, response, next) {
	const clientId = request.query.clientId ? JSON.parse(request.query.clientId) : null;
	const location = request.query.location ? JSON.parse(request.query.location) : null;
	const startDate = request.query.start !== 'null' ? request.query.start : moment.unix(0).toISOString();
	const endDate = request.query.end !== 'null' ? request.query.end : moment().toISOString();

	try {
		const result = await getEmailsChartStats(clientId, location, startDate, endDate);
		response.status(200).json({ data: result });
	} catch (error) {
		console.log(error);
		response.status(400).json({ error: 'Failed to fetch emails chart stats' });
	}
});

router.get('/api/v1.0.0/stats', timeout((60 * 5) + 's'), async function (request, response, next) {
	const clientId = request.auth.user.client.id;

	var statsResult = {
		locations: [],
		questions: [],
		votes: [],
		categoriesWithVotes: [],
		pageViews: [],
		videoViews: [],
		statsResult: {}
	};

	try {
		const [locations, questions, votes, categories, pageViews, videoViews, clientMailData] = await Promise.all([
			getLocationsStats(clientId).then(result => {
				console.log('Locations fetched:', result?.length || 0);
				return result;
			}),
			getQuestionsStats(clientId, [], new Date(0).toISOString(), new Date().toISOString()).then(result => {
				console.log('Questions fetched:', result?.length || 0);
				return result;
			}),
			getVotesStats(clientId).then(result => {
				console.log('Votes fetched:', result?.length || 0);
				return result;
			}),
			getCategoriesStats(clientId).then(result => {
				console.log('Categories fetched:', result?.length || 0);
				return result;
			}),
			getPageViewsStats(clientId).then(result => {
				console.log('PageViews fetched:', result?.length || 0);
				return result;
			}),
			getVideoViewsStats(clientId).then(result => {
				console.log('VideoViews fetched:', result?.length || 0);
				return result;
			}),
			getMailStats(clientId).then(result => {
				console.log('MailStats fetched:', result?.length || 0);
				return result;
			})
		]);

		statsResult.locations = locations;
		statsResult.questions = questions;
		statsResult.votes = votes;
		statsResult.categories = categories;
		statsResult.pageViews = pageViews.reverse();
		statsResult.videoViews = videoViews;
		statsResult.clientMailData = clientMailData.filter(r => r.emails !== null);

		const alignedData = await dataToObject(statsResult);

		response.status(200).json({
			'message': 'Stats retrieved.',
			'totalEntries': 5,
			'data': [alignedData]
		});
	} catch (error) {
		console.error(error);
		response.status(400).json({
			error: 'Failed to fetch stats',
			details: error.message,
			stack: error.stack
		});
	}
});

router.get('/api/v1.0.0/stats/videos', async (request, response, next) => {
	const clientId = request.query.clientId ? JSON.parse(request.query.clientId) : null;
	const location = request.query.location ? JSON.parse(request.query.location) : null;
	const startDate = request.query.start !== 'null' ? request.query.start : moment.unix(0).toISOString();
	const endDate = request.query.end !== 'null' ? request.query.end : moment().toISOString();

	// console.log('videos', clientId, location, startDate, endDate, request.query);

	try {
		const [videos, videoFeedbackStats, completionRateStats, videoPlayStats, nationalCompletionRate] = await Promise.all([
			getVideoInfo(clientId, location, startDate, endDate),
			getVideoFeedbackStats(clientId, location, startDate, endDate),
			getCompletionRateStats(clientId, location, startDate, endDate),
			getVideoPlayStats(clientId, startDate, endDate),
			getNationalCompletionRate(startDate, endDate)
			// feedback
		]);
		response.status(200).json({ videos, videoFeedbackStats, completionRateStats, videoPlayStats, nationalCompletionRate });
	} catch (error) {
		console.log(error);
		response.status(400).json({ error: 'Failed to fetch video information' });
	}
});

router.post( '/api/v1.0.0/stats', conditionalCelebrate(), async function ( request, response, next ) {
	return statEventModel.create( request.body ).then( ( results ) => {
		return response.status( 200 ).json( {
			'message': 'Stat event created.',
			'totalEntries': 1,
			'data': [ results ]
		} );
	} ).catch( ( error ) => {
		return response.status( 400 ).json( {
			'message': 'Failed to create stat event.',
			'error': error
		} );
	} );
} );

const dataToObject = async (statsResult) => {
	const day = (new Date()).toISOString().match(/.{10}/)[0];

	var result = {
		categories: statsResult.categories,
		locations: statsResult.locations,
		questions: { amount: 0, days: 0 },
		votes: { amount: 0, days: 0 },
		engagement: statsResult.pageViews,
		videos: statsResult.videoViews,
		emails: statsResult.clientMailData
	};

	if (statsResult.questions[0])
		result.questions.days = getDayDifference(statsResult.questions[0].day, day);

	if (statsResult.votes[0])
		result.votes.days = getDayDifference(statsResult.votes[0].day, day);

	for (let i in statsResult.questions)
		result.questions.amount += parseInt(statsResult.questions[i].amount);

	for (let i in statsResult.votes)
		result.votes.amount += parseInt(statsResult.votes[i].amount);

	return result;
};

function getDayDifference(dateString1, dateString2) {
	const date1 = new Date(dateString1);
	const date2 = new Date(dateString2);
	const timeDiff = Math.abs(date2.getTime() - date1.getTime());
	return Math.ceil(timeDiff / (1000 * 3600 * 24));
}

export default {
	router: router,
	securityConstraints: [ {
		'regex': '/api/v.*/stats',
		'methods': [ 'GET' ],
		'accessLevels': [ 'admin', 'manager', 'super admin' ]
	}, {
		'regex': '/api/v.*/stats',
		'methods': [ 'POST' ],
		'accessLevels': [ 'visitor', 'user', 'admin', 'guest admin', 'manager', 'super admin' ]
	} ]
};
