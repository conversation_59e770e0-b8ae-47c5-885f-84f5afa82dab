import express from 'express';
import { classifyQuestion } from '../helpers/ai.js';
import logger from '../global/log.js';
import clientModel from '../models/clientModel.js';

const router = express.Router();

router.post('/api/v1.0.0/ai-service', async (request, response) => {
	try {
		const { question, api, clientId, isTeleprompter } = request.body;
		const client = await clientModel.findByPk(clientId);

		if (!question) {
			return response.status(400).json(badDataResponse('Question is required'));
		}

		const classification = await classifyQuestion(question, api);
		const marvinURL = `http://127.0.0.1:3001/marvin/answer/${clientId}?user_id=${request.auth?.user?.id}`;

		const marvinAnswers = await fetch(marvinURL, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ client, question, isTeleprompter })
		}).then(res => res.json());

		const formattedResponse = {
			classification,
			marvinAnswers,
			matchingAnswers: marvinAnswers.matchingAnswers,
		};

		return response.status(200).json(successResponse(formattedResponse));
	} catch (error) {
		logger.error('Error:', error);
		return response.status(500).json(internalErrorResponse());
	}
});

router.get('/api/v1.0.0/marvin/question-embeddings/:clientId', async (request, response) => {
	try {
		console.log('question-embeddings');
		console.log(request.params);

		const { clientId } = request.params;
		const marvinURL = `http://127.0.0.1:3001/marvin/question-embeddings/${clientId}`;

		const marvinResponse = await fetch(marvinURL, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json'
			}
		}).then(res => res.json());

		return response.status(200).json(marvinResponse);
	} catch (error) {
		logger.error('Error fetching question embeddings:', error);
		return response.status(500).json(internalErrorResponse());
	}
});

router.get('/api/v1.0.0/marvin/sites/:clientId', async (request, response) => {
	try {
		const { clientId } = request.params;
		const marvinURL = `http://127.0.0.1:3001/marvin/sites/${clientId}`;

		const marvinResponse = await fetch(marvinURL, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json'
			}
		}).then(res => res.json());

		return response.status(200).json(marvinResponse);
	} catch (error) {
		logger.error('Error fetching sites:', error);
		return response.status(500).json(internalErrorResponse());
	}
});

router.get('/api/v1.0.0/marvin/site-links/:clientId', async (request, response) => {
	try {
		const { clientId } = request.params;
		const marvinURL = `http://127.0.0.1:3001/marvin/site-links/${clientId}`;

		const marvinResponse = await fetch(marvinURL, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json'
			}
		}).then(res => res.json());

		return response.status(200).json(marvinResponse);
	} catch (error) {
		logger.error('Error fetching site links:', error);
		return response.status(500).json(internalErrorResponse());
	}
});

router.get('/api/v1.0.0/marvin/sites', async (request, response) => {
	try {
		const marvinURL = new URL('http://127.0.0.1:3001/marvin/sites');

		// Forward pagination query parameters to Marvin API
		if (request.query.page) {
			marvinURL.searchParams.append('page', request.query.page);
		}
		if (request.query.limit) {
			marvinURL.searchParams.append('limit', request.query.limit);
		}

		const marvinResponse = await fetch(marvinURL.toString(), {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json'
			}
		}).then(res => res.json());

		return response.status(200).json(marvinResponse);
	} catch (error) {
		logger.error('Error fetching all sites:', error);
		return response.status(500).json(internalErrorResponse());
	}
});

router.get('/api/v1.0.0/marvin/site-links', async (request, response) => {
	try {
		const marvinURL = new URL('http://127.0.0.1:3001/marvin/site-links');

		// Forward site_id query parameter to Marvin API
		if (request.query.site_id) {
			marvinURL.searchParams.append('site_id', request.query.site_id);
		}

		const marvinResponse = await fetch(marvinURL.toString(), {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json'
			}
		}).then(res => res.json());

		return response.status(200).json(marvinResponse);
	} catch (error) {
		logger.error('Error fetching all site links:', error);
		return response.status(500).json(internalErrorResponse());
	}
});

export const corsHeaders = {
	'Access-Control-Allow-Origin': '*',
	'Access-Control-Allow-Credentials': 'true',
	'Access-Control-Allow-Methods': 'OPTIONS,POST,GET',
	'Access-Control-Allow-Headers':
    'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
};

const headers = { ...corsHeaders, 'Content-Type': 'application/json' };

const successResponse = (data) => {
	return {
		statusCode: 200,
		headers,
		...data,
	};
};

const internalErrorResponse = () => {
	return {
		statusCode: 500,
		headers,
		body: JSON.stringify({ error: 'Internal server error' }),
	};
};

const badDataResponse = (message) => {
	return {
		statusCode: 400,
		headers,
		body: JSON.stringify({ error: message }),
	};
};

export default {
	router: router,
	securityConstraints: [ {
		regex: '/api/v.*/ai-service/',
		methods: [ 'GET' ],
		accessLevels: []
	}, {
		regex: '/api/v.*/question-embeddings/',
		methods: [ 'GET' ],
		accessLevels: []
	}, {
		regex: '/api/v.*/sites/',
		methods: [ 'GET' ],
		accessLevels: []
	}, {
		regex: '/api/v.*/site-links/',
		methods: [ 'GET' ],
		accessLevels: []
	}]
};
