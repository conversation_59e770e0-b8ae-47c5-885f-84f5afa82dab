import express from 'express';
import { Op } from 'sequelize';
import swaggerUi from 'swagger-ui-express';

import answerService from '../services/answerService.js';
import partnerApiKeyService from '../services/partnerApiKeyService.js';
import partnerService from '../services/partnerService.js';
import errorUtil from '../utils/errorUtil.js';

const router = express.Router();

// API Key Authentication Middleware
async function apiKeyAuthMiddleware(request, response, next) {
	try {
		// Extract API key from Authorization header
		const authHeader = request.headers.authorization || request.headers.Authorization;
		
		if (!authHeader || !authHeader.startsWith('Bearer ')) {
			return response.status(401).json({
				code: 'ERROR_API_KEY_MISSING',
				message: 'API key is required. Please provide it in the Authorization header as "Bearer <your-api-key>".'
			});
		}

		const apiKey = authHeader.substring(7); // Remove 'Bearer ' prefix

		// Find the API key record
		const apiKeyRecord = await partnerApiKeyService.findByRawKey(apiKey);
		
		if (!apiKeyRecord) {
			return response.status(401).json({
				code: 'ERROR_API_KEY_INVALID',
				message: 'Invalid API key provided.'
			});
		}

		// Find the partner that owns this API key using the partnerId from the API key record
		const partner = await partnerService.getById(apiKeyRecord.partnerId, null);

		if (!partner) {
			return response.status(401).json({
				code: 'ERROR_PARTNER_NOT_FOUND',
				message: 'Partner associated with this API key not found.'
			});
		}

		// Check if partner has API enabled
		if (!partner.apiEnabled) {
			return response.status(403).json({
				code: 'ERROR_API_DISABLED',
				message: 'API access is disabled for this partner.'
			});
		}

		// Add partner and client info to request
		request.partnerAuth = {
			partner: partner,
			clientId: apiKeyRecord.clientId,
			apiKeyRecord: apiKeyRecord
		};

		next();
	} catch (error) {
		return response.status(500).json({
			code: 'ERROR_API_KEY_VALIDATION',
			message: 'Error validating API key.'
		});
	}
}

// GET /partner-api/v1.0.0/answers - List answers for partner's client
router.get('/partner-api/v1.0.0/answers', apiKeyAuthMiddleware, async (request, response, next) => {
	try {
		const { partner, clientId } = request.partnerAuth;

		// Check if partner has video_library feature
		if (!partner.features || !partner.features.includes('video_library')) {
			return response.status(403).json({
				code: 'ERROR_FEATURE_NOT_ENABLED',
				message: 'The video_library feature is not enabled for this partner.'
			});
		}

		// Extract pagination parameters
		const page = request.query.page ? parseInt(request.query.page) : undefined;
		const limit = request.query.limit ? parseInt(request.query.limit) : (page !== undefined ? 20 : undefined);

		// Build criteria for answer search
		const criteria = {
			pagination: {
				page: page || 0,
				limit: limit || 10000, // Default large limit when not paginated
				order: [['createdAt', 'DESC']]
			},
			and: `client_id:${clientId}`, // Filter by the client associated with the API key
			showDisabled: false,
			// Only show non-draft answers for API consumers
			customPredicates: {
				[Op.or]: [
					{ isDraft: null },
					{ isDraft: false }
				]
			}
		};

		// Search for answers
		const [totalEntries, answers] = await answerService.search(criteria, null, false);

		response.status(200).json({
			message: 'Answers retrieved successfully',
			totalEntries: totalEntries,
			data: answers,
			pagination: page !== undefined ? {
				currentPage: page,
				totalPages: Math.ceil(totalEntries / (limit || 20)),
				hasNextPage: (page + 1) * (limit || 20) < totalEntries,
				hasPreviousPage: page > 0
			} : null
		});
	} catch (error) {
		next(error);
	}
});

// Swagger documentation for Partner API
const partnerApiSwaggerSpec = {
	openapi: '3.0.0',
	info: {
		title: 'Rep\'d Partner API',
		version: '1.0.0',
		description: 'API for partners to access video library content using API key authentication'
	},
	servers: [
		{
			url: 'http://localhost:3000',
			description: 'Development server'
		},
		{
			url: 'https://api.repd.us',
			description: 'Production server'
		}
	],
	components: {
		securitySchemes: {
			ApiKeyAuth: {
				type: 'http',
				scheme: 'bearer',
				bearerFormat: 'API Key',
				description: 'API key provided by partner management'
			}
		},
		schemas: {
			Answer: {
				type: 'object',
				properties: {
					id: { type: 'integer', description: 'Answer ID' },
					clientId: { type: 'integer', description: 'Client ID' },
					imageUrl: { type: 'string', description: 'Thumbnail image URL' },
					videoUrl: { type: 'string', description: 'Video URL' },
					videoUrls: { type: 'object', description: 'Video URLs in different formats' },
					videoDuration: { type: 'number', description: 'Video duration in seconds' },
					subtitles: { type: 'object', description: 'Video subtitles' },
					transcription: { type: 'object', description: 'Video transcription' },
					isDraft: { type: 'boolean', description: 'Whether answer is a draft' },
					isApproved: { type: 'boolean', description: 'Whether answer is approved' },
					isPinned: { type: 'boolean', description: 'Whether answer is pinned' },
					createdAt: { type: 'string', format: 'date-time', description: 'Creation timestamp' },
					updatedAt: { type: 'string', format: 'date-time', description: 'Last update timestamp' },
					question: {
						type: 'object',
						description: 'Associated question data'
					}
				}
			},
			PaginationInfo: {
				type: 'object',
				properties: {
					currentPage: { type: 'integer', description: 'Current page number (0-based)' },
					totalPages: { type: 'integer', description: 'Total number of pages' },
					hasNextPage: { type: 'boolean', description: 'Whether there is a next page' },
					hasPreviousPage: { type: 'boolean', description: 'Whether there is a previous page' }
				}
			},
			AnswersResponse: {
				type: 'object',
				properties: {
					message: { type: 'string', example: 'Answers retrieved successfully' },
					totalEntries: { type: 'integer', description: 'Total number of answers' },
					data: {
						type: 'array',
						items: { $ref: '#/components/schemas/Answer' }
					},
					pagination: {
						oneOf: [
							{ $ref: '#/components/schemas/PaginationInfo' },
							{ type: 'null' }
						],
						description: 'Pagination info (only present when page parameter is used)'
					}
				}
			},
			ErrorResponse: {
				type: 'object',
				properties: {
					code: { type: 'string', description: 'Error code' },
					message: { type: 'string', description: 'Error message' }
				}
			}
		}
	},
	paths: {
		'/partner-api/v1.0.0/answers': {
			get: {
				summary: 'Get answers for partner\'s client',
				description: 'Retrieves answers associated with the partner\'s client. Requires video_library feature.',
				security: [{ ApiKeyAuth: [] }],
				parameters: [
					{
						name: 'page',
						in: 'query',
						description: 'Page number for pagination (0-based)',
						required: false,
						schema: { type: 'integer', minimum: 0 }
					},
					{
						name: 'limit',
						in: 'query',
						description: 'Number of results per page (default: 20 when page is specified)',
						required: false,
						schema: { type: 'integer', minimum: 1, maximum: 100 }
					}
				],
				responses: {
					'200': {
						description: 'Answers retrieved successfully',
						content: {
							'application/json': {
								schema: { $ref: '#/components/schemas/AnswersResponse' }
							}
						}
					},
					'401': {
						description: 'Authentication failed',
						content: {
							'application/json': {
								schema: { $ref: '#/components/schemas/ErrorResponse' },
								examples: {
									missingApiKey: {
										value: {
											code: 'ERROR_API_KEY_MISSING',
											message: 'API key is required. Please provide it in the Authorization header as "Bearer <your-api-key>".'
										}
									},
									invalidApiKey: {
										value: {
											code: 'ERROR_API_KEY_INVALID',
											message: 'Invalid API key provided.'
										}
									},
									partnerNotFound: {
										value: {
											code: 'ERROR_PARTNER_NOT_FOUND',
											message: 'Partner associated with this API key not found.'
										}
									}
								}
							}
						}
					},
					'403': {
						description: 'Access forbidden',
						content: {
							'application/json': {
								schema: { $ref: '#/components/schemas/ErrorResponse' },
								examples: {
									apiDisabled: {
										value: {
											code: 'ERROR_API_DISABLED',
											message: 'API access is disabled for this partner.'
										}
									},
									featureNotEnabled: {
										value: {
											code: 'ERROR_FEATURE_NOT_ENABLED',
											message: 'The video_library feature is not enabled for this partner.'
										}
									}
								}
							}
						}
					},
					'500': {
						description: 'Internal server error',
						content: {
							'application/json': {
								schema: { $ref: '#/components/schemas/ErrorResponse' },
								example: {
									code: 'ERROR_API_KEY_VALIDATION',
									message: 'Error validating API key.'
								}
							}
						}
					}
				}
			}
		}
	}
};

// GET /partner-api/docs - Swagger documentation for Partner API (separate from versioned API path)
router.use('/partner-api/docs', swaggerUi.serve);
router.get('/partner-api/docs', swaggerUi.setup(partnerApiSwaggerSpec, {
	customCss: '.swagger-ui .topbar { display: none }',
	customSiteTitle: 'Partner API Documentation'
}));

export default {
	router: router,
	// No security constraints needed since we handle auth with API key middleware
	securityConstraints: []
};
