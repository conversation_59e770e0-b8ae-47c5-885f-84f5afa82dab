# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Primary Commands:**
- `npm test` - Run Jest tests with Babel transformation
- `npm run build` - Build all Lambda functions for development environment
- `npm run build-prod` - Build all Lambda functions for production environment
- `npm run deploy <environment> [-f|--force]` - Deploy to staging or prod (use -f to force all functions)

**Build Commands:**
- `./build.sh` - Build all services using esbuild with bundling and minification
- `./build.sh <service>` - Build specific service (e.g., `./build.sh aiService`)
- `./deploy.sh <environment>` - Deploy Lambda functions to AWS (staging/prod)

## Architecture Overview

This is a **serverless AWS Lambda microservices** architecture supporting a political campaign Q&A platform:

### Core Technology Stack
- **AWS Lambda** functions with Node.js ES6 modules
- **esbuild** for bundling, minification, and deployment packaging
- **PostgreSQL** database access via Sequelize ORM
- **Jest** testing with Babel transformation for ES6+ features
- **SendGrid** for email functionality
- **AWS Services**: S3, Transcribe, Lambda invocation

### Lambda Functions Architecture

The codebase contains multiple independent Lambda functions:

#### Video Processing Services
- **conversionHandlers/mp4** - Convert videos to MP4 format using FFmpeg
- **conversionHandlers/ogv** - Convert videos to OGV format 
- **conversionHandlers/webm** - Convert videos to WebM format
- **statusUpdater** - Update video processing status and send notifications
- **thumbnailGenerator** - Generate video thumbnails

#### Business Logic Services  
- **aiService** - AI question classification and answer matching with OpenAI/Anthropic
- **emailSender** - SendGrid email delivery with template management
- **dataService** - Database operations and API endpoints
- **videoEmbeddingsService** - Vector embeddings for semantic search

#### Development/Testing
- **local-test** - Local development and testing utilities

### Common Architecture Patterns

#### Shared Common Module (`src/common/`)
- **config.js** - Environment-specific configuration (mail templates, AWS buckets, DB settings)
- **data/** - Sequelize models and database utilities shared across Lambda functions
- **utils/** - Logging, response formatting, and utility functions
- **env.js** - Environment variable management
- **lib.js** - Shared business logic functions

#### Database Models Pattern
- **SuperModel.js** - Base model with common fields (id, createdAt, updatedAt, enabled)
- Domain models extend SuperModel for consistent schema structure
- Models support multi-tenant architecture with clientId scoping

#### Response Pattern
- Standardized response utilities (`responseUtil.js`) for consistent API responses
- CORS headers automatically applied for web client compatibility
- Error handling with proper HTTP status codes

### Build and Deployment Workflow

#### Build Process (esbuild)
1. **Bundle** - All dependencies packaged into single file
2. **Minify** - Code optimization for production
3. **Sourcemap** - Debug information preserved
4. **Platform targeting** - Node.js ES2020 compatibility
5. **ZIP creation** - AWS Lambda deployment packages

#### Deployment Process
- **Git-based deployment** - Only deploys functions with detected changes
- **Environment targeting** - Separate staging and production environments
- **Force deployment** - Option to deploy all functions regardless of changes
- **Common dependency detection** - Redeploys all functions when shared code changes

### Email System Integration

#### SendGrid Templates
- Template-based email system with dynamic content injection
- Environment-specific template IDs configured in `config.js`
- Email types: question notifications, video processing status, user verification

#### Email Handler Patterns
- Template-based routing in `emailSender/handler.js`
- Conditional logic for shared vs. regular questions
- Payload-driven dynamic content generation

### AI/ML Integration

#### Question Classification
- OpenAI and Anthropic SDK integration for content analysis
- Question categorization and sentiment analysis
- Answer matching using vector embeddings via Supabase

#### Polymorphic Client System
- Multi-tenant AI responses with organization-specific contexts
- External city site integration for comprehensive answer coverage

### Development Patterns

#### ES6 Module System
- All code uses `import/export` syntax (not CommonJS)
- Babel transformation for Jest testing compatibility
- Modern JavaScript features (async/await, destructuring, etc.)

#### Error Handling
- Structured error responses with proper HTTP codes
- Comprehensive logging with request tracing
- CORS handling for preflight OPTIONS requests

#### Configuration Management
- Environment-based configuration files
- Secure environment variable handling
- Template and URL management for different environments

### Testing Strategy
- **Jest** with Babel transformation for ES6+ compatibility
- Test files in `src/*/handler.test.js` pattern
- Mock utilities for AWS services and external APIs

### Important Notes
- **Serverless architecture** - Each function is independently deployable
- **Shared common module** - Changes to `src/common/` trigger all function deployments  
- **Multi-tenant support** - All database operations consider client isolation
- **AWS Lambda constraints** - Functions must be stateless and handle cold starts
- **Build artifacts** - `dist/` directory contains deployable ZIP files
- **Environment targeting** - Staging and production have separate function names and configurations