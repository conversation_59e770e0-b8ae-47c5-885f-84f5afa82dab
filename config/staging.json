{
  "name": "staging",
  "port": "80",
  "host": "admin-staging.repd.us",
  "protocol": "https",

  "aws": {
    "access_key_id": "Passed in the ENV",
    "buckets": {
      "file_document": "repd-api-files",
      "file_image": "repd-api-files"
    },
    "secret_access_key": "Passed in the ENV",
    "region": "Passed in the ENV"
  },

  "db": {
    "connection": {
      "database": "postgres",
      "host": "Passed in the ENV",
      "password": "Passed in the ENV",
      "show_sql": false,
      "user": "Passed in the ENV"
    },
    "dialect": "postgres",
    "storage": null
  },

  "files": {
    "documents": { "max_size_in_kb": 102400 },
    "images": { "max_size_in_kb": 1024 }
  },

  "log": { "aws": false, "level": "info" },

  "mail": {
    "send_grid": { "key": "Passed in the ENV" },
    "default": "<EMAIL>",
    "template": {
      "verify_email": {
        "template_id": "d-6dc25f0e446d464a8971fe2b04f9f0be",
        "url": "http://app-staging.repd.us/:client/:userId"
      },
      "question_answer": {
        "template_id": "d-3d8ad7868d2b41c780f850b329d32f00",
        "url": "http://app-staging.repd.us/:client/:answerId"
      },
      "ngp_van_answer": {
        "template_id": "d-39bd041a02724856b77b3b0e3dbd6c50",
        "url": "http://app-staging.repd.us/:client/:answerId"
      },
      "new_question": {
        "template_id": "d-cc3590be6d7e47fcac1692b80f13fe79",
        "url": "http://app-staging.repd.us/:client/:questionId"
      },
      "question_notification": {
        "template_id": "d-cc3590be6d7e47fcac1692b80f13fe79",
        "url": "http://admin-staging.repd.us/questions-list"
      },
      "share_question": {
        "template_id": "d-8df50cf1b5bb4f5682b0f0c92c30efb3",
        "url": "http://admin-staging.repd.us/answer-invite?token=:sessionToken&questionId=:questionId&uti=:userId"
      },
      "new_client": {
        "template_id": "d-4b90d50106b0404c82f16e41aa81b2b0",
        "url": "http://admin-staging.repd.us/new-client?token=:sessionToken"
      },
      "magic_link": {
        "template_id": "d-6dc25f0e446d464a8971fe2b04f9f0be",
        "url": "http://app-staging.repd.us/:client/magic?token=:sessionToken"
      },
      "login_link": {
        "template_id": "d-6dc25f0e446d464a8971fe2b04f9f0be",
        "url": "http://admin-staging.repd.us/login?token=:sessionToken"
      },
      "new_error_email": {
        "template_id": "d-cc5b5dc2c7494dcea71c64c7a9298b60",
        "url": "http://admin.repd.us/error-page"
      },
      "send_errors_email": "<EMAIL>",
    }
  },

  "session": {
    "disable_cron": "0 */30 * * * *",
    "secret": "Passed in the ENV",
    "timeout_in_minutes": 240
  }
}
