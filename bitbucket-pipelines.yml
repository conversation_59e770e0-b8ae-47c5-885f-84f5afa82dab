image: sleavely/node-awscli

definitions:
  steps:
    - step: &install-dependencies
        name: Install Dependencies
        caches:
          - node
        script:
          - npm install
          - sh fix-fluent-ffmpeg.sh
        artifacts:
          - node_modules/**
    - step: &test
        name: Unit Tests
        script:
          - npm test

pipelines:
  default:
    - step: *install-dependencies
    - step: *test

  branches:
    staging:
      - step: *install-dependencies
      - parallel:
        - step: *test
        - step:
            name: Build
            script:
              - npm run build
            artifacts:
              - dist/**
      - step:
          name: Deploy to Staging
          deployment: staging
          script:
            - aws --version
            - export FORCE_UPDATE=${FORCE_UPDATE}
            - export AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
            - export AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
            - export AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}
            - bash deploy.sh staging

    master:
      - step: *install-dependencies
      - parallel:
        - step: *test
        - step:
            name: Build
            script:
              - npm run build-prod
            artifacts:
              - dist/**
      - step:
          name: Deploy to Production
          deployment: production
          script:
            - aws --version
            - export FORCE_UPDATE=${FORCE_UPDATE}
            - export AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
            - export AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
            - export AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}
            - bash deploy.sh prod
