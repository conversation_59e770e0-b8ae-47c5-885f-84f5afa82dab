# Repd API - Feature Documentation Overview

## Introduction
This documentation provides comprehensive coverage of all features and components in the Repd API platform. Each feature is documented with detailed explanations of functionality, implementation details, API endpoints, usage examples, and related dependencies.

## Platform Overview
Repd is a civic engagement platform that enables citizens to ask questions to their local government and receive video responses from officials. The platform includes AI-powered features, multi-language support, political data integration, and comprehensive analytics.

## Architecture
- **Backend**: Node.js with Express.js framework
- **Database**: PostgreSQL with Sequelize ORM
- **Authentication**: JWT-based session management
- **File Storage**: AWS S3 with CloudFront CDN
- **AI Integration**: OpenAI, Polymorphic AI, Supabase
- **Email**: SendGrid for transactional and campaign emails
- **Transcription**: AWS Transcribe for video processing
- **Political Data**: NGP VAN integration
- **Analytics**: Google Analytics 4 and internal tracking

## Feature Documentation Index

### Core Platform Features

#### 1. [Authentication & Sessions](./01-authentication-sessions.md)
- User authentication and session management
- JWT token-based security
- Access level controls and permissions
- CSRF protection and security middleware

#### 2. [Users & Clients](./02-users-clients.md)
- Multi-tenant user management
- Client organization configuration
- User profiles and verification
- Data encryption and privacy

#### 3. [Questions](./03-questions.md)
- Question submission and management
- AI-powered categorization and sentiment analysis
- Multi-language translation support
- Admin approval workflows

#### 4. [Answers](./04-answers.md)
- Video answer creation and management
- Transcription and subtitle generation
- Engagement features (voting, comments, likes)
- Draft and approval systems

#### 5. [Voting & Engagement](./05-voting-engagement.md)
- User voting on questions and answers
- Like and comment systems
- Engagement analytics and tracking
- Content moderation features

### AI and Intelligence Features

#### 6. [AI Integration](./06-ai-integration.md)
- OpenAI integration for content analysis
- Polymorphic AI for question answering
- Automatic categorization and sentiment analysis
- AI-powered content enhancement

#### 7. [Transcription](./10-transcription.md)
- AWS Transcribe integration
- Automatic subtitle generation
- Multi-language transcription translation
- Quality control and manual overrides

### Integration Features

#### 8. [NGP VAN Integration](./07-ngp-van-integration.md)
- Political data and voter management
- Email campaign targeting
- Activist code management
- Data export and synchronization

#### 9. [Email System](./08-email-system.md)
- SendGrid integration for email delivery
- Campaign management and templates
- Email analytics and tracking
- Unsubscribe and suppression management

### Technical Infrastructure

#### 10. [File Management](./09-file-management.md)
- AWS S3 file storage and processing
- Video optimization and thumbnail generation
- Image processing and multiple formats
- Secure file access and CDN delivery

#### 11. [Statistics & Analytics](./11-statistics-analytics.md)
- Google Analytics 4 integration
- Internal event tracking and metrics
- Real-time dashboard analytics
- Custom reporting and data export

#### 12. [Scheduling](./12-scheduling.md)
- Cron-based task scheduling
- Background job processing
- Email campaign automation
- System maintenance tasks

#### 13. [External Embeds](./13-external-embeds.md)
- JavaScript widget embedding
- Client website integration
- Marvin AI-powered embeds
- Responsive design and customization

### System Architecture

#### 14. [Database Models](./14-database-models.md)
- Sequelize ORM configuration
- Data model relationships
- Encryption and security
- Performance optimization

#### 15. [API Routes](./15-api-routes.md)
- RESTful API endpoint documentation
- Authentication and authorization
- Input validation and error handling
- Rate limiting and security

## Quick Start Guide

### Prerequisites
- Node.js (v16 or higher)
- PostgreSQL database
- AWS account (for S3, Transcribe)
- SendGrid account
- OpenAI API key

### Installation
```bash
# Clone repository
git clone <repository-url>
cd repd-api

# Install dependencies
npm install

# Configure environment
cp .env.example .env
# Edit .env with your configuration

# Run database migrations
npm run migrate

# Start development server
npm run dev
```

### Environment Configuration
```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/repd

# Authentication
JWT_SECRET=your_jwt_secret_key
ENCRYPTION_KEY=your_32_character_encryption_key

# AWS Services
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
S3_BUCKET=your-s3-bucket

# Email
SENDGRID_API_KEY=your_sendgrid_api_key

# AI Services
OPENAI_API_KEY=your_openai_api_key
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_key
```

## Development Workflow

### 1. Feature Development
- Review relevant feature documentation
- Understand data models and relationships
- Implement following established patterns
- Add comprehensive tests

### 2. API Development
- Follow RESTful conventions
- Implement proper validation
- Add authentication/authorization
- Document endpoints

### 3. Testing
- Unit tests for business logic
- Integration tests for API endpoints
- End-to-end tests for critical workflows
- Performance testing for scalability

### 4. Deployment
- Environment-specific configurations
- Database migration execution
- Monitoring and logging setup
- Performance optimization

## Common Patterns

### Service Layer Pattern
```javascript
// Business logic in services
export const questionService = {
  async create(data) {
    // Validation
    // Business logic
    // Database operations
    // Return result
  }
};
```

### Error Handling
```javascript
// Consistent error handling
try {
  const result = await service.operation();
  res.json({ data: result });
} catch (error) {
  next(error); // Handled by global error middleware
}
```

### Authentication
```javascript
// Protected routes
router.post('/protected-endpoint',
  requireAuth,
  validate(schema),
  async (req, res, next) => {
    // Implementation
  }
);
```

## Security Considerations

### Data Protection
- Sensitive data encryption at rest
- HTTPS for all communications
- Input validation and sanitization
- SQL injection prevention

### Authentication & Authorization
- JWT token-based authentication
- Role-based access control
- Session management and timeout
- CSRF protection

### API Security
- Rate limiting on endpoints
- CORS configuration
- Input validation with Joi/Celebrate
- Error message sanitization

## Performance Optimization

### Database
- Proper indexing strategy
- Query optimization
- Connection pooling
- Caching frequently accessed data

### File Processing
- Asynchronous processing
- Multiple format generation
- CDN for static assets
- Lazy loading strategies

### API Performance
- Response compression
- Pagination for large datasets
- Caching strategies
- Background job processing

## Monitoring and Maintenance

### Logging
- Structured logging with Winston
- Error tracking and alerting
- Performance monitoring
- User activity tracking

### Health Monitoring
- Health check endpoints
- Database connection monitoring
- External service availability
- Performance metrics tracking

### Maintenance Tasks
- Automated database cleanup
- File storage optimization
- Log rotation and archival
- Security updates and patches

## Contributing

### Code Standards
- ESLint configuration compliance
- Consistent naming conventions
- Comprehensive documentation
- Test coverage requirements

### Pull Request Process
1. Feature branch from development
2. Implement changes with tests
3. Update relevant documentation
4. Submit PR with detailed description
5. Code review and approval
6. Merge to development branch

## Support and Resources

### Documentation
- Feature-specific README files
- API endpoint documentation
- Database schema documentation
- Deployment guides

### Development Tools
- VS Code configuration
- Debugging setup
- Testing frameworks
- Development scripts

### External Resources
- AWS documentation
- SendGrid guides
- OpenAI API documentation
- NGP VAN API reference

---

This documentation is maintained alongside the codebase. When implementing new features or modifying existing ones, please update the relevant documentation files to keep them current and accurate.
