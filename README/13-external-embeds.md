# External Embeds

## Overview
The external embeds system allows clients to integrate Repd widgets into their websites through customizable JavaScript embeds. These widgets provide a seamless way for visitors to ask questions and view answers without leaving the client's website.

## Key Components

### Files
- `scripts/external-embeds/` - Client-specific embed scripts
- `scripts/external-embeds/marvin/` - Marvin AI-powered embeds
- Individual client embed files (e.g., `denton.html`, `cullman.html`)
- Widget configuration and styling

### Embed Types

#### Standard Embeds
Basic question-asking widgets for municipal websites:
- `andreas-addison.html`
- `cullman.html`
- `denton.html`
- `gadsden.html`
- `kirkland.html`
- `leeds.html`
- `lorain.html`
- `maple-valley.html`
- `morrisville.html`
- `purcellville.html`
- `redondo.html`
- `roeland-park.html`
- `south-bay.html`
- `traverse.html`

#### Marvin AI Embeds
Advanced AI-powered widgets with enhanced features:
- `marvin/emporia.html`
- `marvin/mountjackson.html`
- `marvin/parkcity.html`
- `marvin/southbay`
- `marvin/york.html`

## Embed Architecture

### Basic Embed Structure
```javascript
// Standard embed template
<script>
  const client = {
    id: '123', // Client ID as string
    name: 'CityName'
  };

  (function embedRepdWidget(client) {
    // Validation
    if (client.id !== '123') return;

    // Create iframe
    const iframe = document.createElement('iframe');
    iframe.id = 'repd-embed';

    // Configure embed URL
    const referral = window.location.href;
    const embedUrl = new URL(`https://embed.repd.us/${client.name.toLowerCase()}`);
    embedUrl.searchParams.append('referrer', encodeURIComponent(referral));

    iframe.src = embedUrl.toString();
    
    // Apply styling
    iframe.style.cssText = `
      position: fixed;
      bottom: 0rem;
      left: 0rem;
      border: none;
      z-index: 50;
      transition: all 0.3s ease;
      background: transparent;
      width: 200px;
      height: 200px;
      max-height: calc(100vh - 40px);
      max-width: 98vw;
      border-radius: 10px;
    `;

    // Message handling for dynamic resizing
    const handleMessage = (event) => {
      if (event.origin !== 'https://embed.repd.us') return;

      const { type, dimensions } = event.data;
      
      if (type === 'resize') {
        iframe.style.width = dimensions.width;
        iframe.style.height = dimensions.height;
      }
    };

    window.addEventListener('message', handleMessage);
    document.body.appendChild(iframe);

    // Cleanup function
    window.cleanupEmbed = function () {
      window.removeEventListener('message', handleMessage);
      iframe.remove();
    };
  })(client);
</script>
```

## Widget Features

### Dynamic Resizing
```javascript
// Handle widget size changes
const handleMessage = (event) => {
  if (event.origin !== 'https://embed.repd.us') return;

  const { type, dimensions } = event.data;
  const initialHeight = dimensions.height === '200px';

  if (type === 'resize') {
    iframe.style.width = dimensions.width;
    iframe.style.height = initialHeight
      ? '200px'
      : parseInt(dimensions.height) + 400 + 'px';
  }
};
```

### Responsive Design
```css
/* Widget responsive styling */
.repd-widget {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 9999;
  transition: all 0.3s ease;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .repd-widget {
    width: 100% !important;
    left: 0 !important;
    right: 0 !important;
    border-radius: 10px 10px 0 0;
  }
}
```

### Client Customization
```javascript
// Client-specific configuration
const clientConfig = {
  id: '294',
  name: 'SouthBay',
  colors: {
    primary: '#1E40AF',
    secondary: '#3B82F6',
    accent: '#F59E0B'
  },
  position: 'bottom-left', // bottom-left, bottom-right, top-left, top-right
  size: 'medium', // small, medium, large
  features: {
    aiAnswers: true,
    videoAnswers: true,
    translations: true
  }
};
```

## Marvin AI Integration

### Enhanced AI Features
```javascript
// Marvin AI embed with enhanced capabilities
const marvinConfig = {
  client: {
    id: '283',
    name: 'Emporia'
  },
  ai: {
    enabled: true,
    provider: 'polymorphic',
    orgId: 'client-specific-org-id',
    features: {
      instantAnswers: true,
      contextualHelp: true,
      smartSuggestions: true
    }
  }
};
```

### AI-Powered Features
- **Instant Answers**: Immediate responses using AI knowledge base
- **Smart Suggestions**: Question completion and suggestions
- **Contextual Help**: Context-aware assistance based on page content
- **Multi-language Support**: Real-time translation capabilities

## Embed Deployment

### Installation Process
1. **Client Setup**: Configure client in Repd admin panel
2. **Embed Generation**: Generate client-specific embed code
3. **Website Integration**: Add embed script to client website
4. **Testing**: Verify functionality and appearance
5. **Go Live**: Deploy to production website

### Embed Code Generation
```javascript
// Generate embed code for client
export const generateEmbedCode = (clientConfig) => {
  const {
    id,
    name,
    colors,
    position = 'bottom-left',
    features = {}
  } = clientConfig;

  return `
<script>
  const client = {
    id: '${id}',
    name: '${name}',
    config: ${JSON.stringify({ colors, position, features })}
  };

  (function embedRepdWidget(client) {
    // Embed implementation
    ${getEmbedImplementation(clientConfig)}
  })(client);
</script>
  `;
};
```

## Widget Communication

### PostMessage API
```javascript
// Widget to parent communication
const sendMessageToParent = (type, data) => {
  window.parent.postMessage({
    type,
    source: 'repd-widget',
    ...data
  }, '*');
};

// Common message types
sendMessageToParent('resize', {
  dimensions: { width: '400px', height: '600px' }
});

sendMessageToParent('ready', {
  clientId: client.id,
  version: '1.0.0'
});

sendMessageToParent('question-submitted', {
  questionId: 'q123',
  text: 'What are the city hours?'
});
```

### Event Tracking
```javascript
// Track widget interactions
const trackWidgetEvent = (action, data = {}) => {
  // Send to parent for analytics
  sendMessageToParent('analytics', {
    action,
    data,
    timestamp: new Date().toISOString(),
    url: window.location.href
  });

  // Send to Repd analytics
  fetch('/api/stats/widget-event', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      clientId: client.id,
      action,
      data,
      referrer: document.referrer
    })
  });
};
```

## Security Considerations

### Origin Validation
```javascript
// Validate message origins
const ALLOWED_ORIGINS = [
  'https://embed.repd.us',
  'https://app.repd.us',
  'https://repd.us'
];

const handleMessage = (event) => {
  if (!ALLOWED_ORIGINS.includes(event.origin)) {
    console.warn('Rejected message from unauthorized origin:', event.origin);
    return;
  }

  // Process message
  processWidgetMessage(event.data);
};
```

### Content Security Policy
```html
<!-- CSP headers for embed security -->
<meta http-equiv="Content-Security-Policy" content="
  frame-src https://embed.repd.us;
  script-src 'self' 'unsafe-inline' https://embed.repd.us;
  connect-src https://api.repd.us;
">
```

### Client Validation
```javascript
// Validate client configuration
const validateClient = (client) => {
  if (!client.id || typeof client.id !== 'string') {
    throw new Error('Invalid client ID');
  }

  if (!client.name || typeof client.name !== 'string') {
    throw new Error('Invalid client name');
  }

  // Additional validation
  return true;
};
```

## Performance Optimization

### Lazy Loading
```javascript
// Load widget only when needed
const initializeWidget = () => {
  if (document.getElementById('repd-embed')) {
    return; // Already loaded
  }

  // Create and configure widget
  createWidget();
};

// Initialize on user interaction or scroll
document.addEventListener('scroll', initializeWidget, { once: true });
document.addEventListener('click', initializeWidget, { once: true });
```

### Resource Optimization
```javascript
// Minimize initial load
const loadWidgetResources = async () => {
  // Load CSS
  const cssLink = document.createElement('link');
  cssLink.rel = 'stylesheet';
  cssLink.href = 'https://embed.repd.us/widget.css';
  document.head.appendChild(cssLink);

  // Preload critical resources
  const preloadLink = document.createElement('link');
  preloadLink.rel = 'preload';
  preloadLink.href = 'https://embed.repd.us/widget.js';
  preloadLink.as = 'script';
  document.head.appendChild(preloadLink);
};
```

## Analytics and Monitoring

### Widget Analytics
```javascript
// Track widget performance
const trackWidgetMetrics = () => {
  const metrics = {
    loadTime: performance.now(),
    viewportSize: {
      width: window.innerWidth,
      height: window.innerHeight
    },
    userAgent: navigator.userAgent,
    referrer: document.referrer,
    timestamp: new Date().toISOString()
  };

  sendAnalytics('widget-load', metrics);
};
```

### Error Monitoring
```javascript
// Monitor widget errors
window.addEventListener('error', (event) => {
  if (event.filename && event.filename.includes('repd')) {
    sendAnalytics('widget-error', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack
    });
  }
});
```

## Usage Examples

### Basic Implementation
```html
<!-- Add to client website -->
<script src="https://embed.repd.us/clients/cityname.js"></script>
```

### Custom Configuration
```javascript
// Advanced configuration
window.RepdConfig = {
  clientId: '123',
  position: 'bottom-right',
  theme: 'dark',
  language: 'es',
  features: {
    aiAnswers: true,
    videoAnswers: false
  }
};
```

### Multiple Widgets
```javascript
// Multiple widgets on same page
const widgets = [
  { id: 'main-widget', clientId: '123', position: 'bottom-left' },
  { id: 'help-widget', clientId: '123', position: 'top-right', minimal: true }
];

widgets.forEach(config => {
  createWidget(config);
});
```

## Troubleshooting

### Common Issues
- **Widget not loading**: Check client ID and domain whitelist
- **Styling conflicts**: Ensure CSS isolation and z-index management
- **Message handling**: Verify origin validation and event listeners
- **Mobile responsiveness**: Test on various device sizes

### Debug Mode
```javascript
// Enable debug mode
window.RepdDebug = true;

// Debug logging
const debugLog = (message, data) => {
  if (window.RepdDebug) {
    console.log(`[Repd Widget] ${message}`, data);
  }
};
```

## Related Features
- [Users & Clients](./02-users-clients.md)
- [Questions](./03-questions.md)
- [AI Integration](./06-ai-integration.md)
- [Statistics & Analytics](./11-statistics-analytics.md)

## Dependencies
- Modern browser with postMessage support
- HTTPS for secure communication
- Client website integration capabilities
