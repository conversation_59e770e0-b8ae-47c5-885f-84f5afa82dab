# Users & Clients

## Overview
The user and client management system handles multi-tenant architecture where clients represent organizations (cities, municipalities) and users belong to specific clients. This system manages user profiles, client configurations, and access permissions.

## Key Components

### Files
- `src/models/userModel.js` - User database model
- `src/models/clientModel.js` - Client database model
- `src/services/userService.js` - User business logic
- `src/services/clientService.js` - Client business logic
- `src/routes/userRoute.js` - User API endpoints
- `src/routes/clientRoute.js` - Client API endpoints
- `src/validations/userValidation.js` - User input validation
- `src/validations/clientValidations.js` - Client input validation

### Database Models

#### Users Table
- `id` - Primary key
- `clientId` - Foreign key to clients table
- `firstName` - User's first name
- `lastName` - User's last name
- `email` - User's email address (encrypted)
- `password` - Hashed password
- `location` - User's location
- `accessLevel` - User permission level
- `enabled` - Account status
- `isVerified` - Email verification status
- `createdAt` - Account creation date
- `updatedAt` - Last profile update

#### Clients Table
- `id` - Primary key
- `name` - Client organization name
- `slug` - URL-friendly identifier
- `primaryColor` - Brand primary color
- `secondaryColor` - Brand secondary color
- `logoUrl` - Client logo URL
- `ngpVanUsername` - NGP VAN integration username
- `ngpVanApiKey` - NGP VAN API key (encrypted)
- `polymorphicOrgId` - Polymorphic AI organization ID
- `emailSettings` - Email configuration (JSON)
- `enabled` - Client status

## User Management

### User Access Levels
1. **Regular User** - Can ask questions, vote, comment
2. **Moderator** - Can approve/deny questions and answers
3. **Admin** - Full client administration
4. **Super Admin** - Cross-client administration

### User Registration Flow
```javascript
// POST /users
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "securepassword",
  "clientId": 123,
  "location": "New York, NY"
}
```

### User Verification
- Email verification required for new accounts
- Verification tokens sent via email
- `PUT /users/verify` endpoint for verification

## Client Management

### Client Configuration
```javascript
// Client settings example
{
  "id": 123,
  "name": "City of Example",
  "slug": "example-city",
  "primaryColor": "#1E40AF",
  "secondaryColor": "#3B82F6",
  "logoUrl": "https://files.repd.us/logos/example-city.png",
  "emailSettings": {
    "fromName": "City of Example",
    "fromEmail": "<EMAIL>",
    "replyTo": "<EMAIL>"
  }
}
```

### Multi-Tenancy Features
- Data isolation by client ID
- Client-specific branding and colors
- Separate user bases per client
- Client-specific email templates

## API Endpoints

### User Endpoints
- `POST /users` - Create new user account
- `GET /users/me` - Get current user profile
- `PUT /users` - Update user profile
- `PUT /users/verify` - Verify email address
- `POST /users/block` - Block/unblock user
- `GET /users/:id` - Get user by ID (admin only)

### Client Endpoints
- `GET /clients` - List all clients (admin only)
- `GET /clients/list` - Get client list for dropdown
- `GET /clients/:id` - Get client details
- `PUT /clients/:id` - Update client settings
- `POST /clients` - Create new client (super admin)

## Data Encryption

### Encrypted Fields
- User email addresses
- Client NGP VAN API keys
- Sensitive personal information

### Encryption Implementation
```javascript
// Using sequelize-encrypted
import SequelizeEncrypted from 'sequelize-encrypted';

const encrypted = SequelizeEncrypted(Sequelize, process.env.ENCRYPTION_KEY);

// Model definition with encrypted fields
email: encrypted.vault('email'),
ngpVanApiKey: encrypted.vault('ngpVanApiKey')
```

## User Authentication

### Password Security
- Passwords hashed using bcrypt
- Minimum password requirements enforced
- Password reset functionality via email

### Account Security
- Account lockout after failed attempts
- Email verification required
- Session timeout management

## Client Customization

### Branding
- Custom colors and logos
- Client-specific styling
- Branded email templates

### Integration Settings
- NGP VAN political data integration
- Polymorphic AI configuration
- Custom email settings

## Usage Examples

### Creating a User
```javascript
import userService from '../services/userService.js';

const user = await userService.create({
  firstName: 'Jane',
  lastName: 'Smith',
  email: '<EMAIL>',
  password: 'securepass123',
  clientId: 1,
  location: 'Boston, MA'
});
```

### Getting Client Configuration
```javascript
import clientService from '../services/clientService.js';

const client = await clientService.getById(123);
const branding = {
  primaryColor: client.primaryColor,
  logoUrl: client.logoUrl
};
```

## Error Handling

### Common Errors
- `409 Conflict` - Email already exists
- `404 Not Found` - User/client not found
- `403 Forbidden` - Insufficient permissions
- `422 Validation Error` - Invalid input data

## Related Features
- [Authentication & Sessions](./01-authentication-sessions.md)
- [Questions](./03-questions.md)
- [NGP VAN Integration](./07-ngp-van-integration.md)
- [Email System](./08-email-system.md)

## Dependencies
- `sequelize` - ORM for database operations
- `sequelize-encrypted` - Field encryption
- `bcrypt` - Password hashing
- `validator` - Email validation
