# Email System

## Overview
The email system provides comprehensive email functionality including transactional emails, campaign management, template rendering, and integration with SendGrid for delivery. The system supports multi-client email configurations, tracking, and analytics.

## Key Components

### Files
- `src/global/mail.js` - Email service configuration
- `src/mailers/` - Email template and sending logic
  - `_global.js` - Global email utilities
  - `answerMailer.js` - Answer notification emails
  - `ngpVanMailer.js` - NGP VAN campaign emails
  - `questionMailer.js` - Question notification emails
  - `userMailer.js` - User account emails
- `src/routes/emailRoute.js` - Email API endpoints
- `src/routes/sendgridEventsRoute.js` - SendGrid webhook handling
- `src/schedules/emailSchedule.js` - Email processing jobs
- `src/utils/sendGridUtil.js` - SendGrid utilities
- `src/views/` - Email templates (Pug)

### Database Models
- **ngpVanEmailsModel** - Email campaign tracking
- **statEventModel** - Email event tracking
- **clientModel** - Client email settings

## Email Configuration

### SendGrid Integration
```javascript
// src/global/mail.js
import sgMail from '@sendgrid/mail';

sgMail.setApiKey(process.env.SENDGRID_API_KEY);

export const sendEmail = async (emailData) => {
  try {
    const response = await sgMail.send(emailData);
    return response[0].headers['x-message-id'];
  } catch (error) {
    logger.error('Email send failed', error);
    throw error;
  }
};
```

### Environment Variables
```bash
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Repd
EMAIL_DOMAIN=repd.us
```

## Email Types

### 1. Transactional Emails
- User registration confirmation
- Password reset emails
- Email verification
- Account notifications

### 2. Campaign Emails
- Answer sharing campaigns
- NGP VAN list targeting
- Bulk notifications
- Newsletter campaigns

### 3. System Notifications
- Admin alerts
- Error notifications
- System status updates

## Email Templates

### Template Engine
Uses Pug templating engine for HTML email generation:

```pug
// src/views/email-thumbnail.pug
doctype html
html
  head
    title= subject
    style
      include email-styles.css
  body
    .container
      h1= clientName
      .content
        p= questionText
        .video-container
          img(src=thumbnailUrl alt="Video thumbnail")
          a(href=videoUrl) Watch Video
      .footer
        p Powered by Rep'd
```

### Template Variables
```javascript
// Email template context
{
  clientName: 'City of Example',
  questionText: 'What are the transportation plans?',
  videoUrl: 'https://files.repd.us/videos/answer-123.mp4',
  thumbnailUrl: 'https://files.repd.us/thumbnails/answer-123.jpg',
  unsubscribeUrl: 'https://repd.us/unsubscribe/token'
}
```

## Email Mailers

### Answer Mailer
Sends answer notifications and campaigns:

```javascript
// src/mailers/answerMailer.js
export const sendAnswerEmail = async (
  recipients,
  user,
  emailSettings,
  answer,
  savedListId,
  isNgpVan = false
) => {
  const emailData = {
    to: recipients,
    from: {
      email: emailSettings.fromEmail,
      name: emailSettings.fromName
    },
    subject: `New Answer: ${answer.question.text}`,
    html: await renderTemplate('answer-email', {
      answer,
      user,
      clientName: user.client.name
    }),
    trackingSettings: {
      clickTracking: { enable: true },
      openTracking: { enable: true }
    }
  };

  return await sendEmail(emailData);
};
```

### User Mailer
Handles user account emails:

```javascript
// User verification email
export const sendVerificationEmail = async (user, verificationToken) => {
  const verificationUrl = `${process.env.FRONTEND_URL}/verify?token=${verificationToken}`;
  
  const emailData = {
    to: user.email,
    from: process.env.SENDGRID_FROM_EMAIL,
    subject: 'Verify Your Account',
    html: await renderTemplate('user-verification', {
      user,
      verificationUrl
    })
  };

  return await sendEmail(emailData);
};
```

## Email Tracking

### SendGrid Events
Webhook endpoint for tracking email events:

```javascript
// src/routes/sendgridEventsRoute.js
router.post('/sendgrid/events', async (req, res) => {
  const events = req.body;
  
  for (const event of events) {
    await statEventModel.create({
      type: 'email',
      action: event.event, // delivered, opened, clicked, etc.
      messageId: event.sg_message_id,
      email: event.email,
      timestamp: new Date(event.timestamp * 1000),
      data: event
    });
  }
  
  res.status(200).send('OK');
});
```

### Email Analytics
```javascript
// Get email campaign statistics
export const getEmailStats = async (campaignId) => {
  const stats = await statEventModel.findAll({
    where: { campaignId },
    attributes: [
      'action',
      [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
    ],
    group: ['action']
  });

  return {
    sent: stats.find(s => s.action === 'delivered')?.count || 0,
    opened: stats.find(s => s.action === 'open')?.count || 0,
    clicked: stats.find(s => s.action === 'click')?.count || 0,
    bounced: stats.find(s => s.action === 'bounce')?.count || 0
  };
};
```

## Email Scheduling

### Batch Processing
```javascript
// src/schedules/emailSchedule.js
import cron from 'node-cron';

// Process email queue every minute
cron.schedule('* * * * *', async () => {
  const pendingEmails = await ngpVanEmailsModel.findAll({
    where: { status: 'pending' },
    limit: 100
  });

  for (const email of pendingEmails) {
    try {
      await processEmail(email);
      await email.update({ status: 'sent' });
    } catch (error) {
      await email.update({ 
        status: 'failed',
        errorMessage: error.message 
      });
    }
  }
});
```

### Rate Limiting
```javascript
// Implement rate limiting for email sending
const emailRateLimit = {
  maxPerMinute: 100,
  maxPerHour: 1000,
  queue: []
};

export const queueEmail = async (emailData) => {
  emailRateLimit.queue.push(emailData);
  
  if (emailRateLimit.queue.length >= emailRateLimit.maxPerMinute) {
    await delay(60000); // Wait 1 minute
  }
};
```

## Client Email Settings

### Per-Client Configuration
```javascript
// Client email settings
{
  emailSettings: {
    fromName: 'City of Example',
    fromEmail: '<EMAIL>',
    replyTo: '<EMAIL>',
    branding: {
      logoUrl: 'https://files.repd.us/logos/example.png',
      primaryColor: '#1E40AF',
      footerText: 'City of Example Communications'
    },
    templates: {
      answerNotification: 'custom-answer-template',
      userVerification: 'custom-verification-template'
    }
  }
}
```

### Email Customization
```javascript
// Apply client branding to emails
export const applyClientBranding = (emailHtml, client) => {
  return emailHtml
    .replace('{{CLIENT_LOGO}}', client.emailSettings.branding.logoUrl)
    .replace('{{PRIMARY_COLOR}}', client.emailSettings.branding.primaryColor)
    .replace('{{FOOTER_TEXT}}', client.emailSettings.branding.footerText);
};
```

## API Endpoints

### Email Management
- `POST /email/send` - Send individual email
- `POST /email/campaign` - Create email campaign
- `GET /email/stats/:campaignId` - Get campaign statistics
- `POST /email/test` - Send test email

### Template Management
- `GET /email/templates` - List available templates
- `POST /email/templates` - Create custom template
- `PUT /email/templates/:id` - Update template

### Webhook Endpoints
- `POST /sendgrid/events` - SendGrid event webhook
- `POST /email/unsubscribe` - Handle unsubscribe requests

## Unsubscribe Management

### Unsubscribe Handling
```javascript
// Handle unsubscribe requests
export const processUnsubscribe = async (email, listId) => {
  // Add to suppression list
  await sgMail.request({
    method: 'POST',
    url: '/v3/asm/suppressions/global',
    body: {
      recipient_emails: [email]
    }
  });

  // Update local database
  await userModel.update(
    { emailOptOut: true },
    { where: { email } }
  );
};
```

### Suppression Lists
```javascript
// Check suppression lists before sending
export const isEmailSuppressed = async (email) => {
  const response = await sgMail.request({
    method: 'GET',
    url: `/v3/asm/suppressions/global/${email}`
  });

  return response.body.length > 0;
};
```

## Error Handling

### Email Delivery Errors
```javascript
// Handle SendGrid errors
export const handleEmailError = (error, emailData) => {
  if (error.code === 400) {
    logger.error('Invalid email data', { error, emailData });
  } else if (error.code === 429) {
    // Rate limit exceeded, queue for retry
    queueEmailForRetry(emailData);
  } else {
    logger.error('Email send failed', error);
  }
};
```

### Retry Logic
```javascript
// Retry failed emails
export const retryFailedEmail = async (emailId, maxRetries = 3) => {
  const email = await ngpVanEmailsModel.findByPk(emailId);
  
  if (email.retryCount >= maxRetries) {
    await email.update({ status: 'permanently_failed' });
    return;
  }

  try {
    await sendEmail(email.emailData);
    await email.update({ status: 'sent' });
  } catch (error) {
    await email.update({ 
      retryCount: email.retryCount + 1,
      lastError: error.message 
    });
  }
};
```

## Usage Examples

### Sending Answer Notification
```javascript
import { sendAnswerEmail } from '../mailers/answerMailer.js';

const messageId = await sendAnswerEmail(
  ['<EMAIL>'],
  currentUser,
  {
    fromEmail: '<EMAIL>',
    fromName: 'City Government'
  },
  answer,
  null, // savedListId
  false // isNgpVan
);
```

### Creating Email Campaign
```javascript
import emailService from '../services/emailService.js';

const campaign = await emailService.createCampaign({
  name: 'Transportation Update',
  subject: 'New Transportation Plans Available',
  template: 'answer-notification',
  recipients: voterList,
  answerId: '123',
  clientId: 1
});
```

## Performance Considerations

### Email Queue Management
- Batch processing for large campaigns
- Rate limiting to prevent API limits
- Priority queuing for urgent emails

### Template Caching
- Cache compiled templates
- Minimize template rendering time
- Optimize image loading

## Related Features
- [NGP VAN Integration](./07-ngp-van-integration.md)
- [Users & Clients](./02-users-clients.md)
- [Statistics & Analytics](./11-statistics-analytics.md)
- [Scheduling](./12-scheduling.md)

## Dependencies
- `@sendgrid/mail` - SendGrid email service
- `pug` - Template engine
- `node-cron` - Email scheduling
- `validator` - Email validation
