# Voting & Engagement

## Overview
The voting and engagement system enables users to interact with questions and answers through voting, liking, and commenting. This system tracks user engagement, prevents duplicate interactions, and provides analytics for content quality assessment.

## Key Components

### Files
- `src/models/voteModel.js` - Vote database model
- `src/models/likeModel.js` - Like database model
- `src/models/commentModel.js` - Comment database model
- `src/services/voteService.js` - Vote business logic
- `src/services/likeService.js` - Like business logic
- `src/services/commentService.js` - Comment business logic
- `src/routes/voteRoute.js` - Vote API endpoints
- `src/routes/likesRoute.js` - Like API endpoints
- `src/routes/commentRoute.js` - Comment API endpoints

### Database Models

#### Votes Table
- `id` - Primary key
- `userId` - Foreign key to users table
- `questionId` - Foreign key to questions table (nullable)
- `answerId` - Foreign key to answers table (nullable)
- `value` - Vote value (1 for upvote, -1 for downvote)
- `createdAt` - Vote timestamp
- `enabled` - Vote status

#### Likes Table
- `id` - Primary key
- `userId` - Foreign key to users table
- `answerId` - Foreign key to answers table
- `createdAt` - Like timestamp
- `enabled` - Like status

#### Comments Table
- `id` - Primary key
- `userId` - Foreign key to users table
- `answerId` - Foreign key to answers table
- `text` - Comment content
- `createdAt` - Comment timestamp
- `enabled` - Comment visibility status

## Voting System

### Vote Types
- **Question Votes** - Users vote on question quality/importance
- **Answer Votes** - Users vote on answer helpfulness
- **Binary Voting** - Simple upvote/downvote system

### Vote Values
```javascript
// Vote value system
{
  upvote: 1,
  downvote: -1
}
```

### Vote Constraints
- One vote per user per question/answer
- Users can change their vote
- Vote deletion removes the vote entirely

## Like System

### Like Features
- Simple like/unlike functionality for answers
- One like per user per answer
- Like counts displayed publicly
- Real-time like count updates

### Like Tracking
```javascript
// Like status for user
{
  "answerId": "123",
  "liked": true,
  "likeCount": 45
}
```

## Comment System

### Comment Features
- Text-based comments on answers
- Comment threading (future enhancement)
- Comment moderation capabilities
- User attribution for comments

### Comment Validation
- Maximum character limits
- Profanity filtering
- Spam prevention
- Content moderation

## API Endpoints

### Voting Endpoints
- `POST /votes` - Cast or update vote
- `DELETE /votes/:id` - Remove vote
- `GET /votes/question/:questionId` - Get question votes
- `GET /votes/answer/:answerId` - Get answer votes

### Like Endpoints
- `POST /likes` - Like or unlike answer
- `GET /likes/answer/:answerId` - Get answer likes
- `GET /likes/user/:userId` - Get user's likes

### Comment Endpoints
- `POST /comments` - Add comment to answer
- `GET /comments/answer/:answerId` - Get answer comments
- `PUT /comments/:id` - Update comment (author only)
- `DELETE /comments/:id` - Delete comment (author/admin)

## Engagement Analytics

### Vote Aggregation
```javascript
// Vote statistics
{
  "totalVotes": 150,
  "upvotes": 120,
  "downvotes": 30,
  "score": 90,
  "percentage": 80
}
```

### Engagement Metrics
- Total engagement per content
- User engagement patterns
- Content quality indicators
- Trending content identification

## User Interaction Tracking

### Preventing Duplicates
```javascript
// Check existing vote before creating
const existingVote = await voteService.findByUserAndContent({
  userId: 123,
  answerId: 456
});

if (existingVote) {
  // Update existing vote
  await voteService.update(existingVote.id, { value: newValue });
} else {
  // Create new vote
  await voteService.create({ userId: 123, answerId: 456, value: 1 });
}
```

### User Engagement History
- Track all user interactions
- Engagement timeline
- User preference analysis
- Content recommendation data

## Real-time Updates

### Live Engagement Counts
- Real-time vote count updates
- Live like count changes
- Instant comment additions
- WebSocket integration (future enhancement)

### Optimistic Updates
- Client-side immediate feedback
- Server validation and correction
- Rollback on validation failure

## Content Moderation

### Comment Moderation
- Automatic profanity filtering
- Manual review queue
- User reporting system
- Admin moderation tools

### Spam Prevention
- Rate limiting on interactions
- Duplicate content detection
- User behavior analysis
- Automated spam filtering

## Usage Examples

### Casting a Vote
```javascript
import voteService from '../services/voteService.js';

// Vote on an answer
const vote = await voteService.create({
  userId: 123,
  answerId: 456,
  value: 1 // upvote
});

// Update existing vote
await voteService.updateByUserAndContent({
  userId: 123,
  answerId: 456,
  value: -1 // change to downvote
});
```

### Liking an Answer
```javascript
import likeService from '../services/likeService.js';

// Toggle like status
const like = await likeService.toggle({
  userId: 123,
  answerId: 456
});

// Get like status for user
const isLiked = await likeService.isLikedByUser({
  userId: 123,
  answerId: 456
});
```

### Adding a Comment
```javascript
import commentService from '../services/commentService.js';

const comment = await commentService.create({
  userId: 123,
  answerId: 456,
  text: "Great explanation! This really helped me understand the issue."
});
```

### Getting Engagement Data
```javascript
// Get complete engagement data for an answer
const engagement = await answerService.getEngagementData(answerId);
/*
Returns:
{
  votes: { total: 50, upvotes: 40, downvotes: 10, userVote: 1 },
  likes: { total: 25, userLiked: true },
  comments: { total: 8, recent: [...] }
}
*/
```

## Validation Rules

### Vote Validation
- User must be authenticated
- Cannot vote on own content (optional rule)
- Vote value must be 1 or -1
- Content must exist and be enabled

### Comment Validation
- Maximum 1000 characters
- Minimum 10 characters
- No profanity or harmful content
- User must be authenticated

## Error Handling

### Common Errors
- `409 Conflict` - Duplicate vote/like attempt
- `404 Not Found` - Content not found
- `403 Forbidden` - Cannot interact with own content
- `422 Validation Error` - Invalid input data

### Error Responses
```javascript
{
  "error": "Duplicate vote",
  "message": "You have already voted on this content",
  "code": "DUPLICATE_VOTE"
}
```

## Performance Considerations

### Database Optimization
- Indexes on user-content combinations
- Efficient vote counting queries
- Cached engagement statistics
- Batch processing for analytics

### Caching Strategy
- Cache vote counts for popular content
- Cache user engagement status
- Invalidate cache on updates
- Redis integration for real-time data

## Related Features
- [Questions](./03-questions.md)
- [Answers](./04-answers.md)
- [Users & Clients](./02-users-clients.md)
- [Statistics & Analytics](./11-statistics-analytics.md)

## Dependencies
- `sequelize` - Database ORM
- `celebrate` - Input validation
- Content moderation libraries
- Rate limiting middleware
