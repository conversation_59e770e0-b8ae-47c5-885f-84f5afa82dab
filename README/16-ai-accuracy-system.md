# AI Accuracy System

## Overview

The AI Accuracy System provides configuration-driven validation and correction of AI responses to ensure accuracy against known source content. This system is designed to catch and fix common AI inaccuracies, particularly with numerical data.

## Key Features

- **Configuration-Driven**: All validation rules and corrections are defined in JSON configuration files
- **Runtime Updates**: Configuration can be updated without code changes
- **Client-Specific Rules**: Different validation rules for different clients
- **Automatic Corrections**: Pattern-based corrections for common AI errors
- **Accuracy Scoring**: Quantitative accuracy assessment
- **Comprehensive Logging**: Detailed logging for monitoring and debugging

## Architecture

### Core Components

1. **ConfigDrivenAIValidator** (`src/utils/configDrivenAIValidator.js`)
   - Main validation and correction engine
   - Loads rules from configuration files
   - Applies validation patterns and corrections

2. **AIConfigManager** (`src/utils/aiConfigManager.js`)
   - Manages configuration updates
   - Provides runtime configuration management
   - Handles configuration validation

3. **AI Config API** (`src/routes/aiConfigRoute.js`)
   - REST API for configuration management
   - Admin-only access for security
   - Testing and validation endpoints

## Configuration Structure

### Main Configuration File: `config/ai-validation.json`

```json
{
  "validation_rules": {
    "clients": {
      "CLIENT_ID": {
        "name": "Client Name",
        "source_url": "https://source-url.com",
        "validation_patterns": [
          {
            "field": "field_name",
            "pattern": "regex_pattern",
            "expected_value": 123,
            "unit": "month",
            "description": "Description of what this validates"
          }
        ],
        "correction_patterns": [
          {
            "search_pattern": "regex_to_find",
            "replace_pattern": "replacement_text",
            "description": "What this correction fixes"
          }
        ],
        "suspicious_values": [
          {
            "correct_value": 123,
            "suspicious_values": [120, 125, 130],
            "description": "Common AI errors for this value"
          }
        ]
      }
    },
    "polymorphic_clients": ["CLIENT_ID_1", "CLIENT_ID_2"],
    "accuracy_threshold": 0.8,
    "max_corrections_per_response": 5,
    "validation_enabled": true,
    "correction_enabled": true,
    "logging": {
      "log_validation_issues": true,
      "log_corrections": true,
      "log_accuracy_scores": true
    }
  }
}
```

## Usage

### Integration in AI Route

The system is automatically integrated into the AI response pipeline:

```javascript
// Check if client should use Polymorphic AI
if (configDrivenAIValidator.shouldUsePolymorphicAI(clientId, client)) {
  // Get AI response
  const aiResponse = await getAIResponse(question);
  
  // Apply corrections
  const correctionResult = configDrivenAIValidator.correctResponse(
    aiResponse.text, 
    clientId
  );
  
  // Validate accuracy
  const validationResult = configDrivenAIValidator.validateResponse(
    correctionResult.correctedText,
    clientId,
    question
  );
}
```

### API Endpoints

#### Get Configuration
```
GET /api/v1.0.0/ai-config
```

#### Get Client Summary
```
GET /api/v1.0.0/ai-config/client/:clientId
```

#### Add Validation Rule
```
POST /api/v1.0.0/ai-config/client/:clientId/validation-rule
{
  "type": "validation_pattern",
  "data": {
    "field": "sewer_rate",
    "pattern": "sewer.*\\$?(\\d+)",
    "expected_value": 44,
    "description": "Sewer rate validation"
  }
}
```

#### Test Validation
```
POST /api/v1.0.0/ai-config/test-validation
{
  "responseText": "The sewer rate is $40 per month",
  "clientId": "297",
  "question": "What are the sewer rates?"
}
```

## Adding New Clients

### 1. Add Client to Polymorphic List
```bash
curl -X POST /api/v1.0.0/ai-config/polymorphic-clients/NEW_CLIENT_ID
```

### 2. Configure Validation Rules
```bash
curl -X POST /api/v1.0.0/ai-config/client/NEW_CLIENT_ID/validation-rule \
  -H "Content-Type: application/json" \
  -d '{
    "type": "validation_pattern",
    "data": {
      "field": "rate_field",
      "pattern": "rate.*\\$?(\\d+)",
      "expected_value": 50,
      "description": "Rate validation"
    }
  }'
```

### 3. Add Correction Patterns
```bash
curl -X POST /api/v1.0.0/ai-config/client/NEW_CLIENT_ID/validation-rule \
  -H "Content-Type: application/json" \
  -d '{
    "type": "correction_pattern",
    "data": {
      "search_pattern": "\\$45",
      "replace_pattern": "$50",
      "description": "Fix incorrect rate"
    }
  }'
```

## Monitoring and Debugging

### Log Analysis
The system provides comprehensive logging:

- **Validation Issues**: When responses don't match expected values
- **Applied Corrections**: When automatic corrections are made
- **Accuracy Scores**: Quantitative accuracy assessment

### Testing
Use the test endpoint to validate configuration:

```javascript
// Test a specific response
const testResult = await fetch('/api/v1.0.0/ai-config/test-validation', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    responseText: 'Test response with $40 rate',
    clientId: '297',
    question: 'What is the rate?'
  })
});
```

## Best Practices

### 1. Configuration Management
- Always validate configuration before deployment
- Use version control for configuration files
- Test changes in staging environment first

### 2. Pattern Design
- Use specific patterns to avoid false positives
- Include context in patterns (e.g., "inside andover" not just "$44")
- Test patterns with various response formats

### 3. Monitoring
- Monitor accuracy scores regularly
- Review validation logs for new error patterns
- Update suspicious values based on observed AI errors

### 4. Performance
- Limit number of validation patterns per client
- Use efficient regex patterns
- Monitor processing time impact

## Security Considerations

- AI configuration API requires admin access
- Configuration files should be protected
- Log sensitive data carefully
- Validate all configuration inputs

## Troubleshooting

### Common Issues

1. **Configuration Not Loading**
   - Check file permissions
   - Validate JSON syntax
   - Review error logs

2. **Patterns Not Matching**
   - Test regex patterns separately
   - Check case sensitivity
   - Verify pattern context

3. **Corrections Not Applied**
   - Ensure correction_enabled is true
   - Check pattern order (first match wins)
   - Verify replacement pattern syntax

### Debug Commands

```javascript
// Reload configuration
configDrivenAIValidator.reloadConfig();

// Get client configuration
const config = configDrivenAIValidator.getClientConfig('297');

// Test pattern matching
const text = "Sample response text";
const pattern = /pattern/i;
console.log(text.match(pattern));
```
