# Scheduling

## Overview
The scheduling system manages automated tasks, background jobs, and cron-based operations throughout the Repd platform. It handles data processing, email campaigns, analytics sync, transcription monitoring, and system maintenance tasks.

## Key Components

### Files
- `src/schedules/index.js` - Schedule coordinator and initialization
- `src/schedules/answerSchedule.js` - Answer processing jobs
- `src/schedules/emailSchedule.js` - Email campaign processing
- `src/schedules/embeddingsSchedule.js` - AI embedding generation
- `src/schedules/googleStatsSchedule.js` - Google Analytics sync
- `src/schedules/internalStatsSchedule.js` - Internal metrics processing
- `src/schedules/questionSchedule.js` - Question processing jobs
- `src/schedules/sendgridStatsSchedule.js` - Email analytics sync
- `src/schedules/sessionSchedule.js` - Session cleanup
- `cron.js` - Main cron job runner
- `scripts/js/trigger-all-schedules.js` - Manual schedule trigger

### Cron Configuration
```javascript
// cron.js - Main scheduler entry point
import './src/schedules/index.js';

// Initialize all scheduled tasks
console.log('Cron jobs initialized');
```

## Core Scheduled Tasks

### 1. Answer Processing
```javascript
// src/schedules/answerSchedule.js
import cron from 'node-cron';

// Process pending answer transcriptions every 5 minutes
cron.schedule('*/5 * * * *', async () => {
  await processTranscriptionJobs();
});

// Generate answer thumbnails every 10 minutes
cron.schedule('*/10 * * * *', async () => {
  await generatePendingThumbnails();
});

// Clean up temporary answer files daily at 2 AM
cron.schedule('0 2 * * *', async () => {
  await cleanupTempAnswerFiles();
});
```

### 2. Email Campaign Processing
```javascript
// src/schedules/emailSchedule.js
import cron from 'node-cron';

// Process email queue every minute
cron.schedule('* * * * *', async () => {
  await processEmailQueue();
});

// Send scheduled campaigns every 15 minutes
cron.schedule('*/15 * * * *', async () => {
  await sendScheduledCampaigns();
});

// Process email bounces and unsubscribes hourly
cron.schedule('0 * * * *', async () => {
  await processEmailEvents();
});
```

### 3. Analytics and Statistics
```javascript
// src/schedules/googleStatsSchedule.js
// Sync Google Analytics data daily at 1 AM
cron.schedule('0 1 * * *', async () => {
  await syncGoogleAnalyticsData();
});

// src/schedules/internalStatsSchedule.js
// Process internal statistics daily at midnight
cron.schedule('0 0 * * *', async () => {
  await processDailyStatistics();
});

// Generate weekly reports on Sundays at 3 AM
cron.schedule('0 3 * * 0', async () => {
  await generateWeeklyReports();
});
```

## Schedule Management

### Schedule Coordinator
```javascript
// src/schedules/index.js
import logger from '../global/log.js';

const schedules = [];

export const registerSchedule = (name, cronExpression, task, options = {}) => {
  const schedule = {
    name,
    cronExpression,
    task,
    enabled: options.enabled !== false,
    timezone: options.timezone || 'America/New_York',
    lastRun: null,
    nextRun: null,
    errorCount: 0
  };

  schedules.push(schedule);
  
  if (schedule.enabled) {
    startSchedule(schedule);
  }

  logger.info(`Registered schedule: ${name}`);
};

const startSchedule = (schedule) => {
  const cronJob = cron.schedule(schedule.cronExpression, async () => {
    await executeSchedule(schedule);
  }, {
    scheduled: true,
    timezone: schedule.timezone
  });

  schedule.cronJob = cronJob;
};
```

### Schedule Execution
```javascript
const executeSchedule = async (schedule) => {
  const startTime = Date.now();
  
  try {
    logger.info(`Starting schedule: ${schedule.name}`);
    
    await schedule.task();
    
    schedule.lastRun = new Date();
    schedule.errorCount = 0;
    
    const duration = Date.now() - startTime;
    logger.info(`Completed schedule: ${schedule.name} (${duration}ms)`);
    
  } catch (error) {
    schedule.errorCount++;
    
    logger.error(`Schedule failed: ${schedule.name}`, {
      error: error.message,
      stack: error.stack,
      errorCount: schedule.errorCount
    });

    // Disable schedule after too many failures
    if (schedule.errorCount >= 5) {
      schedule.enabled = false;
      schedule.cronJob.stop();
      
      logger.error(`Disabled schedule due to repeated failures: ${schedule.name}`);
      
      // Send alert to administrators
      await sendScheduleAlert(schedule, error);
    }
  }
};
```

## Specific Schedule Tasks

### Answer Processing Tasks
```javascript
// Process completed transcription jobs
export const processTranscriptionJobs = async () => {
  const pendingJobs = await transcriptModel.findAll({
    where: { status: 'IN_PROGRESS' }
  });

  for (const job of pendingJobs) {
    try {
      const awsJob = await getTranscriptionJobStatus(job.jobName);
      
      if (awsJob.TranscriptionJobStatus === 'COMPLETED') {
        await processCompletedTranscription(job);
      } else if (awsJob.TranscriptionJobStatus === 'FAILED') {
        await handleFailedTranscription(job);
      }
    } catch (error) {
      logger.error(`Failed to process transcription job ${job.jobName}`, error);
    }
  }
};

// Generate missing thumbnails
export const generatePendingThumbnails = async () => {
  const answersWithoutThumbnails = await answerModel.findAll({
    where: {
      imageUrl: null,
      videoUrl: { [Op.not]: null }
    },
    limit: 10 // Process in batches
  });

  for (const answer of answersWithoutThumbnails) {
    try {
      const thumbnailUrl = await generateVideoThumbnail(answer.videoUrl, answer.id);
      await answer.update({ imageUrl: thumbnailUrl });
    } catch (error) {
      logger.error(`Failed to generate thumbnail for answer ${answer.id}`, error);
    }
  }
};
```

### Email Processing Tasks
```javascript
// Process email campaign queue
export const processEmailQueue = async () => {
  const pendingEmails = await ngpVanEmailsModel.findAll({
    where: { status: 'pending' },
    limit: 100,
    order: [['createdAt', 'ASC']]
  });

  for (const email of pendingEmails) {
    try {
      await sendQueuedEmail(email);
      await email.update({ status: 'sent', sentAt: new Date() });
    } catch (error) {
      await email.update({ 
        status: 'failed',
        errorMessage: error.message,
        retryCount: (email.retryCount || 0) + 1
      });
      
      // Retry failed emails up to 3 times
      if (email.retryCount < 3) {
        await scheduleEmailRetry(email);
      }
    }
  }
};

// Send scheduled email campaigns
export const sendScheduledCampaigns = async () => {
  const scheduledCampaigns = await emailCampaignModel.findAll({
    where: {
      status: 'scheduled',
      scheduledAt: { [Op.lte]: new Date() }
    }
  });

  for (const campaign of scheduledCampaigns) {
    try {
      await executeCampaign(campaign);
      await campaign.update({ status: 'sending' });
    } catch (error) {
      logger.error(`Failed to start campaign ${campaign.id}`, error);
      await campaign.update({ status: 'failed', errorMessage: error.message });
    }
  }
};
```

### System Maintenance Tasks
```javascript
// Clean up old sessions
export const cleanupOldSessions = async () => {
  const cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
  
  const deletedCount = await sessionModel.destroy({
    where: {
      updatedAt: { [Op.lt]: cutoffDate }
    }
  });

  logger.info(`Cleaned up ${deletedCount} old sessions`);
};

// Archive old statistics
export const archiveOldStatistics = async () => {
  const archiveDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000); // 1 year ago
  
  const oldStats = await statEventModel.findAll({
    where: {
      timestamp: { [Op.lt]: archiveDate }
    }
  });

  // Move to archive table or external storage
  await archiveStatistics(oldStats);
  
  // Delete from main table
  await statEventModel.destroy({
    where: {
      timestamp: { [Op.lt]: archiveDate }
    }
  });

  logger.info(`Archived ${oldStats.length} old statistics`);
};
```

## Schedule Configuration

### Environment-Based Scheduling
```javascript
// Different schedules for different environments
const getScheduleConfig = () => {
  const env = process.env.NODE_ENV || 'development';
  
  const configs = {
    development: {
      emailProcessing: '*/5 * * * *', // Every 5 minutes
      transcriptionCheck: '*/10 * * * *', // Every 10 minutes
      statsSync: '0 */6 * * *' // Every 6 hours
    },
    staging: {
      emailProcessing: '*/2 * * * *', // Every 2 minutes
      transcriptionCheck: '*/5 * * * *', // Every 5 minutes
      statsSync: '0 */3 * * *' // Every 3 hours
    },
    production: {
      emailProcessing: '* * * * *', // Every minute
      transcriptionCheck: '*/5 * * * *', // Every 5 minutes
      statsSync: '0 1 * * *' // Daily at 1 AM
    }
  };

  return configs[env] || configs.development;
};
```

### Dynamic Schedule Management
```javascript
// Enable/disable schedules dynamically
export const toggleSchedule = async (scheduleName, enabled) => {
  const schedule = schedules.find(s => s.name === scheduleName);
  
  if (!schedule) {
    throw new Error(`Schedule not found: ${scheduleName}`);
  }

  if (enabled && !schedule.enabled) {
    schedule.enabled = true;
    startSchedule(schedule);
    logger.info(`Enabled schedule: ${scheduleName}`);
  } else if (!enabled && schedule.enabled) {
    schedule.enabled = false;
    schedule.cronJob.stop();
    logger.info(`Disabled schedule: ${scheduleName}`);
  }
};

// Update schedule frequency
export const updateScheduleFrequency = async (scheduleName, newCronExpression) => {
  const schedule = schedules.find(s => s.name === scheduleName);
  
  if (!schedule) {
    throw new Error(`Schedule not found: ${scheduleName}`);
  }

  // Stop current schedule
  if (schedule.cronJob) {
    schedule.cronJob.stop();
  }

  // Update and restart with new frequency
  schedule.cronExpression = newCronExpression;
  
  if (schedule.enabled) {
    startSchedule(schedule);
  }

  logger.info(`Updated schedule frequency: ${scheduleName} -> ${newCronExpression}`);
};
```

## Monitoring and Alerting

### Schedule Health Monitoring
```javascript
// Monitor schedule health
export const getScheduleHealth = () => {
  return schedules.map(schedule => ({
    name: schedule.name,
    enabled: schedule.enabled,
    lastRun: schedule.lastRun,
    nextRun: schedule.cronJob ? schedule.cronJob.nextDate() : null,
    errorCount: schedule.errorCount,
    status: schedule.errorCount > 0 ? 'warning' : 'healthy'
  }));
};

// Send alerts for failed schedules
const sendScheduleAlert = async (schedule, error) => {
  const alertData = {
    subject: `Schedule Failed: ${schedule.name}`,
    message: `Schedule "${schedule.name}" has failed ${schedule.errorCount} times.\n\nLast error: ${error.message}`,
    severity: 'high',
    timestamp: new Date()
  };

  // Send to admin notification system
  await sendAdminAlert(alertData);
};
```

### Performance Metrics
```javascript
// Track schedule performance
export const trackSchedulePerformance = async (scheduleName, duration, success) => {
  await statModel.create({
    type: 'schedule_performance',
    category: scheduleName,
    value: duration,
    metadata: {
      success,
      timestamp: new Date()
    }
  });
};
```

## API Endpoints

### Schedule Management
- `GET /admin/schedules` - List all schedules and their status
- `POST /admin/schedules/:name/toggle` - Enable/disable schedule
- `PUT /admin/schedules/:name/frequency` - Update schedule frequency
- `POST /admin/schedules/:name/trigger` - Manually trigger schedule

### Schedule Monitoring
- `GET /admin/schedules/health` - Get schedule health status
- `GET /admin/schedules/logs` - Get schedule execution logs
- `GET /admin/schedules/performance` - Get performance metrics

## Usage Examples

### Registering a Custom Schedule
```javascript
import { registerSchedule } from '../schedules/index.js';

// Register a custom cleanup task
registerSchedule(
  'custom-cleanup',
  '0 3 * * *', // Daily at 3 AM
  async () => {
    await performCustomCleanup();
  },
  {
    enabled: true,
    timezone: 'America/New_York'
  }
);
```

### Manual Schedule Execution
```javascript
// Manually trigger a schedule (useful for testing)
import { executeScheduleByName } from '../schedules/index.js';

await executeScheduleByName('email-processing');
```

### Monitoring Schedule Health
```javascript
// Get current schedule status
const scheduleHealth = getScheduleHealth();

console.log('Schedule Status:');
scheduleHealth.forEach(schedule => {
  console.log(`${schedule.name}: ${schedule.status} (errors: ${schedule.errorCount})`);
});
```

## Error Handling

### Graceful Failure Handling
```javascript
// Wrap schedule tasks with error handling
const safeScheduleWrapper = (taskFunction) => {
  return async () => {
    try {
      await taskFunction();
    } catch (error) {
      logger.error('Schedule task failed', {
        task: taskFunction.name,
        error: error.message,
        stack: error.stack
      });
      
      // Don't let one failed task crash the entire scheduler
      throw error; // Re-throw to trigger schedule error handling
    }
  };
};
```

### Recovery Mechanisms
```javascript
// Implement retry logic for critical tasks
const retryableTask = async (task, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await task();
      return; // Success
    } catch (error) {
      if (attempt === maxRetries) {
        throw error; // Final attempt failed
      }
      
      const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};
```

## Related Features
- [Email System](./08-email-system.md)
- [Transcription](./10-transcription.md)
- [Statistics & Analytics](./11-statistics-analytics.md)
- [File Management](./09-file-management.md)

## Dependencies
- `node-cron` - Cron job scheduling
- `sequelize` - Database operations
- Custom logging and monitoring utilities
- Email and notification services
