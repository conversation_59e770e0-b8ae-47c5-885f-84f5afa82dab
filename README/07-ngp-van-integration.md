# NGP VAN Integration

## Overview
The NGP VAN integration connects the Repd platform with NGP VAN's political data and voter management system. This integration enables political campaigns and organizations to import voter data, manage activist codes, create targeted email campaigns, and export engagement data back to NGP VAN.

## Key Components

### Files
- `src/services/ngpVanService.js` - Core NGP VAN integration logic
- `src/services/ngpVanCanvasResponseService.js` - Canvas response handling
- `src/routes/ngpvanRoute.js` - NGP VAN API endpoints
- `src/models/ngpVanPeopleModel.js` - Voter/people data model
- `src/models/ngpVanEmailsModel.js` - Email campaign tracking
- `src/models/ngpVanExportRequestsModel.js` - Export job tracking
- `src/models/ngpVanActivistCodesModel.js` - Activist code management
- `src/models/ngpVanSavedListsModel.js` - Saved list management
- `src/mailers/ngpVanMailer.js` - NGP VAN email templates

### Database Models

#### NGP VAN People
- `id` - Primary key
- `ngpVanId` - NGP VAN person ID
- `firstName` - Person's first name
- `lastName` - Person's last name
- `email` - Email address
- `phone` - Phone number
- `address` - Physical address
- `clientId` - Associated client
- `activistCodes` - Applied activist codes (JSON)
- `customFields` - Additional data fields (JSON)

#### NGP VAN Export Requests
- `id` - Primary key
- `clientId` - Client making the request
- `answerId` - Answer being shared
- `savedListId` - Target NGP VAN saved list
- `savedListName` - List name
- `status` - Export job status
- `exportJobId` - NGP VAN job ID
- `emailSettings` - Campaign settings (JSON)
- `peopleCount` - Number of people in export

#### NGP VAN Emails
- `id` - Primary key
- `messageId` - SendGrid message ID
- `ngpVanExportRequestId` - Associated export request
- `recipientEmail` - Email recipient
- `status` - Email delivery status
- `sentAt` - Send timestamp

## NGP VAN API Integration

### Authentication
```javascript
// NGP VAN authentication
const getNgpVanAuthObject = async (clientId, dbmode = 1) => {
  const client = await clientModel.findByPk(clientId);
  return {
    user: client.ngpVanUsername,
    password: `${client.ngpVanApiKey}|${dbmode}`
  };
};
```

### API Base Configuration
```javascript
const NGP_VAN_BASE_URL = 'https://api.securevan.com/v4';
const VOTEBUILDER_BASE_URL = 'https://api.securevan.com/v4';
```

## Core Features

### 1. Saved Lists Management
Retrieve and manage NGP VAN saved lists:

```javascript
// Get saved lists for a client
const savedLists = await getNgpVanSavedLists(clientId);
/*
Returns:
[
  {
    savedListId: 12345,
    name: "Registered Voters - District 1",
    description: "Active voters in district 1",
    count: 1500
  }
]
*/
```

### 2. Activist Codes
Manage activist codes for voter engagement tracking:

```javascript
// Get available activist codes
const activistCodes = await getNgpVanActivistCodes(clientId);

// Apply activist code to voter
await applyActivistCode(personId, activistCodeId, clientId);
```

### 3. Email Campaign Integration
Send targeted email campaigns to NGP VAN lists:

```javascript
// Prepare email campaign
await prepareNgpVanEmails(
  answerId,
  exportJobId,
  savedListId,
  savedListName,
  status,
  people,
  user,
  emailSettings
);
```

### 4. Data Export
Export engagement data back to NGP VAN:

```javascript
// Export answer engagement to NGP VAN
const exportRequest = await createExportRequest({
  answerId: "123",
  savedListId: 12345,
  clientId: 1,
  emailSettings: {
    subject: "City Update: Transportation Plans",
    fromName: "City of Example"
  }
});
```

## API Endpoints

### List Management
- `GET /ngpvan/lists` - Get saved lists for client
- `GET /ngpvan/list/:id` - Get specific list details
- `POST /ngpvan/list/:id/export` - Export to specific list

### Activist Codes
- `GET /ngpvan/get-activist-codes` - Get available activist codes
- `POST /ngpvan/get-activist-codes/sync` - Sync activist codes
- `POST /ngpvan/activist-code/apply` - Apply code to person

### Email Campaigns
- `POST /ngpvan/test-email` - Send test email
- `GET /ngpvan/answerExports` - Get export job status
- `POST /ngpvan/resend` - Resend failed emails

### Data Import
- `POST /ngpvan/import` - Import voter data from NGP VAN
- `GET /ngpvan/import/status` - Check import job status

## Email Campaign System

### Email Templates
Customizable email templates for different campaign types:

```javascript
// Email template structure
{
  subject: "City Update: {{answer.question.category}}",
  fromName: "{{client.name}}",
  fromEmail: "noreply@{{client.domain}}",
  template: "ngp-van-answer-email",
  variables: {
    answerUrl: "{{answer.videoUrl}}",
    questionText: "{{answer.question.text}}",
    clientName: "{{client.name}}"
  }
}
```

### Batch Email Processing
```javascript
// Send emails in batches to manage rate limits
const sendNgpVanEmailsInBatches = async (
  people,
  answer,
  savedListId,
  savedListName,
  user,
  emailSettings
) => {
  const batchSize = 1000;
  const messageIds = [];

  for (let i = 0; i < people.length; i += batchSize) {
    const batch = people.slice(i, i + batchSize);
    const emails = batch.map(person => person.email);
    
    const messageId = await sendAnswerEmail(
      emails,
      user,
      emailSettings,
      answer,
      savedListId
    );
    
    messageIds.push(messageId);
  }

  return messageIds;
};
```

## Data Synchronization

### Voter Data Import
```javascript
// Import voter data from NGP VAN saved list
const importVoterData = async (savedListId, clientId) => {
  const auth = await getNgpVanAuthObject(clientId);
  
  const response = await http.get({
    url: `${NGP_VAN_BASE_URL}/savedLists/${savedListId}/people`,
    auth: auth,
    json: true
  });

  // Process and store voter data
  for (const person of response.items) {
    await ngpVanPeopleModel.upsert({
      ngpVanId: person.vanId,
      firstName: person.firstName,
      lastName: person.lastName,
      email: person.email,
      clientId: clientId
    });
  }
};
```

### Engagement Data Export
```javascript
// Export engagement metrics back to NGP VAN
const exportEngagementData = async (answerId, savedListId) => {
  const engagementData = await getAnswerEngagement(answerId);
  
  // Apply activist codes based on engagement
  for (const engagement of engagementData) {
    if (engagement.watched) {
      await applyActivistCode(
        engagement.ngpVanId,
        'VIDEO_WATCHED',
        clientId
      );
    }
    
    if (engagement.liked) {
      await applyActivistCode(
        engagement.ngpVanId,
        'CONTENT_LIKED',
        clientId
      );
    }
  }
};
```

## Configuration

### Client NGP VAN Settings
```javascript
// Client model NGP VAN fields
{
  ngpVanUsername: 'client_username',
  ngpVanApiKey: 'encrypted_api_key',
  ngpVanDbMode: 1, // 0 = MyVoters, 1 = MyCampaign
  emailSettings: {
    fromName: 'Campaign Name',
    fromEmail: '<EMAIL>',
    replyTo: '<EMAIL>'
  }
}
```

### Environment Variables
```bash
NGP_VAN_BASE_URL=https://api.securevan.com/v4
NGP_VAN_TIMEOUT=30000
NGP_VAN_RATE_LIMIT=100
```

## Error Handling

### NGP VAN API Errors
```javascript
// Common NGP VAN error responses
{
  "errors": [
    {
      "code": "INVALID_PARAMETER",
      "text": "The specified saved list was not found",
      "referenceCode": "SL001"
    }
  ]
}
```

### Retry Logic
```javascript
// Implement retry for transient failures
const retryNgpVanRequest = async (requestFn, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      if (attempt === maxRetries || !isRetryableError(error)) {
        throw error;
      }
      
      await delay(attempt * 1000); // Exponential backoff
    }
  }
};
```

## Usage Examples

### Setting Up NGP VAN Integration
```javascript
// Configure client for NGP VAN
await clientService.update(clientId, {
  ngpVanUsername: 'campaign_user',
  ngpVanApiKey: 'api_key_here',
  emailSettings: {
    fromName: 'City of Example',
    fromEmail: '<EMAIL>'
  }
});
```

### Creating Email Campaign
```javascript
// Send answer to NGP VAN saved list
const campaign = await ngpVanService.prepareNgpVanEmails(
  answerId,
  null, // exportJobId
  savedListId,
  'Registered Voters',
  'pending',
  voterList,
  currentUser,
  emailSettings
);
```

## Security Considerations

### API Key Protection
- NGP VAN API keys stored encrypted
- Keys never logged or exposed in responses
- Secure key rotation procedures

### Data Privacy
- Voter data handled according to privacy laws
- Secure data transmission
- Audit trails for data access

## Related Features
- [Email System](./08-email-system.md)
- [Users & Clients](./02-users-clients.md)
- [Statistics & Analytics](./11-statistics-analytics.md)
- [Scheduling](./12-scheduling.md)

## Dependencies
- `request` - HTTP client for NGP VAN API
- `sequelize-encrypted` - API key encryption
- SendGrid integration for email delivery
- Custom authentication utilities
