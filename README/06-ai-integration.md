# AI Integration

## Overview
The AI integration system provides intelligent features throughout the Repd platform, including question categorization, sentiment analysis, content generation, and AI-powered question answering. The system integrates with OpenAI, Polymorphic AI, and Supabase for various AI capabilities.

## Key Components

### Files
- `src/global/ai.js` - AI service configurations and clients
- `src/helpers/ai.js` - AI helper functions
- `src/prompts/` - AI prompt templates directory
- `src/routes/aiRoute.js` - AI API endpoints
- `src/models/aiQueryModel.js` - AI query tracking model

### AI Services Integration

#### OpenAI Integration
- GPT models for text generation and analysis
- Embedding models for semantic search
- Content classification and categorization
- Sentiment analysis

#### Polymorphic AI Integration
- City-specific question answering
- Context-aware responses
- Organization-specific knowledge bases
- Search and retrieval capabilities

#### Supabase AI
- Vector embeddings storage
- Semantic similarity search
- Answer matching based on question embeddings

## AI Features

### 1. Question Categorization
Automatically categorizes questions into predefined topics:

```javascript
// Automatic categorization on question creation
const category = await classifyQuestion(questionText);
// Returns: "transportation", "housing", "public-safety", etc.
```

### 2. Sentiment Analysis
Analyzes emotional tone of questions:

```javascript
// Sentiment scoring (1-3 scale)
const sentiment = await createSentiment(questionText);
// Returns: 1 (positive), 2 (neutral), 3 (negative)
```

### 3. Category Summary Generation
Creates contextual summaries for admin review:

```javascript
// AI-generated category summary
const summary = await createCategorySummary(questionText);
// Returns: Brief explanation of question topic and context
```

### 4. AI-Powered Question Answering
Provides intelligent responses using city-specific knowledge:

```javascript
// Polymorphic AI response
const response = await PolymorphicClient().getCitySiteAnswers(
  questionText,
  polymorphicOrgId
);
```

## AI Prompts System

### Prompt Templates
Located in `src/prompts/` directory:

- `createQuestionClassificationPrompt.js` - Question categorization
- `get-category-for-ai-query.js` - Category determination
- `get-category-summary-for-question.js` - Summary generation
- `get-sentiment-for-question.js` - Sentiment analysis
- `generate-questions-from-answer-transcription.js` - Question generation
- `teleprompter-format.js` - Content formatting

### Prompt Engineering
```javascript
// Example prompt structure
export const createCategoryPrompt = (questionText) => {
  return `
    Analyze the following question and categorize it into one of these categories:
    - transportation
    - housing
    - public-safety
    - environment
    - budget
    - health
    - education
    - infrastructure
    
    Question: "${questionText}"
    
    Return only the category name in lowercase.
  `;
};
```

## AI Query Tracking

### Database Model
```javascript
// aiQueryModel tracks AI API usage
{
  id: 'Primary key',
  queryType: 'Type of AI query (categorization, sentiment, etc.)',
  inputText: 'Input text sent to AI',
  outputText: 'AI response',
  model: 'AI model used',
  tokensUsed: 'Number of tokens consumed',
  cost: 'API call cost',
  clientId: 'Client making the request',
  userId: 'User initiating the query',
  createdAt: 'Timestamp'
}
```

### Usage Analytics
- Track AI API usage per client
- Monitor token consumption
- Cost analysis and budgeting
- Performance metrics

## API Endpoints

### AI Query Endpoints
- `POST /ai/categorize` - Categorize question text
- `POST /ai/sentiment` - Analyze sentiment
- `POST /ai/summary` - Generate category summary
- `POST /ai/answer` - Get AI-powered answer

### AI Analytics
- `GET /ai/usage` - Get AI usage statistics
- `GET /ai/costs` - Get cost breakdown
- `GET /ai/performance` - Get performance metrics

## Configuration

### Environment Variables
```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002

# Polymorphic AI Configuration
POLYMORPHIC_BASE_URL=https://api.polimorphic.com/ai
POLYMORPHIC_ORG_ID=your_org_id

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_key
```

### AI Service Configuration
```javascript
// src/global/ai.js
export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  organization: process.env.OPENAI_ORG_ID
});

export const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);
```

## Embedding System

### Question Embeddings
- Generate embeddings for questions using OpenAI
- Store embeddings in Supabase vector database
- Enable semantic similarity search
- Match related questions and answers

### Answer Matching
```javascript
// Find similar answers based on question embeddings
const similarAnswers = await supabase
  .rpc('match_answers', {
    query_embedding: questionEmbedding,
    match_threshold: 0.8,
    match_count: 5
  });
```

## AI-Powered Features

### 1. Smart Question Suggestions
- Suggest similar existing questions
- Prevent duplicate questions
- Guide users to existing answers

### 2. Content Enhancement
- Improve question clarity
- Suggest better phrasing
- Auto-correct common issues

### 3. Response Quality Assessment
- Analyze answer quality
- Suggest improvements
- Flag low-quality content

### 4. Automated Moderation
- Content safety checking
- Inappropriate content detection
- Spam identification

## Usage Examples

### Question Categorization
```javascript
import { classifyQuestion } from '../helpers/ai.js';

const question = "When will the new bike lanes be completed?";
const category = await classifyQuestion(question);
console.log(category); // "transportation"
```

### Sentiment Analysis
```javascript
import { createSentiment } from '../helpers/ai.js';

const question = "I'm frustrated with the lack of parking downtown";
const sentiment = await createSentiment(question);
console.log(sentiment); // 3 (negative)
```

### AI-Powered Answering
```javascript
import { PolymorphicClient } from '../global/ai.js';

const client = PolymorphicClient();
const response = await client.getCitySiteAnswers(
  "What are the city's recycling policies?",
  "city-org-id-123"
);
```

## Error Handling

### AI Service Errors
- API rate limiting
- Token limit exceeded
- Service unavailability
- Invalid API keys

### Fallback Strategies
```javascript
// Graceful degradation
try {
  const category = await classifyQuestion(text);
} catch (error) {
  // Fallback to default category
  const category = 'general';
  logger.warn('AI categorization failed, using default', error);
}
```

## Performance Optimization

### Caching Strategy
- Cache AI responses for identical inputs
- Reduce redundant API calls
- Implement TTL for cached responses

### Batch Processing
- Process multiple requests together
- Optimize token usage
- Reduce API call overhead

### Rate Limiting
- Implement client-side rate limiting
- Queue requests during high usage
- Prioritize critical AI features

## Cost Management

### Token Optimization
- Minimize prompt length
- Use appropriate models for tasks
- Implement token counting

### Budget Controls
- Set spending limits per client
- Monitor usage patterns
- Alert on unusual usage

## Related Features
- [Questions](./03-questions.md)
- [Answers](./04-answers.md)
- [Statistics & Analytics](./11-statistics-analytics.md)
- [Database Models](./14-database-models.md)

## Dependencies
- `openai` - OpenAI API client
- `@supabase/supabase-js` - Supabase client
- `axios` - HTTP client for Polymorphic AI
- Custom prompt engineering utilities
