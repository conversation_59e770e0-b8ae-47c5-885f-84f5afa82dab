# Transcription

## Overview
The transcription system provides automatic speech-to-text conversion for video answers using AWS Transcribe. It includes subtitle generation, multi-language translation, timing synchronization, and manual transcription override capabilities.

## Key Components

### Files
- `src/global/transcription.js` - AWS Transcribe configuration
- `src/services/transcriptService.js` - Transcription business logic
- `src/routes/transcriptRoute.js` - Transcription API endpoints
- `src/models/transcriptModel.js` - Transcription database model
- `src/validations/transcriptValidation.js` - Input validation
- `src/schedules/` - Automated transcription processing
- `scripts/js/start-transcription-jobs-for-all-answers.js` - Batch processing script

### Database Model
- `id` - Primary key
- `answerId` - Foreign key to answers table
- `jobName` - AWS Transcribe job identifier
- `status` - Transcription job status
- `transcript` - Raw transcript text
- `segments` - Timed transcript segments (JSON)
- `confidence` - Overall confidence score
- `languageCode` - Source language
- `translations` - Multi-language translations (JSON)
- `customVocabulary` - Custom vocabulary used
- `speakerLabels` - Speaker identification data
- `createdAt` - Job creation timestamp
- `completedAt` - Job completion timestamp

## AWS Transcribe Integration

### Service Configuration
```javascript
// src/global/transcription.js
import AWS from 'aws-sdk';

const transcribe = new AWS.TranscribeService({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION
});

export const startTranscriptionJob = async (jobName, mediaUri, languageCode = 'en-US') => {
  const params = {
    TranscriptionJobName: jobName,
    LanguageCode: languageCode,
    Media: {
      MediaFileUri: mediaUri
    },
    OutputBucketName: process.env.AWS_TRANSCRIBE_BUCKET,
    Settings: {
      ShowSpeakerLabels: true,
      MaxSpeakerLabels: 5,
      ShowAlternatives: true,
      MaxAlternatives: 3
    }
  };

  return await transcribe.startTranscriptionJob(params).promise();
};
```

### Job Status Monitoring
```javascript
// Check transcription job status
export const getTranscriptionJobStatus = async (jobName) => {
  const params = { TranscriptionJobName: jobName };
  
  const result = await transcribe.getTranscriptionJob(params).promise();
  return result.TranscriptionJob;
};
```

## Transcription Process

### 1. Job Initiation
```javascript
// Start transcription for answer
export const createTranscription = async (answerId, videoUrl) => {
  const jobName = `transcribe-${answerId}-${Date.now()}`;
  
  // Start AWS Transcribe job
  await startTranscriptionJob(jobName, videoUrl);
  
  // Create database record
  const transcription = await transcriptModel.create({
    answerId,
    jobName,
    status: 'IN_PROGRESS',
    languageCode: 'en-US'
  });

  return transcription;
};
```

### 2. Job Processing
```javascript
// Process completed transcription job
export const processCompletedJob = async (jobName) => {
  const job = await getTranscriptionJobStatus(jobName);
  
  if (job.TranscriptionJobStatus !== 'COMPLETED') {
    return;
  }

  // Download transcript from S3
  const transcriptUrl = job.Transcript.TranscriptFileUri;
  const transcriptData = await downloadTranscript(transcriptUrl);
  
  // Update database record
  await transcriptModel.update({
    status: 'COMPLETED',
    transcript: transcriptData.results.transcripts[0].transcript,
    segments: transcriptData.results.items,
    confidence: calculateAverageConfidence(transcriptData.results.items),
    completedAt: new Date()
  }, {
    where: { jobName }
  });

  // Generate translations
  await generateTranslations(jobName);
};
```

### 3. Subtitle Generation
```javascript
// Generate subtitle format from transcript segments
export const generateSubtitles = (segments, format = 'srt') => {
  let subtitles = '';
  let index = 1;
  
  // Group segments into subtitle blocks
  const blocks = groupSegmentsByTime(segments, 3); // 3-second blocks
  
  for (const block of blocks) {
    const startTime = formatTime(block.start_time);
    const endTime = formatTime(block.end_time);
    const text = block.alternatives[0].content;
    
    if (format === 'srt') {
      subtitles += `${index}\n${startTime} --> ${endTime}\n${text}\n\n`;
    } else if (format === 'vtt') {
      subtitles += `${startTime} --> ${endTime}\n${text}\n\n`;
    }
    
    index++;
  }
  
  return subtitles;
};
```

## Multi-Language Support

### Translation Integration
```javascript
// Translate transcript to multiple languages
export const generateTranslations = async (jobName) => {
  const transcription = await transcriptModel.findOne({ where: { jobName } });
  
  const targetLanguages = ['es', 'fr', 'de', 'zh'];
  const translations = {};
  
  for (const lang of targetLanguages) {
    const translatedText = await translateText(
      transcription.transcript,
      'en',
      lang
    );
    
    // Translate individual segments for timing
    const translatedSegments = await translateSegments(
      transcription.segments,
      'en',
      lang
    );
    
    translations[lang] = {
      transcript: translatedText,
      segments: translatedSegments
    };
  }
  
  await transcription.update({ translations });
};
```

### Language Detection
```javascript
// Auto-detect source language
export const detectLanguage = async (audioUrl) => {
  const params = {
    TranscriptionJobName: `detect-${Date.now()}`,
    IdentifyLanguage: true,
    Media: { MediaFileUri: audioUrl }
  };
  
  const job = await transcribe.startTranscriptionJob(params).promise();
  
  // Wait for completion and get detected language
  const result = await waitForJobCompletion(job.TranscriptionJob.TranscriptionJobName);
  return result.LanguageCode;
};
```

## Subtitle Features

### Subtitle Formats
- **SRT** - Standard subtitle format
- **VTT** - Web Video Text Tracks
- **ASS** - Advanced SubStation Alpha
- **JSON** - Custom JSON format with timing

### Subtitle Customization
```javascript
// Customize subtitle appearance
export const customizeSubtitles = (subtitles, options) => {
  const {
    fontSize = 16,
    fontColor = '#FFFFFF',
    backgroundColor = '#000000',
    position = 'bottom',
    maxCharsPerLine = 40
  } = options;
  
  // Apply styling and formatting
  return formatSubtitles(subtitles, {
    fontSize,
    fontColor,
    backgroundColor,
    position,
    maxCharsPerLine
  });
};
```

### Manual Override
```javascript
// Allow manual subtitle editing
export const updateSubtitles = async (transcriptionId, manualSubtitles) => {
  await transcriptModel.update({
    manualSubtitles,
    hasManualOverride: true,
    updatedAt: new Date()
  }, {
    where: { id: transcriptionId }
  });
};
```

## API Endpoints

### Transcription Management
- `POST /transcripts` - Start new transcription job
- `GET /transcripts/:id` - Get transcription status
- `PUT /transcripts/:id` - Update transcription
- `DELETE /transcripts/:id` - Delete transcription

### Subtitle Operations
- `GET /transcripts/:id/subtitles` - Get subtitle file
- `POST /transcripts/:id/subtitles` - Upload manual subtitles
- `GET /transcripts/:id/subtitles/:format` - Get subtitles in specific format

### Translation Endpoints
- `POST /transcripts/:id/translate` - Generate translations
- `GET /transcripts/:id/translations/:language` - Get specific translation

## Quality Control

### Confidence Scoring
```javascript
// Calculate transcript confidence
export const calculateConfidence = (segments) => {
  const confidenceScores = segments
    .filter(item => item.type === 'pronunciation')
    .map(item => parseFloat(item.alternatives[0].confidence));
  
  const averageConfidence = confidenceScores.reduce((a, b) => a + b, 0) / confidenceScores.length;
  
  return {
    average: averageConfidence,
    minimum: Math.min(...confidenceScores),
    maximum: Math.max(...confidenceScores),
    lowConfidenceCount: confidenceScores.filter(score => score < 0.8).length
  };
};
```

### Quality Flags
```javascript
// Flag low-quality transcriptions
export const flagLowQuality = async (transcriptionId) => {
  const transcription = await transcriptModel.findByPk(transcriptionId);
  const confidence = calculateConfidence(transcription.segments);
  
  if (confidence.average < 0.7 || confidence.lowConfidenceCount > 10) {
    await transcription.update({
      needsReview: true,
      qualityFlags: ['low_confidence', 'manual_review_required']
    });
  }
};
```

## Scheduled Processing

### Batch Job Processing
```javascript
// Process pending transcription jobs
import cron from 'node-cron';

// Check for completed jobs every 5 minutes
cron.schedule('*/5 * * * *', async () => {
  const pendingJobs = await transcriptModel.findAll({
    where: { status: 'IN_PROGRESS' }
  });

  for (const job of pendingJobs) {
    try {
      await processCompletedJob(job.jobName);
    } catch (error) {
      logger.error(`Failed to process job ${job.jobName}`, error);
      
      await job.update({
        status: 'FAILED',
        errorMessage: error.message
      });
    }
  }
});
```

### Cleanup Jobs
```javascript
// Clean up old transcription files
cron.schedule('0 2 * * *', async () => {
  const oldJobs = await transcriptModel.findAll({
    where: {
      createdAt: {
        [Op.lt]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
      },
      status: 'COMPLETED'
    }
  });

  for (const job of oldJobs) {
    await deleteTranscriptionJob(job.jobName);
    await job.destroy();
  }
});
```

## Usage Examples

### Starting Transcription
```javascript
import transcriptService from '../services/transcriptService.js';

// Start transcription for video answer
const transcription = await transcriptService.create({
  answerId: '123',
  videoUrl: 'https://files.repd.us/videos/answer-123.mp4',
  languageCode: 'en-US'
});
```

### Getting Subtitles
```javascript
// Get subtitles in SRT format
const subtitles = await transcriptService.getSubtitles(
  transcriptionId,
  'srt',
  { language: 'en' }
);
```

### Manual Subtitle Override
```javascript
// Update with manual subtitles
await transcriptService.updateSubtitles(transcriptionId, {
  subtitles: "1\n00:00:00,000 --> 00:00:03,000\nHello, thank you for your question\n\n",
  format: 'srt',
  isManual: true
});
```

## Error Handling

### Common Errors
- `400 Bad Request` - Invalid audio format
- `404 Not Found` - Transcription job not found
- `422 Unprocessable Entity` - Audio quality too poor
- `503 Service Unavailable` - AWS Transcribe service issues

### Error Recovery
```javascript
// Retry failed transcription jobs
export const retryTranscription = async (transcriptionId) => {
  const transcription = await transcriptModel.findByPk(transcriptionId);
  
  if (transcription.retryCount >= 3) {
    await transcription.update({ status: 'PERMANENTLY_FAILED' });
    return;
  }

  // Start new job
  const newJobName = `${transcription.jobName}-retry-${transcription.retryCount + 1}`;
  
  await startTranscriptionJob(newJobName, transcription.videoUrl);
  
  await transcription.update({
    jobName: newJobName,
    status: 'IN_PROGRESS',
    retryCount: transcription.retryCount + 1
  });
};
```

## Performance Optimization

### Parallel Processing
- Process multiple jobs simultaneously
- Queue management for high-volume periods
- Priority processing for urgent content

### Caching
- Cache frequently accessed transcripts
- Store processed subtitles for quick retrieval
- Cache translation results

## Related Features
- [Answers](./04-answers.md)
- [File Management](./09-file-management.md)
- [AI Integration](./06-ai-integration.md)
- [Scheduling](./12-scheduling.md)

## Dependencies
- `aws-sdk` - AWS Transcribe integration
- `node-cron` - Scheduled processing
- Translation services (Google Translate, AWS Translate)
- Subtitle formatting libraries
