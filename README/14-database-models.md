# Database Models

## Overview
The database models define the data structure and relationships for the Repd platform using Sequelize ORM with PostgreSQL. The system uses a multi-tenant architecture with encrypted sensitive data and comprehensive relationship mapping.

## Key Components

### Files
- `src/models/index.js` - Model associations and initialization
- `src/models/_globals.js` - Global model utilities and base configuration
- `src/db/config.js` - Database connection configuration
- `src/db/db.schema.sql` - Database schema definition
- `src/utils/modelUtil.js` - Model utility functions
- `src/utils/encryption.js` - Data encryption utilities

### Database Configuration
```javascript
// src/db/config.js
import Sequelize from 'sequelize';
import env from '../global/environment.js';

export const db = new Sequelize(
  env.getProperty('database.name'),
  env.getProperty('database.username'),
  env.getProperty('database.password'),
  {
    host: env.getProperty('database.host'),
    port: env.getProperty('database.port'),
    dialect: 'postgres',
    logging: env.getProperty('database.logging'),
    pool: {
      max: 20,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  }
);
```

## Core Models

### 1. Users Model
```javascript
// src/models/userModel.js
const userModel = db.define('users', {
  id: { type: Sequelize.BIGINT, primaryKey: true, autoIncrement: true },
  clientId: { field: 'client_id', type: Sequelize.BIGINT, references: { model: 'clients', key: 'id' } },
  
  firstName: { field: 'first_name', type: Sequelize.STRING, allowNull: false },
  lastName: { field: 'last_name', type: Sequelize.STRING, allowNull: false },
  email: encrypted.vault('email'), // Encrypted field
  password: { type: Sequelize.STRING, allowNull: false },
  
  location: { type: Sequelize.STRING },
  accessLevel: { field: 'access_level', type: Sequelize.STRING, defaultValue: 'user' },
  
  isVerified: { field: 'is_verified', type: Sequelize.BOOLEAN, defaultValue: false },
  enabled: { type: Sequelize.BOOLEAN, defaultValue: true },
  
  createdAt: { field: 'created_at', type: Sequelize.DATE },
  updatedAt: { field: 'updated_at', type: Sequelize.DATE }
}, {
  timestamps: true,
  tableName: 'users'
});
```

### 2. Clients Model
```javascript
// src/models/clientModel.js
const clientModel = db.define('clients', {
  id: { type: Sequelize.BIGINT, primaryKey: true, autoIncrement: true },
  
  name: { type: Sequelize.STRING, allowNull: false },
  slug: { type: Sequelize.STRING, unique: true },
  
  primaryColor: { field: 'primary_color', type: Sequelize.STRING },
  secondaryColor: { field: 'secondary_color', type: Sequelize.STRING },
  logoUrl: { field: 'logo_url', type: Sequelize.STRING },
  
  ngpVanUsername: { field: 'ngp_van_username', type: Sequelize.STRING },
  ngpVanApiKey: encrypted.vault('ngp_van_api_key'), // Encrypted field
  polymorphicOrgId: { field: 'polymorphic_org_id', type: Sequelize.STRING },
  
  emailSettings: { field: 'email_settings', type: Sequelize.JSONB },
  
  enabled: { type: Sequelize.BOOLEAN, defaultValue: true },
  createdAt: { field: 'created_at', type: Sequelize.DATE },
  updatedAt: { field: 'updated_at', type: Sequelize.DATE }
}, {
  timestamps: true,
  tableName: 'clients'
});
```

### 3. Questions Model
```javascript
// src/models/questionModel.js
const questionModel = db.define('questions', {
  id: { type: Sequelize.BIGINT, primaryKey: true, autoIncrement: true },
  
  text: { type: Sequelize.STRING(2000), allowNull: false, unique: true },
  category: { type: Sequelize.STRING, allowNull: false },
  categoryIcon: { field: 'category_icon', type: Sequelize.STRING },
  categorySummary: { field: 'category_summary', type: Sequelize.STRING },
  
  clientId: { field: 'client_id', type: Sequelize.BIGINT, references: { model: 'clients', key: 'id' } },
  userId: { field: 'user_id', type: Sequelize.BIGINT, allowNull: false },
  
  originalLanguage: { field: 'original_language', type: Sequelize.STRING },
  translations: { type: Sequelize.JSONB },
  
  sentiment: { type: Sequelize.INTEGER }, // 1-3 scale
  suggestedNotes: { field: 'suggested_notes', type: Sequelize.JSON },
  transcript: { type: Sequelize.JSON },
  
  isApproved: { field: 'is_approved', type: Sequelize.BOOLEAN },
  isDenied: { field: 'is_denied', type: Sequelize.BOOLEAN },
  isAnswered: { field: 'is_answered', type: Sequelize.BOOLEAN },
  isShared: { field: 'is_shared', type: Sequelize.BOOLEAN },
  
  enabled: { type: Sequelize.BOOLEAN, defaultValue: true },
  createdAt: { field: 'created_at', type: Sequelize.DATE },
  updatedAt: { field: 'updated_at', type: Sequelize.DATE }
}, {
  timestamps: true,
  tableName: 'questions'
});
```

### 4. Answers Model
```javascript
// src/models/answerModel.js
const answerModel = db.define('answers', {
  id: { type: Sequelize.BIGINT, primaryKey: true, autoIncrement: true },
  
  clientId: { field: 'client_id', type: Sequelize.BIGINT, references: { model: 'clients', key: 'id' } },
  userId: { field: 'user_id', type: Sequelize.BIGINT, references: { model: 'users', key: 'id' } },
  questionId: { field: 'question_id', type: Sequelize.BIGINT, references: { model: 'questions', key: 'id' } },
  
  videoUrl: { field: 'video_url', type: Sequelize.STRING },
  videoUrls: { field: 'video_urls', type: Sequelize.JSONB },
  imageUrl: { field: 'image_url', type: Sequelize.STRING },
  videoDuration: { field: 'video_duration', type: Sequelize.INTEGER },
  
  subtitles: { type: Sequelize.TEXT },
  subtitlesSpeed: { field: 'subtitles_speed', type: Sequelize.FLOAT },
  subtitlesTranslations: { field: 'subtitles_translations', type: Sequelize.JSONB },
  
  transcription: { type: Sequelize.JSONB },
  transcriptionTranslation: { field: 'transcription_translation', type: Sequelize.JSONB },
  showTranscribedSubtitles: { field: 'show_transcribed_subtitles', type: Sequelize.BOOLEAN, defaultValue: false },
  
  isDraft: { field: 'is_draft', type: Sequelize.BOOLEAN, defaultValue: false },
  isApproved: { field: 'is_approved', type: Sequelize.BOOLEAN },
  isDenied: { field: 'is_denied', type: Sequelize.BOOLEAN },
  isPinned: { field: 'is_pinned', type: Sequelize.BOOLEAN, defaultValue: false },
  isShared: { field: 'is_shared', type: Sequelize.BOOLEAN },
  
  endDate: { field: 'end_date', type: Sequelize.DATE },
  enabled: { type: Sequelize.BOOLEAN, defaultValue: true },
  createdAt: { field: 'created_at', type: Sequelize.DATE },
  updatedAt: { field: 'updated_at', type: Sequelize.DATE }
}, {
  timestamps: true,
  tableName: 'answers'
});
```

## Engagement Models

### 5. Votes Model
```javascript
// src/models/voteModel.js
const voteModel = db.define('votes', {
  id: { type: Sequelize.BIGINT, primaryKey: true, autoIncrement: true },
  
  userId: { field: 'user_id', type: Sequelize.BIGINT, references: { model: 'users', key: 'id' } },
  questionId: { field: 'question_id', type: Sequelize.BIGINT, references: { model: 'questions', key: 'id' } },
  answerId: { field: 'answer_id', type: Sequelize.BIGINT, references: { model: 'answers', key: 'id' } },
  
  value: { type: Sequelize.INTEGER }, // 1 for upvote, -1 for downvote
  
  enabled: { type: Sequelize.BOOLEAN, defaultValue: true },
  createdAt: { field: 'created_at', type: Sequelize.DATE }
}, {
  timestamps: true,
  updatedAt: false,
  tableName: 'votes'
});
```

### 6. Likes Model
```javascript
// src/models/likeModel.js
const likeModel = db.define('likes', {
  id: { type: Sequelize.BIGINT, primaryKey: true, autoIncrement: true },
  
  userId: { field: 'user_id', type: Sequelize.BIGINT, references: { model: 'users', key: 'id' } },
  answerId: { field: 'answer_id', type: Sequelize.BIGINT, references: { model: 'answers', key: 'id' } },
  
  enabled: { type: Sequelize.BOOLEAN, defaultValue: true },
  createdAt: { field: 'created_at', type: Sequelize.DATE }
}, {
  timestamps: true,
  updatedAt: false,
  tableName: 'likes'
});
```

### 7. Comments Model
```javascript
// src/models/commentModel.js
const commentModel = db.define('comments', {
  id: { type: Sequelize.BIGINT, primaryKey: true, autoIncrement: true },
  
  userId: { field: 'user_id', type: Sequelize.BIGINT, references: { model: 'users', key: 'id' } },
  answerId: { field: 'answer_id', type: Sequelize.BIGINT, references: { model: 'answers', key: 'id' } },
  
  text: { type: Sequelize.TEXT, allowNull: false },
  
  enabled: { type: Sequelize.BOOLEAN, defaultValue: true },
  createdAt: { field: 'created_at', type: Sequelize.DATE },
  updatedAt: { field: 'updated_at', type: Sequelize.DATE }
}, {
  timestamps: true,
  tableName: 'comments'
});
```

## System Models

### 8. Sessions Model
```javascript
// src/models/sessionModel.js
const sessionModel = db.define('sessions', {
  id: { type: Sequelize.BIGINT, primaryKey: true, autoIncrement: true },
  
  userId: { field: 'user_id', type: Sequelize.BIGINT, references: { model: 'users', key: 'id' } },
  token: { type: Sequelize.STRING, allowNull: false },
  
  enabled: { type: Sequelize.BOOLEAN, defaultValue: true },
  createdAt: { field: 'created_at', type: Sequelize.DATE },
  updatedAt: { field: 'updated_at', type: Sequelize.DATE }
}, {
  timestamps: true,
  tableName: 'sessions'
});
```

### 9. Statistics Models
```javascript
// src/models/statModel.js
const statModel = db.define('stats', {
  id: { type: Sequelize.BIGINT, primaryKey: true, autoIncrement: true },
  
  clientId: { field: 'client_id', type: Sequelize.BIGINT },
  type: { type: Sequelize.STRING, allowNull: false },
  category: { type: Sequelize.STRING },
  value: { type: Sequelize.FLOAT, allowNull: false },
  metadata: { type: Sequelize.JSONB },
  date: { type: Sequelize.DATEONLY, allowNull: false },
  
  createdAt: { field: 'created_at', type: Sequelize.DATE }
}, {
  timestamps: true,
  updatedAt: false,
  tableName: 'stats'
});

// src/models/statEventModel.js
const statEventModel = db.define('stat_events', {
  id: { type: Sequelize.BIGINT, primaryKey: true, autoIncrement: true },
  
  type: { type: Sequelize.STRING, allowNull: false },
  action: { type: Sequelize.STRING, allowNull: false },
  entityType: { field: 'entity_type', type: Sequelize.STRING },
  entityId: { field: 'entity_id', type: Sequelize.STRING },
  userId: { field: 'user_id', type: Sequelize.BIGINT },
  clientId: { field: 'client_id', type: Sequelize.BIGINT },
  sessionId: { field: 'session_id', type: Sequelize.STRING },
  metadata: { type: Sequelize.JSONB },
  timestamp: { type: Sequelize.DATE, allowNull: false }
}, {
  timestamps: false,
  tableName: 'stat_events'
});
```

## NGP VAN Models

### 10. NGP VAN Integration Models
```javascript
// src/models/ngpVanPeopleModel.js
const ngpVanPeopleModel = db.define('ngp_van_people', {
  id: { type: Sequelize.BIGINT, primaryKey: true, autoIncrement: true },
  
  ngpVanId: { field: 'ngp_van_id', type: Sequelize.INTEGER, allowNull: false },
  clientId: { field: 'client_id', type: Sequelize.BIGINT, references: { model: 'clients', key: 'id' } },
  
  firstName: { field: 'first_name', type: Sequelize.STRING },
  lastName: { field: 'last_name', type: Sequelize.STRING },
  email: encrypted.vault('email'),
  phone: encrypted.vault('phone'),
  address: encrypted.vault('address'),
  
  activistCodes: { field: 'activist_codes', type: Sequelize.JSONB },
  customFields: { field: 'custom_fields', type: Sequelize.JSONB },
  
  enabled: { type: Sequelize.BOOLEAN, defaultValue: true },
  createdAt: { field: 'created_at', type: Sequelize.DATE },
  updatedAt: { field: 'updated_at', type: Sequelize.DATE }
}, {
  timestamps: true,
  tableName: 'ngp_van_people'
});
```

## Model Relationships

### Association Configuration
```javascript
// src/models/index.js - Model associations
clientModel.hasMany(userModel, { as: 'users' });
clientModel.hasMany(questionModel, { as: 'questions' });
clientModel.hasMany(answerModel, { as: 'answers' });

userModel.belongsTo(clientModel, { as: 'client' });
userModel.hasMany(sessionModel, { as: 'sessions' });
userModel.hasMany(questionModel, { as: 'questions' });
userModel.hasMany(answerModel, { as: 'answers' });
userModel.hasMany(voteModel, { as: 'votes' });
userModel.hasMany(likeModel, { as: 'likes' });
userModel.hasMany(commentModel, { as: 'comments' });

questionModel.belongsTo(userModel, { as: 'user' });
questionModel.belongsTo(clientModel, { as: 'client' });
questionModel.hasMany(answerModel, { as: 'answers' });
questionModel.hasMany(voteModel, { as: 'votes' });

answerModel.belongsTo(userModel, { as: 'user' });
answerModel.belongsTo(clientModel, { as: 'client' });
answerModel.belongsTo(questionModel, { as: 'question' });
answerModel.hasMany(voteModel, { as: 'votes' });
answerModel.hasMany(likeModel, { as: 'likes' });
answerModel.hasMany(commentModel, { as: 'comments' });

voteModel.belongsTo(userModel, { as: 'user' });
voteModel.belongsTo(questionModel, { as: 'question' });
voteModel.belongsTo(answerModel, { as: 'answer' });

likeModel.belongsTo(userModel, { as: 'user' });
likeModel.belongsTo(answerModel, { as: 'answer' });

commentModel.belongsTo(userModel, { as: 'user' });
commentModel.belongsTo(answerModel, { as: 'answer' });
```

## Data Encryption

### Encrypted Fields
```javascript
// src/utils/encryption.js
import SequelizeEncrypted from 'sequelize-encrypted';

const encrypted = SequelizeEncrypted(Sequelize, process.env.ENCRYPTION_KEY);

// Usage in models
email: encrypted.vault('email'),
phone: encrypted.vault('phone'),
ngpVanApiKey: encrypted.vault('ngp_van_api_key')
```

### Encryption Configuration
```javascript
// Environment variables for encryption
ENCRYPTION_KEY=your_32_character_encryption_key
ENCRYPTION_ALGORITHM=aes-256-cbc
```

## Model Utilities

### Global Model Methods
```javascript
// src/models/_globals.js
export const modelSuper = (fields) => {
  return {
    id: { type: Sequelize.BIGINT, primaryKey: true, autoIncrement: true },
    enabled: { type: Sequelize.BOOLEAN, defaultValue: true },
    createdAt: { field: 'created_at', type: Sequelize.DATE },
    updatedAt: { field: 'updated_at', type: Sequelize.DATE },
    ...fields
  };
};
```

### Custom Model Methods
```javascript
// src/utils/modelUtil.js
export const injectCustomModelMethods = (model, errorClass) => {
  // Add custom findByIdOrFail method
  model.findByIdOrFail = async (id) => {
    const instance = await model.findByPk(id);
    if (!instance) {
      throw new errorClass.NotFound(`${model.name} not found with id: ${id}`);
    }
    return instance;
  };

  // Add custom soft delete method
  model.softDelete = async (id) => {
    return await model.update({ enabled: false }, { where: { id } });
  };

  // Add custom restore method
  model.restore = async (id) => {
    return await model.update({ enabled: true }, { where: { id } });
  };
};
```

## Database Migrations

### Migration Structure
```javascript
// Example migration
export const up = async (queryInterface, Sequelize) => {
  await queryInterface.createTable('questions', {
    id: {
      type: Sequelize.BIGINT,
      primaryKey: true,
      autoIncrement: true
    },
    text: {
      type: Sequelize.STRING(2000),
      allowNull: false
    },
    // ... other fields
    created_at: {
      type: Sequelize.DATE,
      allowNull: false
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: false
    }
  });

  // Add indexes
  await queryInterface.addIndex('questions', ['client_id']);
  await queryInterface.addIndex('questions', ['user_id']);
  await queryInterface.addIndex('questions', ['category']);
};

export const down = async (queryInterface, Sequelize) => {
  await queryInterface.dropTable('questions');
};
```

## Performance Optimization

### Database Indexes
```sql
-- Key indexes for performance
CREATE INDEX idx_questions_client_id ON questions(client_id);
CREATE INDEX idx_questions_category ON questions(category);
CREATE INDEX idx_answers_question_id ON answers(question_id);
CREATE INDEX idx_votes_user_answer ON votes(user_id, answer_id);
CREATE INDEX idx_stat_events_timestamp ON stat_events(timestamp);
CREATE INDEX idx_stat_events_client_type ON stat_events(client_id, type);
```

### Query Optimization
```javascript
// Optimized queries with proper includes
const getQuestionsWithAnswers = async (clientId) => {
  return await questionModel.findAll({
    where: { clientId, enabled: true },
    include: [
      {
        model: answerModel,
        as: 'answers',
        where: { enabled: true },
        required: false,
        include: [
          { model: userModel, as: 'user', attributes: ['firstName', 'lastName'] }
        ]
      },
      { model: userModel, as: 'user', attributes: ['firstName', 'lastName'] }
    ],
    order: [['createdAt', 'DESC']]
  });
};
```

## Related Features
- [Authentication & Sessions](./01-authentication-sessions.md)
- [Users & Clients](./02-users-clients.md)
- [Questions](./03-questions.md)
- [Answers](./04-answers.md)
- [NGP VAN Integration](./07-ngp-van-integration.md)

## Dependencies
- `sequelize` - ORM framework
- `sequelize-encrypted` - Field encryption
- `pg` - PostgreSQL driver
- Database migration tools
