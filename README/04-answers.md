# Answers

## Overview
The answers system allows authorized users to respond to questions with video content. Answers include video processing, transcription, translation, approval workflows, and engagement features like voting and comments.

## Key Components

### Files
- `src/models/answerModel.js` - Answer database model
- `src/services/answerService.js` - Answer business logic
- `src/routes/answerRoute.js` - Answer API endpoints
- `src/validations/answerValidation.js` - Answer input validation
- `src/schedules/answerSchedule.js` - Automated answer processing

### Database Model
- `id` - Primary key
- `clientId` - Foreign key to clients table
- `userId` - Foreign key to users table (responder)
- `questionId` - Foreign key to questions table
- `videoUrl` - Main video file URL
- `videoUrls` - JSON object with multiple video formats
- `imageUrl` - Video thumbnail URL
- `videoDuration` - Video length in seconds
- `subtitles` - Manual subtitle text
- `subtitlesSpeed` - Subtitle display speed
- `subtitlesTranslations` - Translated subtitles (JSON)
- `transcription` - AWS transcription data (JSON)
- `transcriptionTranslation` - Translated transcriptions (JSON)
- `showTranscribedSubtitles` - Whether to show auto-generated subtitles
- `isDraft` - Draft status
- `isApproved` - Admin approval status
- `isDenied` - Admin denial status
- `isPinned` - Featured/pinned status
- `isShared` - Public sharing status
- `endDate` - Answer expiration date
- `enabled` - Answer visibility status

## Answer Lifecycle

### 1. Answer Creation
```javascript
// POST /answers
{
  "questionId": "123",
  "videoUrl": "https://files.repd.us/videos/answer-123.mp4",
  "imageUrl": "https://files.repd.us/thumbnails/answer-123.jpg",
  "clientId": 1,
  "isDraft": false
}
```

### 2. Video Processing
- Automatic thumbnail generation
- Multiple video format creation (different resolutions)
- Duration calculation
- File optimization for web delivery

### 3. Transcription Processing
- AWS Transcribe integration for automatic transcription
- Subtitle generation with timing
- Translation to multiple languages
- Manual subtitle override capability

### 4. Approval Workflow
- Answers require admin approval before publication
- Draft mode for work-in-progress answers
- Quality control and content moderation

## Video Management

### Video Formats
```javascript
// videoUrls structure
{
  "original": "https://files.repd.us/videos/answer-123.mp4",
  "720p": "https://files.repd.us/videos/answer-123-720p.mp4",
  "480p": "https://files.repd.us/videos/answer-123-480p.mp4",
  "360p": "https://files.repd.us/videos/answer-123-360p.mp4"
}
```

### Thumbnail Generation
- Automatic thumbnail extraction from video
- Multiple thumbnail sizes
- Custom thumbnail upload capability

### Video Duration
- Automatic duration calculation
- Used for UI progress bars and time displays
- Stored in seconds

## Transcription System

### AWS Transcribe Integration
```javascript
// transcription field structure
{
  "jobName": "transcribe-job-123",
  "status": "COMPLETED",
  "transcript": "Hello, thank you for your question about...",
  "segments": [
    {
      "start": 0.0,
      "end": 2.5,
      "text": "Hello, thank you for your question"
    }
  ]
}
```

### Subtitle Features
- Manual subtitle input with custom speed
- Auto-generated subtitles from transcription
- Multi-language subtitle support
- Subtitle timing synchronization

### Translation Support
- Automatic translation of transcriptions
- Multiple language support
- Preserves timing information for subtitles

## Engagement Features

### Voting System
- Users can vote on answer quality
- Vote counts displayed publicly
- Vote tracking per user to prevent duplicates

### Comments
- Users can comment on answers
- Threaded comment discussions
- Comment moderation capabilities

### Likes
- Simple like/unlike functionality
- Like counts displayed
- User-specific like tracking

## API Endpoints

### Answer Management
- `POST /answers` - Create new answer
- `GET /answers` - List answers (with filters)
- `GET /answers/:id` - Get specific answer
- `PUT /answers/:id` - Update answer
- `DELETE /answers/:id` - Delete answer

### Admin Operations
- `PUT /answers/:id/approve` - Approve answer
- `PUT /answers/:id/deny` - Deny answer
- `PUT /answers/:id/pin` - Pin/unpin answer
- `PUT /answers/:id/share` - Share answer publicly

### Engagement Endpoints
- `POST /answers/:id/vote` - Vote on answer
- `POST /answers/:id/like` - Like/unlike answer
- `POST /answers/:id/comment` - Add comment

## Answer Filtering

### Filter Options
```javascript
// GET /answers with query parameters
{
  "questionId": "123",
  "clientId": 1,
  "isApproved": true,
  "isPinned": false,
  "userId": "456",
  "page": 1,
  "limit": 10,
  "sortBy": "createdAt",
  "sortOrder": "desc"
}
```

### Search Capabilities
- Full-text search in transcriptions
- Category-based filtering
- Date range filtering
- User-specific filtering

## Draft System

### Draft Features
- Save work-in-progress answers
- Private draft visibility
- Draft-to-published workflow
- Auto-save functionality

### Draft Management
```javascript
// Creating a draft
const draft = await answerService.create({
  questionId: "123",
  videoUrl: "temp-video-url",
  isDraft: true,
  clientId: 1
});

// Publishing a draft
await answerService.publish(draft.id);
```

## Scheduled Processing

### Automated Tasks
- Transcription job monitoring
- Video processing completion
- Thumbnail generation
- Translation processing

### Background Jobs
```javascript
// Answer processing schedule
import cron from 'node-cron';

// Check for completed transcription jobs every 5 minutes
cron.schedule('*/5 * * * *', async () => {
  await processCompletedTranscriptions();
});
```

## Usage Examples

### Creating an Answer
```javascript
import answerService from '../services/answerService.js';

const answer = await answerService.create({
  questionId: "123",
  videoUrl: "https://files.repd.us/videos/answer.mp4",
  imageUrl: "https://files.repd.us/thumbnails/answer.jpg",
  clientId: 1,
  userId: 456,
  subtitles: "Thank you for your question about transportation..."
});
```

### Getting Answers with Engagement Data
```javascript
const answers = await answerService.getWithEngagement({
  questionId: "123",
  userId: "current-user-id", // For like/vote status
  includeComments: true
});
```

## Error Handling

### Common Errors
- `404 Not Found` - Answer or question not found
- `403 Forbidden` - Insufficient permissions
- `422 Validation Error` - Invalid video format or missing data
- `409 Conflict` - Duplicate answer for question

## Related Features
- [Questions](./03-questions.md)
- [Transcription](./10-transcription.md)
- [File Management](./09-file-management.md)
- [Voting & Engagement](./05-voting-engagement.md)
- [AI Integration](./06-ai-integration.md)

## Dependencies
- `sequelize` - Database ORM
- `aws-sdk` - AWS services integration
- `ffmpeg` - Video processing
- `sharp` - Image processing
- `node-cron` - Scheduled tasks
