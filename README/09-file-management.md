# File Management

## Overview
The file management system handles video uploads, image processing, thumbnail generation, and AWS S3 integration. It supports multiple file formats, automatic optimization, and secure file storage with CDN delivery.

## Key Components

### Files
- `src/services/fileService.js` - File processing business logic
- `src/routes/fileRoute.js` - File upload API endpoints
- `src/global/aws.js` - AWS S3 configuration
- `src/utils/` - File processing utilities
- `scripts/js/regenerate-thumbnails.js` - Thumbnail regeneration script
- `scripts/js/get-duration-for-videos.js` - Video duration calculation

### File Types Supported

#### Video Files
- **MP4** - Primary video format
- **MOV** - Apple video format
- **AVI** - Windows video format
- **WebM** - Web-optimized format

#### Image Files
- **JPEG/JPG** - Standard image format
- **PNG** - Lossless image format
- **WebP** - Modern web format
- **GIF** - Animated images

#### Document Files
- **PDF** - Document format
- **DOC/DOCX** - Microsoft Word documents
- **TXT** - Plain text files

## AWS S3 Integration

### S3 Configuration
```javascript
// src/global/aws.js
import AWS from 'aws-sdk';

const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION
});

export const uploadToS3 = async (file, key, bucket) => {
  const params = {
    Bucket: bucket,
    Key: key,
    Body: file.buffer,
    ContentType: file.mimetype,
    ACL: 'public-read'
  };

  const result = await s3.upload(params).promise();
  return result.Location;
};
```

### S3 Buckets
- **repd-videos** - Video file storage
- **repd-images** - Image and thumbnail storage
- **repd-documents** - Document storage
- **repd-temp** - Temporary file storage

## File Upload Process

### 1. File Validation
```javascript
// Validate file type and size
export const validateFile = (file, type) => {
  const maxSizes = {
    video: 500 * 1024 * 1024, // 500MB
    image: 10 * 1024 * 1024,  // 10MB
    document: 50 * 1024 * 1024 // 50MB
  };

  const allowedTypes = {
    video: ['video/mp4', 'video/mov', 'video/avi'],
    image: ['image/jpeg', 'image/png', 'image/webp'],
    document: ['application/pdf', 'application/msword']
  };

  if (file.size > maxSizes[type]) {
    throw new Error(`File too large. Maximum size: ${maxSizes[type]} bytes`);
  }

  if (!allowedTypes[type].includes(file.mimetype)) {
    throw new Error(`Invalid file type. Allowed: ${allowedTypes[type].join(', ')}`);
  }
};
```

### 2. File Processing
```javascript
// Process uploaded file
export const processFile = async (file, type, clientId) => {
  // Validate file
  validateFile(file, type);

  // Generate unique filename
  const filename = generateUniqueFilename(file.originalname);
  
  // Upload to S3
  const s3Key = `${clientId}/${type}s/${filename}`;
  const fileUrl = await uploadToS3(file, s3Key, process.env.S3_BUCKET);

  // Process based on type
  if (type === 'video') {
    return await processVideo(file, fileUrl, clientId);
  } else if (type === 'image') {
    return await processImage(file, fileUrl, clientId);
  }

  return { url: fileUrl, filename };
};
```

## Video Processing

### Video Optimization
```javascript
// Generate multiple video formats
export const processVideo = async (file, originalUrl, clientId) => {
  const videoId = generateVideoId();
  const formats = ['720p', '480p', '360p'];
  const videoUrls = { original: originalUrl };

  // Generate optimized versions
  for (const format of formats) {
    const optimizedUrl = await generateVideoFormat(file, format, videoId);
    videoUrls[format] = optimizedUrl;
  }

  // Generate thumbnail
  const thumbnailUrl = await generateVideoThumbnail(file, videoId);

  // Calculate duration
  const duration = await getVideoDuration(file);

  return {
    videoUrls,
    thumbnailUrl,
    duration,
    videoId
  };
};
```

### Thumbnail Generation
```javascript
// Generate video thumbnail
export const generateVideoThumbnail = async (videoFile, videoId) => {
  const ffmpeg = require('fluent-ffmpeg');
  
  return new Promise((resolve, reject) => {
    ffmpeg(videoFile.path)
      .screenshots({
        timestamps: ['50%'],
        filename: `${videoId}-thumbnail.jpg`,
        folder: '/tmp/thumbnails',
        size: '640x360'
      })
      .on('end', async () => {
        const thumbnailPath = `/tmp/thumbnails/${videoId}-thumbnail.jpg`;
        const thumbnailUrl = await uploadToS3(
          { path: thumbnailPath },
          `thumbnails/${videoId}.jpg`,
          process.env.S3_BUCKET
        );
        resolve(thumbnailUrl);
      })
      .on('error', reject);
  });
};
```

### Video Duration Calculation
```javascript
// Calculate video duration
export const getVideoDuration = async (videoFile) => {
  const ffprobe = require('ffprobe');
  
  try {
    const info = await ffprobe(videoFile.path);
    return Math.round(info.streams[0].duration);
  } catch (error) {
    logger.error('Failed to get video duration', error);
    return null;
  }
};
```

## Image Processing

### Image Optimization
```javascript
// Process and optimize images
export const processImage = async (file, originalUrl, clientId) => {
  const sharp = require('sharp');
  const imageId = generateImageId();
  
  // Generate multiple sizes
  const sizes = {
    thumbnail: { width: 150, height: 150 },
    medium: { width: 500, height: 500 },
    large: { width: 1200, height: 1200 }
  };

  const imageUrls = { original: originalUrl };

  for (const [sizeName, dimensions] of Object.entries(sizes)) {
    const resizedBuffer = await sharp(file.buffer)
      .resize(dimensions.width, dimensions.height, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .jpeg({ quality: 85 })
      .toBuffer();

    const resizedUrl = await uploadToS3(
      { buffer: resizedBuffer, mimetype: 'image/jpeg' },
      `images/${imageId}-${sizeName}.jpg`,
      process.env.S3_BUCKET
    );

    imageUrls[sizeName] = resizedUrl;
  }

  return { imageUrls, imageId };
};
```

## API Endpoints

### File Upload Endpoints
- `POST /files/videos` - Upload video file
- `POST /files/images` - Upload image file
- `POST /files/documents` - Upload document file
- `GET /files/:id` - Get file information
- `DELETE /files/:id` - Delete file

### File Management
- `GET /files/list` - List uploaded files
- `POST /files/regenerate-thumbnail` - Regenerate video thumbnail
- `POST /files/optimize` - Re-optimize existing file

### Upload Configuration
```javascript
// Multer configuration for file uploads
import multer from 'multer';

const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 500 * 1024 * 1024, // 500MB
    files: 1
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|mp4|mov|avi|pdf/;
    const extname = allowedTypes.test(
      path.extname(file.originalname).toLowerCase()
    );
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  }
});
```

## File Security

### Access Control
```javascript
// Secure file access
export const generateSignedUrl = async (s3Key, expiresIn = 3600) => {
  const params = {
    Bucket: process.env.S3_BUCKET,
    Key: s3Key,
    Expires: expiresIn
  };

  return s3.getSignedUrl('getObject', params);
};
```

### File Validation
```javascript
// Validate file content
export const validateFileContent = async (file) => {
  const fileType = await import('file-type');
  const type = await fileType.fromBuffer(file.buffer);
  
  if (!type) {
    throw new Error('Unable to determine file type');
  }

  // Check if detected type matches declared type
  if (type.mime !== file.mimetype) {
    throw new Error('File type mismatch');
  }

  return type;
};
```

## File Cleanup

### Temporary File Cleanup
```javascript
// Clean up temporary files
export const cleanupTempFiles = async () => {
  const fs = require('fs').promises;
  const tempDir = '/tmp/uploads';
  
  try {
    const files = await fs.readdir(tempDir);
    const now = Date.now();
    
    for (const file of files) {
      const filePath = path.join(tempDir, file);
      const stats = await fs.stat(filePath);
      
      // Delete files older than 1 hour
      if (now - stats.mtime.getTime() > 3600000) {
        await fs.unlink(filePath);
      }
    }
  } catch (error) {
    logger.error('Temp file cleanup failed', error);
  }
};
```

### S3 Lifecycle Management
```javascript
// S3 lifecycle configuration
const lifecycleConfig = {
  Rules: [
    {
      ID: 'DeleteTempFiles',
      Status: 'Enabled',
      Filter: { Prefix: 'temp/' },
      Expiration: { Days: 1 }
    },
    {
      ID: 'ArchiveOldFiles',
      Status: 'Enabled',
      Filter: { Prefix: 'archive/' },
      Transitions: [
        {
          Days: 30,
          StorageClass: 'STANDARD_IA'
        },
        {
          Days: 90,
          StorageClass: 'GLACIER'
        }
      ]
    }
  ]
};
```

## Usage Examples

### Uploading a Video
```javascript
import fileService from '../services/fileService.js';

// Handle video upload
const uploadVideo = async (req, res) => {
  try {
    const file = req.file;
    const clientId = req.body.clientId;
    
    const result = await fileService.processFile(file, 'video', clientId);
    
    res.json({
      success: true,
      data: {
        videoUrls: result.videoUrls,
        thumbnailUrl: result.thumbnailUrl,
        duration: result.duration
      }
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};
```

### Generating Thumbnails
```javascript
// Regenerate thumbnail for existing video
const regenerateThumbnail = async (videoId) => {
  const video = await answerModel.findByPk(videoId);
  
  if (!video.videoUrl) {
    throw new Error('Video URL not found');
  }

  const thumbnailUrl = await generateVideoThumbnail(
    { path: video.videoUrl },
    videoId
  );

  await video.update({ imageUrl: thumbnailUrl });
  
  return thumbnailUrl;
};
```

## Error Handling

### Common Errors
- `413 Payload Too Large` - File size exceeds limit
- `415 Unsupported Media Type` - Invalid file type
- `500 Internal Server Error` - Processing failure
- `503 Service Unavailable` - S3 service issues

### Error Recovery
```javascript
// Retry failed uploads
export const retryUpload = async (file, retryCount = 0) => {
  const maxRetries = 3;
  
  try {
    return await uploadToS3(file);
  } catch (error) {
    if (retryCount < maxRetries) {
      await delay(1000 * Math.pow(2, retryCount)); // Exponential backoff
      return retryUpload(file, retryCount + 1);
    }
    throw error;
  }
};
```

## Performance Optimization

### CDN Integration
- CloudFront distribution for fast delivery
- Edge caching for frequently accessed files
- Automatic compression and optimization

### Lazy Loading
- Progressive image loading
- Video thumbnail previews
- On-demand format generation

## Related Features
- [Answers](./04-answers.md)
- [Transcription](./10-transcription.md)
- [Statistics & Analytics](./11-statistics-analytics.md)

## Dependencies
- `multer` - File upload middleware
- `aws-sdk` - AWS S3 integration
- `sharp` - Image processing
- `fluent-ffmpeg` - Video processing
- `file-type` - File type detection
