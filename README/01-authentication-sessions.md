# Authentication & Sessions

## Overview
The authentication and session management system handles user authentication, session creation, and access control throughout the Repd API. It supports multiple access levels and integrates with client-specific configurations.

## Key Components

### Files
- `src/global/session.js` - Session configuration and middleware
- `src/services/sessionService.js` - Session business logic
- `src/routes/sessionRoute.js` - Session API endpoints
- `src/models/sessionModel.js` - Session database model
- `src/validations/sessionValidation.js` - Session input validation

### Database Models
- **sessions** - Stores user session data
  - `id` - Primary key
  - `userId` - Foreign key to users table
  - `token` - Session authentication token
  - `createdAt` - Session creation timestamp
  - `updatedAt` - Last session update
  - `enabled` - Session status flag

## Authentication Flow

### 1. Session Creation
```javascript
// POST /sessions
{
  "email": "<EMAIL>",
  "password": "userpassword",
  "clientId": 123
}
```

### 2. Token-Based Authentication
- Sessions use JWT-like tokens for authentication
- Tokens are passed via `Authorization` header
- Middleware validates tokens on protected routes

### 3. Access Levels
The system supports multiple access levels:
- **Public** - No authentication required
- **User** - Basic authenticated user
- **Admin** - Administrative privileges
- **Client Admin** - Client-specific admin rights

## API Endpoints

### Session Management
- `POST /sessions` - Create new session (login)
- `GET /sessions/current` - Get current session info
- `DELETE /sessions` - Destroy session (logout)
- `PUT /sessions` - Update session data

### Authentication Middleware
```javascript
// Security constraints example
{
  regex: /^\/admin/,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  accessLevels: ['admin']
}
```

## Configuration

### Environment Variables
```bash
SESSION_SECRET=your_session_secret_key
SESSION_TIMEOUT=3600000  # 1 hour in milliseconds
```

### Session Configuration
```javascript
// src/global/session.js
export default {
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    maxAge: parseInt(process.env.SESSION_TIMEOUT)
  }
}
```

## Security Features

### 1. CSRF Protection
- Uses `csrf-sync` middleware for CSRF protection
- Tokens validated on state-changing operations

### 2. Helmet Security
- Security headers via Helmet middleware
- XSS protection, content type sniffing prevention

### 3. Cookie Security
- Secure cookies in production
- HttpOnly flags to prevent XSS
- SameSite protection

## Usage Examples

### Creating a Session
```javascript
import sessionService from '../services/sessionService.js';

const session = await sessionService.create({
  email: '<EMAIL>',
  password: 'password123',
  clientId: 1
});
```

### Validating Authentication
```javascript
// Middleware automatically validates tokens
// Access user data via request.auth
app.get('/protected', (req, res) => {
  const { user, session } = req.auth;
  res.json({ user: user.email, sessionId: session.id });
});
```

## Error Handling

### Common Errors
- `401 Unauthorized` - Invalid or expired token
- `403 Forbidden` - Insufficient access level
- `422 Validation Error` - Invalid login credentials

### Error Responses
```javascript
{
  "error": "Authentication failed",
  "message": "Invalid email or password",
  "code": "AUTH_FAILED"
}
```

## Related Features
- [Users & Clients](./02-users-clients.md)
- [API Routes](./15-api-routes.md)
- [Database Models](./14-database-models.md)

## Dependencies
- `express-session` - Session middleware
- `csrf-sync` - CSRF protection
- `helmet` - Security headers
- `cookie-parser` - Cookie parsing
- `celebrate` - Request validation
