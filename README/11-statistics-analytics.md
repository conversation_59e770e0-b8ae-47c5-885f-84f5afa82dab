# Statistics & Analytics

## Overview
The statistics and analytics system provides comprehensive tracking and reporting for user engagement, content performance, and system metrics. It integrates with Google Analytics, internal tracking, and SendGrid analytics to provide detailed insights.

## Key Components

### Files
- `src/services/statsService.js` - Statistics business logic
- `src/routes/statsRoute.js` - Analytics API endpoints
- `src/models/statModel.js` - Statistics database model
- `src/models/statEventModel.js` - Event tracking model
- `src/schedules/googleStatsSchedule.js` - Google Analytics sync
- `src/schedules/internalStatsSchedule.js` - Internal metrics processing
- `src/schedules/sendgridStatsSchedule.js` - Email analytics sync
- `src/global/ga.js` - Google Analytics configuration
- `src/helpers/statsQueries.js` - Analytics query helpers

### Database Models

#### Statistics Model
- `id` - Primary key
- `clientId` - Client identifier
- `type` - Statistic type (views, votes, engagement, etc.)
- `category` - Category classification
- `value` - Numeric value
- `metadata` - Additional data (JSON)
- `date` - Date of statistic
- `createdAt` - Record creation timestamp

#### Stat Events Model
- `id` - Primary key
- `type` - Event type (click, view, vote, etc.)
- `action` - Specific action taken
- `entityType` - Type of entity (question, answer, user)
- `entityId` - Entity identifier
- `userId` - User who performed action
- `clientId` - Client context
- `sessionId` - Session identifier
- `metadata` - Event-specific data (JSON)
- `timestamp` - Event occurrence time

## Analytics Integration

### Google Analytics 4
```javascript
// src/global/ga.js
import { GoogleAuth } from 'google-auth-library';
import { google } from 'googleapis';

const auth = new GoogleAuth({
  keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS,
  scopes: ['https://www.googleapis.com/auth/analytics.readonly']
});

export const getGoogleAnalyticsData = async (propertyId, startDate, endDate, metrics, dimensions) => {
  const analyticsData = google.analyticsdata('v1beta');
  
  const response = await analyticsData.properties.runReport({
    property: `properties/${propertyId}`,
    requestBody: {
      dateRanges: [{ startDate, endDate }],
      metrics: metrics.map(metric => ({ name: metric })),
      dimensions: dimensions.map(dimension => ({ name: dimension }))
    },
    auth
  });

  return response.data;
};
```

### Internal Event Tracking
```javascript
// Track user events
export const trackEvent = async (eventData) => {
  const {
    type,
    action,
    entityType,
    entityId,
    userId,
    clientId,
    sessionId,
    metadata = {}
  } = eventData;

  await statEventModel.create({
    type,
    action,
    entityType,
    entityId,
    userId,
    clientId,
    sessionId,
    metadata,
    timestamp: new Date()
  });

  // Also send to Google Analytics if configured
  if (process.env.GA_MEASUREMENT_ID) {
    await sendToGoogleAnalytics(eventData);
  }
};
```

## Key Metrics

### Engagement Metrics
```javascript
// Calculate engagement statistics
export const getEngagementStats = async (clientId, dateRange) => {
  const stats = await statEventModel.findAll({
    where: {
      clientId,
      timestamp: {
        [Op.between]: [dateRange.start, dateRange.end]
      }
    },
    attributes: [
      'action',
      [Sequelize.fn('COUNT', Sequelize.col('id')), 'count'],
      [Sequelize.fn('COUNT', Sequelize.fn('DISTINCT', Sequelize.col('userId'))), 'uniqueUsers']
    ],
    group: ['action']
  });

  return {
    totalEvents: stats.reduce((sum, stat) => sum + parseInt(stat.count), 0),
    uniqueUsers: Math.max(...stats.map(stat => parseInt(stat.uniqueUsers))),
    eventBreakdown: stats.reduce((acc, stat) => {
      acc[stat.action] = parseInt(stat.count);
      return acc;
    }, {})
  };
};
```

### Content Performance
```javascript
// Get question and answer performance metrics
export const getContentPerformance = async (clientId, period = '30d') => {
  const endDate = new Date();
  const startDate = new Date(endDate.getTime() - (parseInt(period) * 24 * 60 * 60 * 1000));

  const questionStats = await statEventModel.findAll({
    where: {
      clientId,
      entityType: 'question',
      timestamp: { [Op.between]: [startDate, endDate] }
    },
    attributes: [
      'entityId',
      'action',
      [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
    ],
    group: ['entityId', 'action']
  });

  const answerStats = await statEventModel.findAll({
    where: {
      clientId,
      entityType: 'answer',
      timestamp: { [Op.between]: [startDate, endDate] }
    },
    attributes: [
      'entityId',
      'action',
      [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
    ],
    group: ['entityId', 'action']
  });

  return {
    questions: processContentStats(questionStats),
    answers: processContentStats(answerStats)
  };
};
```

### User Analytics
```javascript
// Get user behavior analytics
export const getUserAnalytics = async (clientId, userId = null) => {
  const whereClause = { clientId };
  if (userId) whereClause.userId = userId;

  const userStats = await statEventModel.findAll({
    where: whereClause,
    attributes: [
      'userId',
      'action',
      [Sequelize.fn('COUNT', Sequelize.col('id')), 'actionCount'],
      [Sequelize.fn('MIN', Sequelize.col('timestamp')), 'firstAction'],
      [Sequelize.fn('MAX', Sequelize.col('timestamp')), 'lastAction']
    ],
    group: ['userId', 'action']
  });

  return processUserStats(userStats);
};
```

## Real-time Analytics

### Live Dashboard Data
```javascript
// Get real-time statistics for dashboard
export const getRealTimeStats = async (clientId) => {
  const now = new Date();
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

  const [hourlyStats, dailyStats, activeUsers] = await Promise.all([
    getEventCounts(clientId, oneHourAgo, now),
    getEventCounts(clientId, oneDayAgo, now),
    getActiveUsers(clientId, oneHourAgo, now)
  ]);

  return {
    lastHour: hourlyStats,
    last24Hours: dailyStats,
    activeUsers,
    timestamp: now
  };
};
```

### Event Streaming
```javascript
// Stream events for real-time updates
export const streamEvents = (clientId, callback) => {
  const eventEmitter = new EventEmitter();
  
  // Listen for new events
  statEventModel.addHook('afterCreate', (event) => {
    if (event.clientId === clientId) {
      eventEmitter.emit('newEvent', event);
    }
  });

  eventEmitter.on('newEvent', callback);
  
  return eventEmitter;
};
```

## Scheduled Analytics

### Daily Statistics Processing
```javascript
// src/schedules/internalStatsSchedule.js
import cron from 'node-cron';

// Process daily statistics at midnight
cron.schedule('0 0 * * *', async () => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  
  const clients = await clientModel.findAll({ where: { enabled: true } });
  
  for (const client of clients) {
    await processDailyStats(client.id, yesterday);
  }
});

const processDailyStats = async (clientId, date) => {
  const startOfDay = new Date(date.setHours(0, 0, 0, 0));
  const endOfDay = new Date(date.setHours(23, 59, 59, 999));

  // Calculate daily metrics
  const dailyMetrics = await calculateDailyMetrics(clientId, startOfDay, endOfDay);
  
  // Store aggregated statistics
  for (const [type, value] of Object.entries(dailyMetrics)) {
    await statModel.create({
      clientId,
      type,
      value,
      date: startOfDay,
      metadata: { period: 'daily' }
    });
  }
};
```

### Google Analytics Sync
```javascript
// src/schedules/googleStatsSchedule.js
cron.schedule('0 1 * * *', async () => {
  const clients = await clientModel.findAll({
    where: { 
      enabled: true,
      gaPropertyId: { [Op.not]: null }
    }
  });

  for (const client of clients) {
    await syncGoogleAnalytics(client);
  }
});

const syncGoogleAnalytics = async (client) => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  
  const dateString = yesterday.toISOString().split('T')[0];
  
  const gaData = await getGoogleAnalyticsData(
    client.gaPropertyId,
    dateString,
    dateString,
    ['activeUsers', 'sessions', 'pageviews'],
    ['pagePath', 'deviceCategory']
  );

  // Process and store GA data
  await processGoogleAnalyticsData(client.id, gaData, yesterday);
};
```

## API Endpoints

### Statistics Endpoints
- `GET /stats/dashboard` - Get dashboard statistics
- `GET /stats/engagement` - Get engagement metrics
- `GET /stats/content` - Get content performance
- `GET /stats/users` - Get user analytics
- `GET /stats/realtime` - Get real-time statistics

### Event Tracking
- `POST /stats/event` - Track custom event
- `GET /stats/events` - Get event history
- `POST /stats/batch` - Batch event tracking

### Analytics Reports
- `GET /stats/report/:type` - Generate specific report
- `POST /stats/export` - Export analytics data
- `GET /stats/compare` - Compare time periods

## Custom Reports

### Report Generation
```javascript
// Generate custom analytics report
export const generateReport = async (clientId, reportConfig) => {
  const {
    type,
    dateRange,
    metrics,
    dimensions,
    filters = {}
  } = reportConfig;

  let query = statEventModel.findAll({
    where: {
      clientId,
      timestamp: {
        [Op.between]: [dateRange.start, dateRange.end]
      },
      ...filters
    }
  });

  // Apply grouping and aggregation based on report type
  switch (type) {
    case 'engagement':
      return await generateEngagementReport(query, metrics, dimensions);
    case 'content':
      return await generateContentReport(query, metrics, dimensions);
    case 'user_behavior':
      return await generateUserBehaviorReport(query, metrics, dimensions);
    default:
      throw new Error(`Unknown report type: ${type}`);
  }
};
```

### Data Export
```javascript
// Export analytics data in various formats
export const exportAnalytics = async (clientId, format, reportConfig) => {
  const data = await generateReport(clientId, reportConfig);
  
  switch (format) {
    case 'csv':
      return convertToCSV(data);
    case 'json':
      return JSON.stringify(data, null, 2);
    case 'xlsx':
      return convertToExcel(data);
    default:
      throw new Error(`Unsupported export format: ${format}`);
  }
};
```

## Performance Optimization

### Data Aggregation
```javascript
// Pre-aggregate frequently accessed statistics
export const preAggregateStats = async () => {
  const aggregationPeriods = ['hourly', 'daily', 'weekly', 'monthly'];
  
  for (const period of aggregationPeriods) {
    await aggregateStatsByPeriod(period);
  }
};

const aggregateStatsByPeriod = async (period) => {
  const timeRange = getTimeRangeForPeriod(period);
  
  const aggregatedStats = await statEventModel.findAll({
    where: {
      timestamp: {
        [Op.between]: [timeRange.start, timeRange.end]
      }
    },
    attributes: [
      'clientId',
      'type',
      'action',
      [Sequelize.fn('COUNT', Sequelize.col('id')), 'count'],
      [Sequelize.fn('COUNT', Sequelize.fn('DISTINCT', Sequelize.col('userId'))), 'uniqueUsers']
    ],
    group: ['clientId', 'type', 'action']
  });

  // Store aggregated data
  for (const stat of aggregatedStats) {
    await statModel.upsert({
      clientId: stat.clientId,
      type: `${stat.type}_${stat.action}`,
      value: stat.count,
      metadata: {
        period,
        uniqueUsers: stat.uniqueUsers
      },
      date: timeRange.start
    });
  }
};
```

### Caching Strategy
```javascript
// Cache frequently accessed statistics
import Redis from 'redis';

const redis = Redis.createClient(process.env.REDIS_URL);

export const getCachedStats = async (cacheKey, generator, ttl = 3600) => {
  const cached = await redis.get(cacheKey);
  
  if (cached) {
    return JSON.parse(cached);
  }

  const data = await generator();
  await redis.setex(cacheKey, ttl, JSON.stringify(data));
  
  return data;
};
```

## Usage Examples

### Tracking User Events
```javascript
import statsService from '../services/statsService.js';

// Track question view
await statsService.trackEvent({
  type: 'engagement',
  action: 'view',
  entityType: 'question',
  entityId: questionId,
  userId: currentUser.id,
  clientId: currentUser.clientId,
  sessionId: req.sessionID
});

// Track answer vote
await statsService.trackEvent({
  type: 'engagement',
  action: 'vote',
  entityType: 'answer',
  entityId: answerId,
  userId: currentUser.id,
  clientId: currentUser.clientId,
  metadata: { voteValue: 1 }
});
```

### Getting Dashboard Statistics
```javascript
// Get dashboard data for admin panel
const dashboardStats = await statsService.getDashboardStats(clientId, {
  period: '30d',
  includeRealTime: true
});

/*
Returns:
{
  totalQuestions: 150,
  totalAnswers: 120,
  totalUsers: 500,
  engagementRate: 0.75,
  topCategories: [...],
  recentActivity: [...],
  realTimeUsers: 25
}
*/
```

## Error Handling

### Analytics Errors
```javascript
// Handle analytics service failures gracefully
export const safeTrackEvent = async (eventData) => {
  try {
    await trackEvent(eventData);
  } catch (error) {
    logger.error('Failed to track event', { error, eventData });
    
    // Don't fail the main request if analytics fails
    // Optionally queue for retry
    await queueEventForRetry(eventData);
  }
};
```

## Related Features
- [Users & Clients](./02-users-clients.md)
- [Questions](./03-questions.md)
- [Answers](./04-answers.md)
- [Email System](./08-email-system.md)
- [Scheduling](./12-scheduling.md)

## Dependencies
- `googleapis` - Google Analytics integration
- `sequelize` - Database operations
- `redis` - Caching layer
- `node-cron` - Scheduled processing
- Chart.js or similar for data visualization
