# API Routes

## Overview
The API routes system provides RESTful endpoints for all Repd platform functionality. It includes authentication middleware, input validation, error handling, and comprehensive API documentation. The routes are organized by feature area with consistent patterns and security controls.

## Key Components

### Files
- `src/routes/index.js` - Route coordinator and middleware
- `src/routes/answerRoute.js` - Answer management endpoints
- `src/routes/questionRoute.js` - Question management endpoints
- `src/routes/userRoute.js` - User management endpoints
- `src/routes/clientRoute.js` - Client management endpoints
- `src/routes/sessionRoute.js` - Authentication endpoints
- `src/routes/voteRoute.js` - Voting endpoints
- `src/routes/likesRoute.js` - Like management endpoints
- `src/routes/commentRoute.js` - Comment endpoints
- `src/routes/fileRoute.js` - File upload endpoints
- `src/routes/emailRoute.js` - Email management endpoints
- `src/routes/ngpvanRoute.js` - NGP VAN integration endpoints
- `src/routes/statsRoute.js` - Analytics endpoints
- `src/routes/transcriptRoute.js` - Transcription endpoints
- `src/routes/aiRoute.js` - AI integration endpoints

### Route Architecture
```javascript
// src/routes/index.js - Main router configuration
import express from 'express';

const router = express.Router();
const routes = [];
const securityConstraints = [];

// Dynamic route loading
const routeFiles = fs.readdirSync(routesDir).filter(file => file.endsWith('Route.js'));

for (const file of routeFiles) {
  const routeModule = await import(path.join(routesDir, file));
  const route = routeModule.default || routeModule;
  
  routes.push(route.router);
  securityConstraints.push(...route.securityConstraints);
}

// Apply authentication middleware
router.use(authenticationMiddleware);

// Mount all routes
routes.forEach(route => router.use('/', route));
```

## Authentication & Security

### Security Constraints
```javascript
// Example security constraint configuration
export const securityConstraints = [
  {
    regex: /^\/admin/,
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    accessLevels: ['admin', 'super_admin']
  },
  {
    regex: /^\/questions$/,
    methods: ['POST'],
    accessLevels: ['user', 'admin']
  },
  {
    regex: /^\/answers$/,
    methods: ['POST', 'PUT'],
    accessLevels: ['admin', 'moderator']
  }
];
```

### Authentication Middleware
```javascript
// Authentication logic
router.use((request, response, next) => {
  const scs = securityConstraints.filter(
    sc => sc.methods.includes(request.method) && request.path.match(sc.regex)
  );

  const authIsMandatory = !scs.some(sc => !(sc.accessLevels || []).length > 0);
  const tokenValue = request.headers['authorization'] || request.headers['Authorization'];

  if (scs.length === 0 && !tokenValue) {
    request.auth = { user: {}, session: {} };
    return next();
  }

  // Validate token and set user context
  sessionService.validateToken(tokenValue)
    .then(authData => {
      request.auth = authData;
      next();
    })
    .catch(error => {
      return response.status(401).json({ error: 'Authentication failed' });
    });
});
```

## Core API Endpoints

### 1. Authentication Routes
```javascript
// src/routes/sessionRoute.js
import { celebrate, Joi } from 'celebrate';

const router = express.Router();

// Login
router.post('/sessions', 
  celebrate({
    body: Joi.object({
      email: Joi.string().email().required(),
      password: Joi.string().required(),
      clientId: Joi.number().integer().required()
    })
  }),
  async (req, res, next) => {
    try {
      const session = await sessionService.create(req.body);
      res.json({ message: 'Login successful', data: session });
    } catch (error) {
      next(error);
    }
  }
);

// Get current session
router.get('/sessions/current', async (req, res, next) => {
  try {
    const session = await sessionService.getCurrent(req.auth.session.id);
    res.json({ data: session });
  } catch (error) {
    next(error);
  }
});

// Logout
router.delete('/sessions', async (req, res, next) => {
  try {
    await sessionService.destroy(req.auth.session.id);
    res.json({ message: 'Logout successful' });
  } catch (error) {
    next(error);
  }
});

export default { router, securityConstraints };
```

### 2. Question Routes
```javascript
// src/routes/questionRoute.js
const router = express.Router();

// Get questions
router.get('/questions',
  celebrate({
    query: Joi.object({
      clientId: Joi.number().integer(),
      category: Joi.string(),
      isApproved: Joi.boolean(),
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20)
    })
  }),
  async (req, res, next) => {
    try {
      const questions = await questionService.getAll(req.query);
      res.json({
        message: 'Questions retrieved successfully',
        data: questions.rows,
        totalEntries: questions.count,
        page: req.query.page,
        limit: req.query.limit
      });
    } catch (error) {
      next(error);
    }
  }
);

// Create question
router.post('/questions',
  celebrate({
    body: Joi.object({
      text: Joi.string().max(2000).required(),
      clientId: Joi.number().integer().required(),
      originalLanguage: Joi.string().default('en')
    })
  }),
  async (req, res, next) => {
    try {
      const questionData = {
        ...req.body,
        userId: req.auth.user.id
      };
      
      const question = await questionService.create(questionData);
      res.status(201).json({
        message: 'Question created successfully',
        data: question
      });
    } catch (error) {
      next(error);
    }
  }
);

// Update question (admin only)
router.put('/questions/:id',
  celebrate({
    params: Joi.object({
      id: Joi.number().integer().required()
    }),
    body: Joi.object({
      isApproved: Joi.boolean(),
      isDenied: Joi.boolean(),
      category: Joi.string(),
      categoryIcon: Joi.string()
    })
  }),
  async (req, res, next) => {
    try {
      const question = await questionService.update(req.params.id, req.body);
      res.json({
        message: 'Question updated successfully',
        data: question
      });
    } catch (error) {
      next(error);
    }
  }
);

export const securityConstraints = [
  {
    regex: /^\/questions$/,
    methods: ['POST'],
    accessLevels: ['user', 'admin']
  },
  {
    regex: /^\/questions\/\d+$/,
    methods: ['PUT', 'DELETE'],
    accessLevels: ['admin']
  }
];

export default { router, securityConstraints };
```

### 3. Answer Routes
```javascript
// src/routes/answerRoute.js
const router = express.Router();

// Get answers
router.get('/answers', async (req, res, next) => {
  try {
    const answers = await answerService.getAll(req.query);
    res.json({
      message: 'Answers retrieved successfully',
      data: answers
    });
  } catch (error) {
    next(error);
  }
});

// Create answer
router.post('/answers',
  celebrate({
    body: Joi.object({
      questionId: Joi.number().integer().required(),
      videoUrl: Joi.string().uri().required(),
      imageUrl: Joi.string().uri(),
      subtitles: Joi.string(),
      isDraft: Joi.boolean().default(false)
    })
  }),
  async (req, res, next) => {
    try {
      const answerData = {
        ...req.body,
        userId: req.auth.user.id,
        clientId: req.auth.user.clientId
      };
      
      const answer = await answerService.create(answerData);
      res.status(201).json({
        message: 'Answer created successfully',
        data: answer
      });
    } catch (error) {
      next(error);
    }
  }
);

// Vote on answer
router.post('/answers/:id/vote',
  celebrate({
    params: Joi.object({
      id: Joi.number().integer().required()
    }),
    body: Joi.object({
      value: Joi.number().integer().valid(1, -1).required()
    })
  }),
  async (req, res, next) => {
    try {
      const vote = await voteService.castVote({
        userId: req.auth.user.id,
        answerId: req.params.id,
        value: req.body.value
      });
      
      res.json({
        message: 'Vote cast successfully',
        data: vote
      });
    } catch (error) {
      next(error);
    }
  }
);

export const securityConstraints = [
  {
    regex: /^\/answers$/,
    methods: ['POST'],
    accessLevels: ['admin', 'moderator']
  },
  {
    regex: /^\/answers\/\d+\/vote$/,
    methods: ['POST'],
    accessLevels: ['user', 'admin']
  }
];

export default { router, securityConstraints };
```

## File Upload Routes

### File Management
```javascript
// src/routes/fileRoute.js
import multer from 'multer';

const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 500 * 1024 * 1024 }, // 500MB
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|mp4|mov|avi/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  }
});

// Upload video
router.post('/files/videos',
  upload.single('video'),
  async (req, res, next) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      const result = await fileService.processVideo(req.file, req.auth.user.clientId);
      
      res.json({
        message: 'Video uploaded successfully',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
);

// Upload image
router.post('/files/images',
  upload.single('image'),
  async (req, res, next) => {
    try {
      const result = await fileService.processImage(req.file, req.auth.user.clientId);
      
      res.json({
        message: 'Image uploaded successfully',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
);
```

## Admin Routes

### Administrative Endpoints
```javascript
// Admin-only routes with enhanced security
router.get('/admin/stats',
  requireAdmin,
  async (req, res, next) => {
    try {
      const stats = await statsService.getAdminStats(req.query);
      res.json({ data: stats });
    } catch (error) {
      next(error);
    }
  }
);

router.get('/admin/users',
  requireAdmin,
  celebrate({
    query: Joi.object({
      clientId: Joi.number().integer(),
      accessLevel: Joi.string(),
      enabled: Joi.boolean(),
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20)
    })
  }),
  async (req, res, next) => {
    try {
      const users = await userService.getAll(req.query);
      res.json({
        data: users.rows,
        totalEntries: users.count,
        page: req.query.page
      });
    } catch (error) {
      next(error);
    }
  }
);

// Middleware for admin access
const requireAdmin = (req, res, next) => {
  if (!['admin', 'super_admin'].includes(req.auth.user.accessLevel)) {
    return res.status(403).json({ error: 'Admin access required' });
  }
  next();
};
```

## Input Validation

### Celebrate/Joi Validation
```javascript
// Comprehensive validation schemas
const questionValidation = {
  create: celebrate({
    body: Joi.object({
      text: Joi.string().min(10).max(2000).required(),
      clientId: Joi.number().integer().positive().required(),
      originalLanguage: Joi.string().length(2).default('en'),
      category: Joi.string().valid(
        'transportation', 'housing', 'public-safety', 
        'environment', 'budget', 'health', 'education'
      )
    })
  }),
  
  update: celebrate({
    params: Joi.object({
      id: Joi.number().integer().positive().required()
    }),
    body: Joi.object({
      isApproved: Joi.boolean(),
      isDenied: Joi.boolean(),
      category: Joi.string(),
      categoryIcon: Joi.string()
    }).min(1)
  })
};
```

## Error Handling

### Global Error Handler
```javascript
// src/routes/index.js - Error handling middleware
app.use((err, req, res, next) => {
  if (err.joi) {
    // Validation error
    logger.error('Validation error', {
      message: err.joi.message,
      details: err.joi.details,
      data: req.body
    });

    return res.status(400).json({
      error: err.joi.message,
      details: err.joi.details
    });
  }

  // Use centralized error utility
  errorUtil.sendErrorResponse(res, err);
});
```

### Custom Error Responses
```javascript
// Standardized error responses
const sendErrorResponse = (res, error) => {
  const statusCode = error.statusCode || 500;
  const message = error.message || 'Internal server error';
  
  const response = {
    error: message,
    code: error.code || 'INTERNAL_ERROR',
    timestamp: new Date().toISOString()
  };

  if (process.env.NODE_ENV === 'development') {
    response.stack = error.stack;
  }

  logger.error('API Error', { error, statusCode });
  
  res.status(statusCode).json(response);
};
```

## API Documentation

### Health Check
```javascript
// Health check endpoint
router.get('/health_check', (req, res) => {
  console.log('health check ping');
  return res.status(200).send('OK');
});
```

### API Versioning
```javascript
// Version-specific routes
router.use('/v1', v1Routes);
router.use('/v2', v2Routes);

// Default to latest version
router.use('/', v2Routes);
```

## Rate Limiting

### Request Rate Limiting
```javascript
import rateLimit from 'express-rate-limit';

// General rate limiting
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});

// Strict rate limiting for sensitive endpoints
const strictLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 10,
  message: 'Rate limit exceeded for sensitive operation'
});

router.use('/sessions', strictLimiter);
router.use('/', generalLimiter);
```

## CORS Configuration

### Cross-Origin Resource Sharing
```javascript
import cors from 'cors';

const corsOptions = {
  origin: (origin, callback) => {
    const allowedOrigins = [
      'https://app.repd.us',
      'https://embed.repd.us',
      'https://admin.repd.us'
    ];
    
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
};

app.use(cors(corsOptions));
```

## Usage Examples

### Making API Requests
```javascript
// Client-side API usage
const apiClient = {
  baseURL: 'https://api.repd.us',
  
  async request(method, endpoint, data = null) {
    const config = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.getToken()}`
      }
    };
    
    if (data) {
      config.body = JSON.stringify(data);
    }
    
    const response = await fetch(`${this.baseURL}${endpoint}`, config);
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }
    
    return response.json();
  },
  
  // Convenience methods
  get(endpoint) { return this.request('GET', endpoint); },
  post(endpoint, data) { return this.request('POST', endpoint, data); },
  put(endpoint, data) { return this.request('PUT', endpoint, data); },
  delete(endpoint) { return this.request('DELETE', endpoint); }
};

// Usage examples
const questions = await apiClient.get('/questions?clientId=1');
const newQuestion = await apiClient.post('/questions', {
  text: 'What are the city hours?',
  clientId: 1
});
```

## Related Features
- [Authentication & Sessions](./01-authentication-sessions.md)
- [Users & Clients](./02-users-clients.md)
- [Questions](./03-questions.md)
- [Answers](./04-answers.md)
- [File Management](./09-file-management.md)

## Dependencies
- `express` - Web framework
- `celebrate` - Input validation
- `cors` - Cross-origin resource sharing
- `express-rate-limit` - Rate limiting
- `multer` - File upload handling
