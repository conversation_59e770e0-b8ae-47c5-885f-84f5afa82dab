# Questions

## Overview
The questions system allows users to submit questions to their local government or organization. Questions are automatically categorized, analyzed for sentiment, and can be translated into multiple languages. The system includes approval workflows and AI-powered enhancements.

## Key Components

### Files
- `src/models/questionModel.js` - Question database model
- `src/services/questionService.js` - Question business logic
- `src/routes/questionRoute.js` - Question API endpoints
- `src/validations/questionValidation.js` - Question input validation
- `src/helpers/ai.js` - AI integration for categorization and sentiment
- `src/prompts/` - AI prompt templates

### Database Model
- `id` - Primary key
- `text` - Question content (max 2000 chars, unique)
- `category` - Auto-generated category
- `categoryIcon` - Icon identifier for category
- `categorySummary` - AI-generated category summary
- `sentiment` - Sentiment score (1-3: happy, neutral, unhappy)
- `clientId` - Foreign key to clients table
- `userId` - Foreign key to users table
- `originalLanguage` - Language of original question
- `translations` - JSON object with translations
- `suggestedNotes` - AI-generated notes for responders
- `transcript` - Audio transcript if applicable
- `isApproved` - Admin approval status
- `isDenied` - Admin denial status
- `isAnswered` - Whether question has been answered
- `isShared` - Whether question is shared publicly

## Question Lifecycle

### 1. Question Submission
```javascript
// POST /questions
{
  "text": "What are the city's plans for improving public transportation?",
  "clientId": 123,
  "originalLanguage": "en"
}
```

### 2. AI Processing (Automatic)
After question creation, the system automatically:
- Generates sentiment analysis (1-3 scale)
- Creates category classification
- Generates category summary
- Creates suggested notes for responders

### 3. Admin Review
- Questions require approval before being answered
- Admins can approve, deny, or request modifications
- Approval workflow ensures quality control

### 4. Answer Assignment
- Approved questions can be answered by authorized users
- Questions marked as `isAnswered` when response is provided

## AI Integration

### Sentiment Analysis
```javascript
// Automatic sentiment scoring
const sentiment = await createSentiment(questionText);
// Returns: 1 (positive), 2 (neutral), or 3 (negative)
```

### Category Classification
- Questions automatically categorized into topics
- Categories include: Transportation, Housing, Public Safety, etc.
- Category icons assigned for visual representation

### Category Summary Generation
```javascript
// AI-generated summary for admin context
const categorySummary = await createCategorySummary(questionText);
// Returns: Brief summary of question topic and context
```

## Translation System

### Multi-Language Support
- Questions can be submitted in any supported language
- Automatic translation to other languages
- Original language preserved in `originalLanguage` field

### Supported Languages
- English (en)
- Spanish (es)
- French (fr)
- German (de)
- Chinese (zh)
- And more...

### Translation Storage
```javascript
// translations field structure
{
  "en": "What are the city's plans for transportation?",
  "es": "¿Cuáles son los planes de la ciudad para el transporte?",
  "fr": "Quels sont les plans de la ville pour les transports?"
}
```

## API Endpoints

### Question Management
- `POST /questions` - Submit new question
- `GET /questions` - List questions (with filters)
- `GET /questions/:id` - Get specific question
- `PUT /questions/:id` - Update question (admin only)
- `DELETE /questions/:id` - Delete question (admin only)

### Admin Operations
- `PUT /questions/:id/approve` - Approve question
- `PUT /questions/:id/deny` - Deny question
- `PUT /questions/:id/share` - Share question publicly

### Filtering and Search
```javascript
// GET /questions with query parameters
{
  "category": "transportation",
  "isApproved": true,
  "clientId": 123,
  "page": 1,
  "limit": 20
}
```

## Question Categories

### Standard Categories
- **Transportation** - Public transit, roads, parking
- **Housing** - Affordable housing, zoning, development
- **Public Safety** - Police, fire, emergency services
- **Environment** - Parks, sustainability, waste management
- **Budget** - City finances, taxes, spending
- **Health** - Public health, healthcare services
- **Education** - Schools, libraries, youth programs
- **Infrastructure** - Utilities, maintenance, construction

### Category Icons
Each category has an associated icon for UI display:
```javascript
categoryIcon: 'transportation-bus' // Icon identifier
```

## Validation Rules

### Question Text
- Required field
- Maximum 2000 characters
- Must be unique across client
- Profanity filtering applied

### Content Moderation
- Automatic screening for inappropriate content
- Manual review for flagged content
- Obscene and harmful words filtering

## Usage Examples

### Creating a Question
```javascript
import questionService from '../services/questionService.js';

const question = await questionService.create({
  text: "When will the new bike lanes be completed?",
  clientId: 1,
  userId: 123,
  originalLanguage: "en"
});
```

### Getting Questions with Filters
```javascript
const questions = await questionService.getAll({
  clientId: 1,
  isApproved: true,
  category: 'transportation',
  page: 1,
  limit: 10
});
```

## Error Handling

### Common Errors
- `409 Conflict` - Duplicate question text
- `422 Validation Error` - Invalid input data
- `403 Forbidden` - Insufficient permissions
- `400 Bad Request` - Missing required fields

### Validation Errors
```javascript
{
  "error": "Validation failed",
  "details": [
    {
      "field": "text",
      "message": "Question text is required"
    }
  ]
}
```

## Related Features
- [Answers](./04-answers.md)
- [AI Integration](./06-ai-integration.md)
- [Users & Clients](./02-users-clients.md)
- [Voting & Engagement](./05-voting-engagement.md)

## Dependencies
- `sequelize` - Database ORM
- `openai` - AI integration
- `celebrate` - Input validation
- Translation services for multi-language support
